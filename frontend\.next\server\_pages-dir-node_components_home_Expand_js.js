"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_home_Expand_js";
exports.ids = ["_pages-dir-node_components_home_Expand_js"];
exports.modules = {

/***/ "(pages-dir-node)/./components/home/<USER>":
/*!***********************************!*\
  !*** ./components/home/<USER>
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight!=!react-icons/fa6 */ \"(pages-dir-node)/__barrel_optimize__?names=FaArrowRight!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/navigation */ \"(pages-dir-node)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_react__WEBPACK_IMPORTED_MODULE_3__, swiper_modules__WEBPACK_IMPORTED_MODULE_5__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_7__]);\n([swiper_react__WEBPACK_IMPORTED_MODULE_3__, swiper_modules__WEBPACK_IMPORTED_MODULE_5__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unescaped-entities */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n// import { useInView } from \"react-intersection-observer\";\n\n\n\n\n\n\n\n// import { motion } from \"framer-motion\";\nconst ExpandPage = ()=>{\n    const [currentImage, setCurrentImage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // eslint-disable-next-line no-unused-vars\n    const [manualClick, setManualClick] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // const [ref1, inView1] = useInView({ threshold: 0.2 });\n    // const [ref2, inView2] = useInView({ threshold: 0.1 });\n    // const [ref3, inView3] = useInView({ threshold: 0.98 });\n    const [popertyCount, setPopertyCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [visibleSlides, setVisibleSlides] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // eslint-disable-next-line no-unused-vars\n    const [direction, setDirection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"right\"); // Track slide direction\n    const countryImages = {\n        India: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/india-new.webp`,\n        Thailand: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Thailand-new.webp`,\n        Indonesia: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Indonesia-new.webp`,\n        Colombia: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/colombiaaa.webp`,\n        Spain: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/spainnn.webp`,\n        Mexico: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/mexicooo.webp`,\n        Italy: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/italyyy-new.webp`,\n        Portugal: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/portugalll.webp`,\n        Brazil: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/brazilll.webp`,\n        USA: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/usaaa.webp`,\n        Japan: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/japannn.webp`,\n        Vietnam: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Vietnam-new.webp`,\n        France: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/franceee.webp`,\n        Australia: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/australiaaa.webp`,\n        Peru: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/peruuu.webp`\n    };\n    const CountryList = [\n        \"India\",\n        \"Thailand\",\n        \"Indonesia\",\n        \"Colombia\",\n        \"Spain\",\n        \"Mexico\",\n        \"Italy\",\n        \"Portugal\",\n        \"Brazil\",\n        \"USA\",\n        \"Japan\",\n        \"Vietnam\",\n        \"France\",\n        \"Australia\",\n        \"Peru\"\n    ];\n    const images = [\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Bookhostel.webp`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/phone-details-1.2.webp`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/phone-details-2.2.webp`\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExpandPage.useEffect\": ()=>{\n            const fetchPropertyCount = {\n                \"ExpandPage.useEffect.fetchPropertyCount\": async ()=>{\n                    try {\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_7__.getHomePagePropertyCountApi)({\n                            countries: CountryList\n                        });\n                        setPopertyCount(response?.data?.data?.propertyCounts || []);\n                    } catch (error) {\n                        console.error(\"Error fetching stay data:\", error);\n                    } finally{\n                    /* empty */ }\n                }\n            }[\"ExpandPage.useEffect.fetchPropertyCount\"];\n            if (!isFirstRender.current) {\n                fetchPropertyCount();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"ExpandPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExpandPage.useEffect\": ()=>{\n            const checkMobile = {\n                \"ExpandPage.useEffect.checkMobile\": ()=>{\n                    setIsMobile( false && 0);\n                }\n            }[\"ExpandPage.useEffect.checkMobile\"];\n            // Initial check\n            checkMobile();\n            // Add resize listener\n            if (false) {}\n        }\n    }[\"ExpandPage.useEffect\"], []);\n    // useEffect(() => {\n    //   if (!manualClick) {\n    //     if (inView3) setCurrentImage(2);\n    //     else if (inView2) setCurrentImage(1);\n    //     else if (inView1) setCurrentImage(0);\n    //   }\n    // }, [inView1, inView2, inView3, manualClick]);\n    const handleClick = (index)=>{\n        setCurrentImage(index);\n        setManualClick(true);\n    // setTimeout(() => setManualClick(false), 3500); // allow scroll update after 1.5s\n    };\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array(images.length).fill(false));\n    const [qrLoaded, setQrLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleImageLoad = (index)=>{\n        setImageLoaded((prev)=>{\n            const updated = [\n                ...prev\n            ];\n            updated[index] = true;\n            return updated;\n        });\n    };\n    // useEffect(() => {\n    //   const interval = setInterval(() => {\n    //     setCurrentImage((prev) => (prev + 1) % images.length); // Loop through images\n    //   }, 5000); // Change image every 3 seconds\n    //   return () => clearInterval(interval); // Cleanup interval\n    // }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative container z-0 sm:block hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"w-full bg-transparent md:pb-16 pb-2 backdrop-blur-sm hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full  absolute -top-44 lg:-top-60 \",\n                    children: isMobile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-semibold text-white font-manrope md:text-3xl sm:text-2xl text-xl mb-3\",\n                                children: [\n                                    \"Explore the\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mashiny font-normal text-primary-blue text-4xl md:text-5xl\",\n                                        children: [\n                                            \" \",\n                                            \"World\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                slidesPerView: \"auto\",\n                                spaceBetween: 15,\n                                autoplay: true,\n                                loop: true,\n                                modules: [\n                                    swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Autoplay,\n                                    swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Navigation\n                                ],\n                                // navigation={true}\n                                className: \"mySwiper myCustomSwiper\",\n                                autoHeight: true,\n                                onSlideChange: (swiper)=>{\n                                    // Get currently visible slides\n                                    const newVisibleSlides = swiper.slides.slice(swiper.activeIndex, swiper.activeIndex + 4) // Select 4 slides in view\n                                    .map((slide)=>slide.getAttribute(\"data-index\")); // Get index of each slide\n                                    setVisibleSlides(newVisibleSlides);\n                                },\n                                breakpoints: {\n                                    0: {\n                                        slidesPerView: 1.5,\n                                        spaceBetween: 20\n                                    },\n                                    640: {\n                                        slidesPerView: 2\n                                    },\n                                    768: {\n                                        slidesPerView: 3\n                                    },\n                                    1024: {\n                                        slidesPerView: 4\n                                    }\n                                },\n                                children: [\n                                    \"India\",\n                                    \"Thailand\",\n                                    \"Indonesia\",\n                                    \"Colombia\",\n                                    \"Spain\",\n                                    \"Mexico\",\n                                    \"Italy\",\n                                    \"Portugal\",\n                                    \"Brazil\",\n                                    \"USA\",\n                                    \"Japan\",\n                                    \"Vietnam\",\n                                    \"France\",\n                                    \"Australia\",\n                                    \"Peru\"\n                                ].map((country, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                        \"data-index\": index,\n                                        className: `${visibleSlides.indexOf(index.toString()) === 1 || visibleSlides.indexOf(index.toString()) === 3 ? \"mt-6 pb-10\" : \"mt-0\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden duration-300 ease-in-out group min-h-[228px] max-h-[268px] shadow-lg bg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                        href: `/tophostel?country=${country}`,\n                                                        prefetch: false,\n                                                        className: \"comman-tooltip\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            src: countryImages[country],\n                                                            width: 208,\n                                                            height: 362,\n                                                            alt: country,\n                                                            className: \"object-cover w-full h-full duration-300 ease-in-out opacity-100 group-hover:scale-105\",\n                                                            loading: \"lazy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute text-white top-[80%] font-manrope left-5 opacity-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm sm:text-base font-bold\",\n                                                                children: [\n                                                                    \"Hostel in \",\n                                                                    country\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            popertyCount?.[country] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-sm font-normal\",\n                                                                children: [\n                                                                    popertyCount[country],\n                                                                    \" Hostels\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 205,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between flex-col md:flex-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-white  md:text-3xl sm:text-2xl text-xl mb-5  relative z-10\",\n                                        children: [\n                                            \"Explore the\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl text-primary-blue\",\n                                                children: [\n                                                    \"World\",\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/exploreworld\",\n                                        prefetch: false,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-primary-blue hover:bg-teal-400 h-9 w-24 rounded-3xl text-sm font-semibold mt-5\",\n                                            children: \"See All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                    slidesPerView: \"auto\",\n                                    spaceBetween: 15,\n                                    autoplay: true,\n                                    loop: true,\n                                    modules: [\n                                        swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Autoplay,\n                                        swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Navigation\n                                    ],\n                                    // navigation={true}\n                                    className: \"mySwiper myCustomSwiper\",\n                                    autoHeight: true,\n                                    onSlideChange: (swiper)=>{\n                                        // Get currently visible slides\n                                        const newVisibleSlides = swiper.slides.slice(swiper.activeIndex, swiper.activeIndex + 4) // Select 4 slides in view\n                                        .map((slide)=>slide.getAttribute(\"data-index\")); // Get index of each slide\n                                        setVisibleSlides(newVisibleSlides);\n                                    },\n                                    breakpoints: {\n                                        0: {\n                                            slidesPerView: 1.5,\n                                            spaceBetween: 20\n                                        },\n                                        640: {\n                                            slidesPerView: 2\n                                        },\n                                        768: {\n                                            slidesPerView: 3\n                                        },\n                                        1024: {\n                                            slidesPerView: 4\n                                        }\n                                    },\n                                    children: [\n                                        \"India\",\n                                        \"Thailand\",\n                                        \"Indonesia\",\n                                        \"Colombia\",\n                                        \"Spain\",\n                                        \"Mexico\",\n                                        \"Italy\",\n                                        \"Portugal\",\n                                        \"Brazil\",\n                                        \"USA\",\n                                        \"Japan\",\n                                        \"Vietnam\",\n                                        \"France\",\n                                        \"Australia\",\n                                        \"Peru\"\n                                    ].map((country, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                            \"data-index\": index,\n                                            className: `${visibleSlides.indexOf(index.toString()) === 1 || visibleSlides.indexOf(index.toString()) === 3 ? \"mt-5 py-10\" : \"mt-0\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative overflow-hidden duration-300 ease-in-out group min-h-40 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: `/tophostel?country=${country}`,\n                                                            prefetch: false,\n                                                            className: \"comman-tooltip\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                src: countryImages[country],\n                                                                width: 208,\n                                                                height: 362,\n                                                                alt: country,\n                                                                className: \"object-cover w-full h-full duration-300 ease-in-out  group-hover:scale-105 bg-white opacity-100\",\n                                                                loading: \"lazy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute text-white top-[83%] font-manrope left-5 opacity-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm 2xl:text-xl font-bold\",\n                                                                    children: [\n                                                                        \"Hostel in \",\n                                                                        country\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                popertyCount?.[country] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-xs  2xl:text-sm font-normal\",\n                                                                    children: [\n                                                                        popertyCount[country],\n                                                                        \" Hostels\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white flex flex-col z-0 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"relative z-10 w-full lg:w-[40%] text-left lg:text-right font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl mb-4 md:mb-0 mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl\",\n                                children: \"Expand\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \",\n                            \"your\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl \",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex  justify-between flex-col-reverse md:flex-row md:justify-around lg:justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-[20%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1 left-[33%] w-40 h-40 bg-pink-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1 left-[10%] w-40 h-40 bg-yellow-300 rounded-full blur-2xl opacity-40\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 -left-2 w-36 h-36 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-[48%] left-[15%] w-36 h-36 bg-yellow-300 rounded-full blur-xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-[7%] right-[29%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-[23%] right-[4%] w-40 h-40 bg-pink-400 rounded-full blur-xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex flex-wrap justify-end items-start lg:justify-end  lg:items-end md:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // initial={{ x: -100, opacity: 0 }}\n                                        // whileInView={{ x: 0, opacity: 1 }}\n                                        // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.3 }}\n                                        // viewport={{ once: false }}\n                                        onClick: ()=>setCurrentImage(0),\n                                        className: `relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] ${currentImage === 0 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"sr-only\",\n                                                children: \"Main Page Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"sr-only\",\n                                                children: \"Section Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                children: \"Book your hostels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer line-clamp-4\",\n                                                children: \"Top Hostel Booking Made Easy with Mixdorm Explore and book hostels, dormitories, and budget hotels around the world — from solo backpacker spots to vibrant youth hostels in top cities. Whether you're looking for the cheapest accommodation, a cozy hostel stay, or social backpackers hostels, Mixdorm helps you find the perfect match. Start your hostel booking in India or across the globe with real traveler reviews, smart filters, and seamless online booking. From hidden gems to popular picks, discover hostel listings tailored for travelers like you.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                    className: \"text-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex  justify-start items-stretch gap-6 mt-8 w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.9 }}\n                                                // viewport={{ once: false }}\n                                                onClick: ()=>setCurrentImage(1),\n                                                className: `relative p-5 border rounded-xl w-full max-w-[280px]  ${currentImage === 1 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                        children: \"Noticeboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer\",\n                                                        children: \"AI-powered social noticeboard keeps you updated on trending stays, local events, and must-visit spots—helping you connect, explore, and experience more on your journey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.6 }}\n                                                // viewport={{ once: false }}\n                                                onClick: ()=>setCurrentImage(2),\n                                                className: `relative p-4 border rounded-xl max-w-[217px] ${currentImage === 2 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer whitespace-nowrap\",\n                                                        children: \"Events Spotlight\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer line-clamp-5\",\n                                                        children: \"Explore Top-Rated Hostels Around the World Discover the best hostels and dormitory stays offering fun events, social vibes, and unforgettable travel experiences. From budget-friendly hostels to lively backpackers' stays, find your perfect match for adventure, comfort, and connection.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full hidden md:flex flex-col justify-end items-start lg:items-end md:w-[90%] lg:w-[45%] mb-16 px-4 lg:px-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // initial={{ x: -100, opacity: 0 }}\n                                        // whileInView={{ x: 0, opacity: 1 }}\n                                        // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.3 }}\n                                        // viewport={{ once: false }}\n                                        // ref={ref1}\n                                        onClick: ()=>handleClick(0),\n                                        className: `relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] ${currentImage === 0 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"sr-only\",\n                                                children: \"Main Page Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 513,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"sr-only\",\n                                                children: \"Section Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 514,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                children: \"Book your hostels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 font-manrope cursor-pointer line-clamp-4\",\n                                                children: [\n                                                    \"Top Hostel Booking Made Easy with \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                        href: \"/owner/about-mixdorm\",\n                                                        className: \"text-primary-blue\",\n                                                        children: \"Mixdorm \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 51\n                                                    }, undefined),\n                                                    \"Explore and book hostels, dormitories, and budget hotels around the world — from solo backpacker spots to vibrant youth hostels in top cities. Whether you're looking for the cheapest accommodation, a cozy hostel stay, or social backpackers hostels, Mixdorm helps you find the perfect match. Start your hostel booking in India or across the globe with real traveler reviews, smart filters, and seamless online booking. From hidden gems to popular picks, discover hostel listings tailored for travelers like you.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                    className: \"text-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-start items-stretch gap-6 mt-8 w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.9 }}\n                                                // viewport={{ once: false }}\n                                                // ref={ref2}\n                                                onClick: ()=>handleClick(1),\n                                                className: `relative p-5 border rounded-xl w-full max-w-[330px] ${currentImage === 1 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                        children: \"Noticeboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 font-manrope cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                href: \"/meetbuddies\",\n                                                                className: \"text-primary-blue\",\n                                                                children: \"AI-powered social noticeboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            \" keeps you updated on trending stays, local events, and must-visit spots—helping you connect, explore, and experience more on your journey.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 550,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.6 }}\n                                                // viewport={{ once: false }}\n                                                // ref={ref3}\n                                                onClick: ()=>handleClick(2),\n                                                className: `relative p-4 border rounded-xl w-full sm:max-w-[217px] ${currentImage === 2 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                        children: \"Events Spotlight\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 font-manrope cursor-pointer line-clamp-5\",\n                                                        children: \"Explore Top-Rated Hostels Around the World Discover the best hostels and dormitory stays offering fun events, social vibes, and unforgettable travel experiences. From budget-friendly hostels to lively backpackers' stays, find your perfect match for adventure, comfort, and connection.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center w-full sm:w-[90%] md:w-[70%] lg:w-[30%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    // initial={{ y: 80, opacity: 0 }}\n                                    // whileInView={{ y: 0, opacity: 1 }}\n                                    // transition={{\n                                    //   type: \"spring\",\n                                    //   stiffness: 120,\n                                    //   damping: 12,\n                                    //   duration: 0.8,\n                                    //   delay: 0.2,\n                                    // }}\n                                    // viewport={{ once: false }}\n                                    className: \"relative w-full max-w-[327px] h-[670px] rounded-3xl p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Phone-image.webp`,\n                                                width: 327,\n                                                height: 670,\n                                                alt: \"Mobile UI Mockup\",\n                                                className: \"rounded-3xl w-full h-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute rounded-3xl top-2 left-3 w-[100%] md:w-[90%] max-w-[270px] overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `relative w-full rounded-3xl h-[660px] flex transition-transform duration-500 ease-in-out ${direction === \"right\" ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                                                    children: images.map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-hidden\",\n                                                            children: [\n                                                                !imageLoaded[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full max-h-[560px] h-[560px] bg-slate-200 animate-pulse rounded-3xl z-10 flex items-center justify-center absolute top-0 left-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                        className: \"text-white font-bold font-manrope text-center\",\n                                                                        children: \"MixDorm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    src: img,\n                                                                    width: 320,\n                                                                    height: 660,\n                                                                    alt: \"Mobile UI Mockup\",\n                                                                    onLoad: ()=>handleImageLoad(index),\n                                                                    className: `absolute rounded-3xl transition-transform duration-500 ${index === currentImage ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"}`\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 643,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex justify-start w-[30%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col justify-evenly \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            // initial={{ x: 100, opacity: 0 }}\n                                            // whileInView={{ x: 0, opacity: 1 }}\n                                            // transition={{ duration: 0.8, ease: \"easeOut\" }}\n                                            // viewport={{ once: false }}\n                                            className: \"pb-56\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-[15px] font-manrope text-justify\",\n                                                    children: [\n                                                        \"Find \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/tophostel?country=India\",\n                                                            className: \"text-primary-blue\",\n                                                            children: \"Top-Rated Hostels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 24\n                                                        }, undefined),\n                                                        \" Worldwide for Every Kind of Traveler Explore the best hostel stays across the globe — perfect for solo travelers, backpackers, digital nomads, and budget-conscious explorers. From vibrant social hostels in major cities to peaceful dorms in nature getaways, our curated \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/search\",\n                                                            className: \"text-primary-blue\",\n                                                            children: \" hostel listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 69\n                                                        }, undefined),\n                                                        \" offer unique stays on every continent.\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/search\",\n                                                            className: \"text-primary-blue\",\n                                                            children: \" Search hostels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \" by destination, book instantly, and connect with travelers worldwide. Start Your Hostel Adventure Now\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-primary-blue text-black py-2.5 px-5 rounded-full shadow-lg transition text-sm font-manrope font-semibold mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                        href: \"/exploreworld\",\n                                                        children: \"Explore World \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-10 text-center flex items-end justify-end w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200  shadow-xl shadow-black/40 p-4 rounded-lg inline-block w-[155px] h-[147px] relative bottom-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-black text-base font-manrope font-extrabold\",\n                                                        children: \"Scan Here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute h-[102px] w-[102px] left-7\",\n                                                        children: [\n                                                            !qrLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full bg-slate-200 animate-pulse rounded-lg absolute top-0 left-0 z-10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/QR.webp`,\n                                                                width: 100,\n                                                                height: 100,\n                                                                onLoad: ()=>setQrLoaded(true),\n                                                                alt: \"QR Code\",\n                                                                className: `rounded-lg transition-opacity duration-300 ${qrLoaded ? \"opacity-100\" : \"opacity-0\"}`,\n                                                                style: {\n                                                                    height: \"100%\",\n                                                                    width: \"100%\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 756,\n                                            columnNumber: 16\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 693,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExpandPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/home/<USER>");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaArrowRight!=!./node_modules/react-icons/fa6/index.mjs":
/*!*****************************************************************************************!*\
  !*** __barrel_optimize__?names=FaArrowRight!=!./node_modules/react-icons/fa6/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;