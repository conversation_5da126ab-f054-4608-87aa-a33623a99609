"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_pages-dir-browser_components_navbar_navbar_jsx",{

/***/ "(pages-dir-browser)/./components/navbar/navbar.jsx":
/*!**************************************!*\
  !*** ./components/navbar/navbar.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_MdOutlineNotificationsActive_react_icons_md__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineNotificationsActive!=!react-icons/md */ \"(pages-dir-browser)/__barrel_optimize__?names=MdOutlineNotificationsActive!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Grip,User,X!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=ChevronDown,Globe,Grip,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_HiMenuAlt3_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=HiMenuAlt3!=!react-icons/hi */ \"(pages-dir-browser)/__barrel_optimize__?names=HiMenuAlt3!=!./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,Drawer,Paper!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Box,Divider,Drawer,Paper!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/browserSetting */ \"(pages-dir-browser)/./utils/browserSetting.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../home/<USER>/ \"(pages-dir-browser)/./components/home/<USER>");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-browser)/./services/webflowServices.jsx\");\n/* harmony import */ var _popup_menuPopup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../popup/menuPopup */ \"(pages-dir-browser)/./components/popup/menuPopup.jsx\");\n/* harmony import */ var _popup_menuPopupMobile__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../popup/menuPopupMobile */ \"(pages-dir-browser)/./components/popup/menuPopupMobile.jsx\");\n/* harmony import */ var _toast_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../toast/toast */ \"(pages-dir-browser)/./components/toast/toast.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=FaRegUser!=!react-icons/fa */ \"(pages-dir-browser)/__barrel_optimize__?names=FaRegUser!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(pages-dir-browser)/./node_modules/framer-motion/dist/es/index.mjs\");\n/* eslint-disable react/no-unknown-property */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// import { Toaster, toast } from 'sonner'\n\n\n\n\n\n\n\n// import { PiHandCoinsFill } from \"react-icons/pi\";\n// import axios from \"axios\";\nconst CountryModal = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_model_countryModel_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../model/countryModel */ \"(pages-dir-browser)/./components/model/countryModel.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../model/countryModel\"\n        ]\n    },\n    ssr: false\n});\n_c = CountryModal;\nconst LoginPopup = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_popup_loginPopup_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../popup/loginPopup */ \"(pages-dir-browser)/./components/popup/loginPopup.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../popup/loginPopup\"\n        ]\n    },\n    ssr: false\n});\n_c1 = LoginPopup;\n// const MenuPopup = dynamic(() => import(\"../popup/menuPopup\"), { ssr: false });\nconst Noticeboard = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_model_noticeboard_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../model/noticeboard */ \"(pages-dir-browser)/./components/model/noticeboard.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../model/noticeboard\"\n        ]\n    },\n    ssr: false\n});\n_c2 = Noticeboard;\nconst MyProfile = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-browser_components_model_myProfile_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../model/myProfile */ \"(pages-dir-browser)/./components/model/myProfile.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../model/myProfile\"\n        ]\n    },\n    ssr: false\n});\n_c3 = MyProfile;\nconst Navbar = ()=>{\n    var _getItemLocalStorage_charAt, _getItemLocalStorage;\n    _s();\n    const [openCountryModal, setOpenCountryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenCountryModal = ()=>setOpenCountryModal(true);\n    const handleCloseCountryModal = ()=>setOpenCountryModal(false);\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoginPopup, setShowLoginPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveringTop, setHoveringTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpenMobile, setIsMenuOpenMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // eslint-disable-next-line no-unused-vars\n    const [hasToken, setHasToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // eslint-disable-next-line no-unused-vars\n    const [roles, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMyProfileOpen, setIsMyProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flagUrl, setFlagUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currencyCode, setCurrencyCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isPopupOpen, setIsPopupOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [timeLeft, setTimeLeft] = useState(2 * 24 * 60 * 60); // 2 days in seconds\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // const containerRef = useRef(null);\n    const toggleMyProfile = ()=>setIsMyProfileOpen(!isMyProfileOpen);\n    const toggleMenu = ()=>{\n        if (!token || role !== \"user\") {\n            _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                subText: \"You need to be Logged In\"\n            });\n        } else {\n            setIsMenuOpen(!isMenuOpen);\n        }\n    };\n    const toggleMenuMobile = ()=>{\n        if (!token || role !== \"user\") {\n            _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                subText: \"You need to be Logged In\"\n            });\n        } else {\n            setIsMenuOpenMobile(!isMenuOpenMobile);\n        }\n    };\n    // const toggleMenuMobile = () => setIsMenuOpenMobile(!isMenuOpenMobile);\n    const toggleLoginPopup = ()=>setShowLoginPopup(!showLoginPopup);\n    // const [user, setUser] = useState(null);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleRouteChange = {\n                \"Navbar.useEffect.handleRouteChange\": ()=>{\n                    if (isMenuOpen) toggleMenu(false);\n                    if (isMenuOpenMobile) toggleMenuMobile(false);\n                }\n            }[\"Navbar.useEffect.handleRouteChange\"];\n            router.events.on(\"routeChangeStart\", handleRouteChange);\n            // Cleanup\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    router.events.off(\"routeChangeStart\", handleRouteChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        isMenuOpen,\n        isMenuOpenMobile\n    ]);\n    const updateTokenState = ()=>{\n        const token = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"token\");\n        setHasToken(!!token);\n    };\n    const updateRoleState = ()=>{\n        const role = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"role\");\n        setRole(role);\n    };\n    const updateCountry = ()=>{\n        const flag = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCountryFlag\");\n        const code = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCurrencyCode\");\n        setFlagUrl(flag);\n        setCurrencyCode(code);\n        updateCountry2(flag, code);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            updateTokenState();\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            updateRoleState();\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleStorageChange = {\n                \"Navbar.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === \"token\") {\n                        updateTokenState();\n                    }\n                }\n            }[\"Navbar.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleStorageChange = {\n                \"Navbar.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === \"role\") {\n                        updateRoleState();\n                    }\n                }\n            }[\"Navbar.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const originalSetItem = localStorage.setItem;\n            localStorage.setItem = ({\n                \"Navbar.useEffect\": function(key) {\n                    const event = new Event(\"itemInserted\");\n                    originalSetItem.apply(this, arguments);\n                    if (key === \"token\") {\n                        window.dispatchEvent(event);\n                    }\n                }\n            })[\"Navbar.useEffect\"];\n            const handleItemInserted = {\n                \"Navbar.useEffect.handleItemInserted\": ()=>{\n                    updateTokenState();\n                }\n            }[\"Navbar.useEffect.handleItemInserted\"];\n            window.addEventListener(\"itemInserted\", handleItemInserted);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"itemInserted\", handleItemInserted);\n                    localStorage.setItem = originalSetItem;\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const originalSetItem = localStorage.setItem;\n            localStorage.setItem = ({\n                \"Navbar.useEffect\": function(key) {\n                    const event = new Event(\"itemInserted\");\n                    originalSetItem.apply(this, arguments);\n                    if (key === \"token\") {\n                        window.dispatchEvent(event);\n                    }\n                }\n            })[\"Navbar.useEffect\"];\n            const handleItemInserted = {\n                \"Navbar.useEffect.handleItemInserted\": ()=>{\n                    updateRoleState();\n                }\n            }[\"Navbar.useEffect.handleItemInserted\"];\n            window.addEventListener(\"itemInserted\", handleItemInserted);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"itemInserted\", handleItemInserted);\n                    localStorage.setItem = originalSetItem;\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 0);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            const handleMouseMove = {\n                \"Navbar.useEffect.handleMouseMove\": (event)=>{\n                    setHoveringTop(event.clientY < 85);\n                }\n            }[\"Navbar.useEffect.handleMouseMove\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            window.addEventListener(\"mousemove\", handleMouseMove);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"scroll\", handleScroll);\n                    window.removeEventListener(\"mousemove\", handleMouseMove);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const selectedCountryFlag = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCountryFlag\");\n            const selectedCurrencyCode = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCurrencyCode\");\n            if (selectedCountryFlag) {\n                const url = selectedCountryFlag;\n                setFlagUrl(url);\n                setCurrencyCode(selectedCurrencyCode);\n            }\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Notice Board Modal\n    const [openNoticeBoard, setOpenNoticeBoard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openNoticeBoardDetails, setOpenNoticeBoardDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenNoticeBoard = async ()=>{\n        if ((0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"token\") && role === \"user\") {\n            var _propertyCountResponse_data_data, _propertyCountResponse_data, _propertyCountResponse_data_data1, _propertyCountResponse_data1;\n            const propertyCountResponse = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_7__.getPropertyCountApi)();\n            if ((propertyCountResponse === null || propertyCountResponse === void 0 ? void 0 : (_propertyCountResponse_data = propertyCountResponse.data) === null || _propertyCountResponse_data === void 0 ? void 0 : (_propertyCountResponse_data_data = _propertyCountResponse_data.data) === null || _propertyCountResponse_data_data === void 0 ? void 0 : _propertyCountResponse_data_data.totalBooking) > 0) {\n                setOpenNoticeBoardDetails(true);\n            } else if ((propertyCountResponse === null || propertyCountResponse === void 0 ? void 0 : (_propertyCountResponse_data1 = propertyCountResponse.data) === null || _propertyCountResponse_data1 === void 0 ? void 0 : (_propertyCountResponse_data_data1 = _propertyCountResponse_data1.data) === null || _propertyCountResponse_data_data1 === void 0 ? void 0 : _propertyCountResponse_data_data1.totalBooking) === 0) {\n                setOpenNoticeBoard(true);\n            }\n        } else {\n            _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                subText: \"You need to be Logged In\"\n            });\n        }\n    };\n    const handleCloseNoticeBoard = ()=>{\n        setOpenNoticeBoard(false);\n        setOpenNoticeBoardDetails(false);\n    };\n    // Mobile Drawer\n    const [openDrawer, setOpenDrawer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleDrawer = (newOpen)=>()=>{\n            setOpenDrawer(newOpen);\n        };\n    const { updateCountry2, token, role } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_6__.useNavbar)();\n    // Update country when the component is mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const selectedCountryFlag = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCountryFlag\");\n            const selectedCurrencyCode = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCurrencyCode\");\n            if (selectedCountryFlag && selectedCurrencyCode) {\n                updateCountry2(selectedCountryFlag, selectedCurrencyCode);\n            }\n            setFlagUrl(selectedCountryFlag);\n            setCurrencyCode(selectedCurrencyCode);\n        }\n    }[\"Navbar.useEffect\"], [\n        updateCountry2\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    if (window.innerWidth < 768) return;\n                    if (window.scrollY > 900) {\n                        setIsScrolled(true);\n                    } else {\n                        setIsScrolled(false);\n                    }\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Auto show popup on load, close after 4 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            setIsPopupOpen(true);\n            const closeTimer = setTimeout({\n                \"Navbar.useEffect.closeTimer\": ()=>{\n                    setIsPopupOpen(false);\n                }\n            }[\"Navbar.useEffect.closeTimer\"], 4000);\n            return ({\n                \"Navbar.useEffect\": ()=>clearTimeout(closeTimer)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Countdown timer\n    // useEffect(() => {\n    //   if (!isPopupOpen) return;\n    //   const interval = setInterval(() => {\n    //     setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));\n    //   }, 1000);\n    //   return () => clearInterval(interval);\n    // }, [isPopupOpen]);\n    // Close on outside click\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (popupRef.current && !popupRef.current.contains(event.target)) {\n                    setIsPopupOpen(false);\n                }\n            }\n            if (isPopupOpen) {\n                document.addEventListener(\"mousedown\", handleClickOutside);\n            } else {\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n            }\n            return ({\n                \"Navbar.useEffect\": ()=>document.removeEventListener(\"mousedown\", handleClickOutside)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        isPopupOpen\n    ]);\n    // Format time left as hh:mm:ss\n    // const formatTime = (seconds) => {\n    //   const days = Math.floor(seconds / (24 * 3600));\n    //   const hrs = Math.floor((seconds % (24 * 3600)) / 3600);\n    //   const mins = Math.floor((seconds % 3600) / 60);\n    //   const secs = seconds % 60;\n    //   return `${days}d:${hrs.toString().padStart(2, \"0\")}:${mins\n    //     .toString()\n    //     .padStart(2, \"0\")}:${secs.toString().padStart(2, \"0\")}`;\n    // };\n    //   useEffect(() => {\n    //   const fetchUserData = async () => {\n    //     try {\n    //       const res = await axios.get(\"/api/user/me\", {\n    //         headers: {\n    //           Authorization: `Bearer ${token}`,\n    //         },\n    //       });\n    //       setUser(res.data); // Make sure the API returns { name: \"User Name\", ... }\n    //     } catch (error) {\n    //       console.error(\"Error fetching user data:\", error);\n    //     }\n    //   };\n    //   if (token) fetchUserData();\n    // }, [token]);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const isHomePage = router.pathname === \"/\";\n    const isTopHostelPage = router.pathname === \"/tophostel\" || router.pathname === \"/exploreworld\" || router.pathname === \"/featuredhostel\" || router.pathname === \"/travelactivity\" || router.pathname === \"/meetbuddies\" || router.pathname === \"/discover-event\" || router.pathname === \"/search\";\n    console.log(\"token\", token);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"w-full duration-300 ease-in-out sticky top-0 z-40 overflow-visible \".concat(scrolled && !hoveringTop ? \"-top-10\" : \"sticky top-0\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center px-5 py-2.5 bg-black w-full min-h-[44px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white xs:text-sm text-xs leading-tight text-center\",\n                                children: [\n                                    \"Get 1 Month Free\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/my-profile?section=membership\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-blue\",\n                                            children: \" Mix Premium \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Membership – Sign Up Now!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            !token || role !== \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"absolute inset-0 w-full h-full\",\n                                \"aria-label\": \"Login required to access Mix Premium Membership\",\n                                onClick: ()=>{\n                                    _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                                        subText: \"You need to be Logged In\"\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, undefined) : null\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full hidden lg:flex \".concat(isTopHostelPage ? \"absolute z-50 bg-black bg-opacity-10 shadow-md backdrop-blur-sm\" : isHomePage ? isScrolled ? \"bg-white shadow-md\" : \"bg-white bg-opacity-10 fixed top-10 z-50 backdrop-blur-sm\" : \"bg-transparent lg:bg-white shadow-md\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-50 flex items-start justify-between py-4 container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[45%] flex items-center gap-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"md:hidden block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                // href=\"#\"\n                                                rel: \"canonical\",\n                                                className: \"text-2xl font-manrope justify-center items-center font-bold cursor-pointer  duration-300 ease-in-out  \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                onClick: toggleDrawer(true),\n                                                prefetch: false,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiMenuAlt3_react_icons_hi__WEBPACK_IMPORTED_MODULE_12__.HiMenuAlt3, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/\",\n                                            rel: \"canonical\",\n                                            prefetch: false,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: isTopHostelPage ? \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Mixdorm-white-D.svg\") : isHomePage ? isScrolled ? \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/logo.svg\") : \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Mixdorm-white-D.svg\") : \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/logo.svg\"),\n                                                width: 155,\n                                                height: 40,\n                                                alt: \"Mixdorm\",\n                                                title: \"Mixdorm\",\n                                                className: \"max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out\",\n                                                loading: \"lazy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[55%] flex justify-end items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"flex items-center justify-center md:gap-x-5 gap-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"md:block hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/owner/list-your-hostel\",\n                                                    passHref: true,\n                                                    className: \"text-xs text-center font-manrope min-w-[140px] w-full block font-bold bg-primary-blue cursor-pointer rounded-9xl text-black duration-300 ease-in-out px-5 py-3\",\n                                                    prefetch: false,\n                                                    children: \"List Your Hostel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: !token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    // href=\"#\"\n                                                    className: \"text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                    onClick: toggleLoginPopup,\n                                                    prefetch: false,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.User, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: \"Traveller\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, undefined) : token && role === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    // href=\"#\"\n                                                    rel: \"canonical\",\n                                                    className: \"text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                    // onClick={toggleMyProfile} // Open MyProfile if token exists\n                                                    prefetch: false,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.User, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"name\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-sm font-manrope flex items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                    onClick: toggleLoginPopup,\n                                                    prefetch: false,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.User, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Traveller\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"text-sm font-manrope items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 flex \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                    onClick: handleOpenCountryModal,\n                                                    children: [\n                                                        flagUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            src: flagUrl,\n                                                            alt: \"Country Flag\",\n                                                            width: 20,\n                                                            height: 20,\n                                                            loading: \"lazy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Globe, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        currencyCode ? currencyCode : \"Country\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronDown, {\n                                                            size: 18\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        \"aria-label\": \"Mobile Menu\",\n                                                        className: \"sm:hidden text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                        onClick: toggleMenuMobile,\n                                                        prefetch: false,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Grip, {\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        \"aria-label\": \"Mobile Menu\",\n                                                        className: \"hidden sm:block text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out \".concat(isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"),\n                                                        onClick: toggleMenu,\n                                                        prefetch: false,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Grip, {\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, undefined),\n                    !isHomePage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex lg:hidden \".concat(isTopHostelPage ? \"absolute z-50 bg-black bg-opacity-10 shadow-md backdrop-blur-sm\" : \"bg-white border-y border-white\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-50 flex items-start justify-between py-4 container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[30%] flex items-center gap-x-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        rel: \"canonical\",\n                                        prefetch: false,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: isTopHostelPage ? \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Mixdorm-white-D.svg\") : \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/logo.svg\"),\n                                            width: 155,\n                                            height: 40,\n                                            alt: \"Content Ai\",\n                                            title: \"Content Ai\",\n                                            className: \"max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out\",\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[55%] flex justify-end items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"flex items-center justify-center md:gap-x-5 gap-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        // href=\"0\"\n                                                        rel: \"canonical\",\n                                                        className: \"text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl  duration-300 ease-in-out gap-x-2 flex \".concat(isTopHostelPage ? \"text-white\" : \"text-black\"),\n                                                        onClick: handleOpenNoticeBoard,\n                                                        \"aria-label\": \"Notification\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineNotificationsActive_react_icons_md__WEBPACK_IMPORTED_MODULE_14__.MdOutlineNotificationsActive, {\n                                                                size: 26,\n                                                                className: \"font-normal \".concat(isTopHostelPage ? \"text-white\" : \"text-black\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 animate-ping rounded-full bg-primary-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute flex items-center justify-center left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 rounded-full bg-primary-blue text-[10px] font-medium text-black text-center leading-none\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: !token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    // href=\"#\"\n                                                    className: \"text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 \".concat(isTopHostelPage ? \"text-white\" : \"text-black\"),\n                                                    onClick: toggleLoginPopup,\n                                                    \"aria-label\": \"User\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_15__.FaRegUser, {\n                                                            size: 20,\n                                                            className: \"font-bold \".concat(isTopHostelPage ? \"text-white\" : \"text-black\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 746,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: \"Traveller\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 23\n                                                }, undefined) : token && role === \"user\" ? // <button\n                                                //   // href=\"#\"\n                                                //   rel=\"canonical\"\n                                                //   className={`text-sm font-manrope flex items-center  justify-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 border-2 border-black-100 rounded-full h-7 w-7  ${isTopHostelPage ? \"text-white\" : \"text-black\"}`}\n                                                //   // onClick={toggleMyProfile} // Open MyProfile if token exists\n                                                // >\n                                                //   {/* <FaRegUser size={20} />  */}\n                                                //   A\n                                                // </button>\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"text-sm font-manrope flex items-center justify-center font-bold cursor-pointer duration-300 ease-in-out gap-x-2  rounded-full h-7 w-7  \".concat(isTopHostelPage ? \"bg-primary-blue text-black\" : \"bg-primary-blue text-black\"),\n                                                            onMouseEnter: ()=>setShowTooltip(true),\n                                                            onMouseLeave: ()=>setShowTooltip(false),\n                                                            children: ((_getItemLocalStorage = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"name\")) === null || _getItemLocalStorage === void 0 ? void 0 : (_getItemLocalStorage_charAt = _getItemLocalStorage.charAt(0)) === null || _getItemLocalStorage_charAt === void 0 ? void 0 : _getItemLocalStorage_charAt.toUpperCase()) || \"A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.AnimatePresence, {\n                                                            children: showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.motion.div, {\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    y: 5\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    y: 0\n                                                                },\n                                                                exit: {\n                                                                    opacity: 0,\n                                                                    y: 5\n                                                                },\n                                                                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 bg-white text-black text-xs rounded py-1 px-2 whitespace-nowrap\",\n                                                                children: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"name\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-sm font-manrope flex items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 \".concat(isTopHostelPage ? \"text-white\" : \"text-black\", \" \"),\n                                                    onClick: toggleLoginPopup,\n                                                    prefetch: false,\n                                                    \"aria-label\": \"User\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_15__.FaRegUser, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: \"Traveller\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"text-sm font-manrope items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 flex \".concat(isTopHostelPage ? \"text-white\" : \"text-black\"),\n                                                    onClick: handleOpenCountryModal,\n                                                    \"aria-label\": \"Flag\",\n                                                    children: flagUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-6 h-6 rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            src: flagUrl,\n                                                            alt: \"Country Flag\",\n                                                            fill: true,\n                                                            className: \"object-cover rounded-full\",\n                                                            sizes: \"24px\",\n                                                            loading: \"lazy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Globe, {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    \"aria-label\": \"Mobile Menu\",\n                                                    className: \"block text-sm font-manrope font-bold cursor-pointer   duration-300 ease-in-out \".concat(isTopHostelPage ? \"text-white\" : \"text-black\"),\n                                                    onClick: toggleMenuMobile,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Grip, {\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 686,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Drawer, {\n                open: openDrawer,\n                onClose: toggleDrawer(false),\n                className: \"nav-bar-humburger fadeInLeft animated\",\n                anchor: \"left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                    sx: {\n                        width: 346\n                    },\n                    role: \"presentation\",\n                    borderRadius: 10,\n                    onClick: toggleDrawer(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Paper, {\n                        sx: {\n                            width: 326,\n                            background: \"#fff\",\n                            borderRadius: \"0 0 10px 10px\",\n                            height: \"100vh\",\n                            elevation: 0\n                        },\n                        elevation: 0,\n                        borderRadius: 10,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                rel: \"canonical\",\n                                className: \"p-4 block\",\n                                prefetch: false,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/logo.svg\"),\n                                    width: 155,\n                                    height: 40,\n                                    alt: \"Mixdorm\",\n                                    title: \"Mixdorm\",\n                                    className: \"max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out\",\n                                    loading: \"lazy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 883,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"absolute top-4 right-4\",\n                                onClick: toggleDrawer(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.X, {\n                                    size: 22\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 899,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Divider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 905,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"\",\n                                        className: \"text-sm sm:text-base flex items-center gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Globe, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \" Select Currency\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center gap-x-2 text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out\",\n                                        onClick: handleOpenCountryModal,\n                                        children: [\n                                            flagUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: flagUrl,\n                                                alt: \"Country Flag\",\n                                                width: 20,\n                                                height: 20,\n                                                loading: \"lazy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Globe, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            currencyCode ? currencyCode : \"Country\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__.ChevronDown, {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 906,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/owner/hostel-login\",\n                                    rel: \"canonical\",\n                                    className: \"block px-5 py-3 text-center font-manrope text-base font-bold bg-primary-blue cursor-pointer rounded-4xl text-black duration-300 ease-in-out\",\n                                    prefetch: false,\n                                    children: \"List Your Hostel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 950,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 872,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                    lineNumber: 866,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 860,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Noticeboard, {\n                close: handleCloseNoticeBoard,\n                open: openNoticeBoard,\n                openNoticeBoardDetails: openNoticeBoardDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 964,\n                columnNumber: 7\n            }, undefined),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_popup_menuPopup__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isMenuOpen,\n                toggleMenu: toggleMenu,\n                updateTokenState: updateTokenState,\n                toggleLoginPopup: toggleLoginPopup,\n                updateRoleState: updateRoleState\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 970,\n                columnNumber: 9\n            }, undefined),\n            isMenuOpenMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_popup_menuPopupMobile__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isMenuOpenMobile,\n                toggleMenu: toggleMenuMobile,\n                updateTokenState: updateTokenState,\n                toggleLoginPopup: toggleLoginPopup,\n                updateRoleState: updateRoleState\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 979,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MyProfile, {\n                isMenuOpen: isMyProfileOpen,\n                toggleMenu: toggleMyProfile,\n                updateTokenState: updateTokenState,\n                updateRoleState: updateRoleState\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 988,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginPopup, {\n                isOpen: showLoginPopup,\n                onClose: toggleLoginPopup\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 994,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CountryModal, {\n                openCountryModal: openCountryModal,\n                handleCloseCountryModal: handleCloseCountryModal,\n                updateCountry: updateCountry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 996,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Navbar, \"+MSWfmfKsaol9/lLtNdRZ/i0D+s=\", false, function() {\n    return [\n        _home_navbarContext__WEBPACK_IMPORTED_MODULE_6__.useNavbar,\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter\n    ];\n});\n_c4 = Navbar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c6 = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(_c5 = ()=>Promise.resolve(Navbar), {\n    ssr: false\n}));\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"CountryModal\");\n$RefreshReg$(_c1, \"LoginPopup\");\n$RefreshReg$(_c2, \"Noticeboard\");\n$RefreshReg$(_c3, \"MyProfile\");\n$RefreshReg$(_c4, \"Navbar\");\n$RefreshReg$(_c5, \"%default%$dynamic\");\n$RefreshReg$(_c6, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/navbar/navbar.jsx\n"));

/***/ })

});