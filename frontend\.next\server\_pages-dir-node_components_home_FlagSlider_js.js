"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_home_FlagSlider_js";
exports.ids = ["_pages-dir-node_components_home_FlagSlider_js"];
exports.modules = {

/***/ "(pages-dir-node)/./components/home/<USER>":
/*!***************************************!*\
  !*** ./components/home/<USER>
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/css */ \"(pages-dir-node)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(swiper_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/css/pagination */ \"(pages-dir-node)/./node_modules/swiper/modules/pagination.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(swiper_css_pagination__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/navigation */ \"(pages-dir-node)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\n/* harmony import */ var world_countries__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! world-countries */ \"world-countries\");\n/* harmony import */ var world_countries__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(world_countries__WEBPACK_IMPORTED_MODULE_10__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_react__WEBPACK_IMPORTED_MODULE_2__, swiper_modules__WEBPACK_IMPORTED_MODULE_3__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_9__]);\n([swiper_react__WEBPACK_IMPORTED_MODULE_2__, swiper_modules__WEBPACK_IMPORTED_MODULE_3__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// import { Pagination, Navigation } from 'swiper/modules';\n\n\n\n\n\n\n\n// import { motion } from \"framer-motion\";\nconst FlagSlider = ()=>{\n    const swiperRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [flags, setFlags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [popertyCount, setPopertyCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const getZIndex = (i)=>{\n        const distance = Math.abs(i - activeIndex);\n        if (distance === 0) return 30;\n        if (distance === 1) return 20;\n        return 10;\n    };\n    const getScale = (i)=>{\n        const distance = Math.abs(i - activeIndex);\n        if (distance === 0) return \"scale-[1.2]\";\n        if (distance === 1 || i == 14) return \"scale-110\";\n        return \"scale-100\";\n    };\n    // const [coverflowEffect, setCoverflowEffect] = useState({\n    //   rotate: 30,\n    //   stretch: 0,\n    //   depth: 300,\n    //   modifier: 1,\n    //   slideShadows: false,\n    // });\n    // useEffect(() => {\n    //   const updateEffect = () => {\n    //     const isMobile = window.innerWidth < 520;\n    //     setCoverflowEffect({\n    //       rotate: isMobile ? 50 : 30,\n    //       stretch: 0,\n    //       depth: isMobile ? 100 : 300,\n    //       modifier: isMobile ? 1 : 1,\n    //       slideShadows: false,\n    //     });\n    //   };\n    //   updateEffect(); // initial\n    //   window.addEventListener(\"resize\", updateEffect);\n    //   return () => window.removeEventListener(\"resize\", updateEffect);\n    // }, []);\n    const countryImages = {\n        India: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/india-new.webp`,\n        Thailand: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Thailand-new.webp`,\n        Indonesia: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Indonesia-new.webp`,\n        Colombia: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/colombiaaa.webp`,\n        Spain: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/spainnn.webp`,\n        Mexico: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/mexicooo.webp`,\n        Italy: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/italyyy-new.webp`,\n        Portugal: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/portugalll.webp`,\n        Brazil: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/brazilll.webp`,\n        USA: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/usaaa.webp`,\n        Japan: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/japannn.webp`,\n        Vietnam: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Vietnam-new.webp`,\n        France: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/franceee.webp`,\n        Australia: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/australiaaa.webp`,\n        Peru: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/peruuu.webp`\n    };\n    const CountryList = [\n        \"India\",\n        \"Thailand\",\n        \"Indonesia\",\n        \"Colombia\",\n        \"Spain\",\n        \"Mexico\",\n        \"Italy\",\n        \"Portugal\",\n        \"Brazil\",\n        \"USA\",\n        \"Japan\",\n        \"Vietnam\",\n        \"France\",\n        \"Australia\",\n        \"Peru\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagSlider.useEffect\": ()=>{\n            const fetchPropertyCount = {\n                \"FlagSlider.useEffect.fetchPropertyCount\": async ()=>{\n                    try {\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_9__.getHomePagePropertyCountApi)({\n                            countries: CountryList\n                        });\n                        setPopertyCount(response?.data?.data?.propertyCounts || []);\n                    } catch (error) {\n                        console.error(\"Error fetching stay data:\", error);\n                    } finally{\n                    /* empty */ }\n                }\n            }[\"FlagSlider.useEffect.fetchPropertyCount\"];\n            if (!isFirstRender.current) {\n                fetchPropertyCount();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"FlagSlider.useEffect\"], []);\n    const countriesForFlag = [\n        \"India\",\n        \"Thailand\",\n        \"Indonesia\",\n        \"Colombia\",\n        \"Spain\",\n        \"Mexico\",\n        \"Italy\",\n        \"Portugal\",\n        \"Brazil\",\n        \"United States\",\n        \"Japan\",\n        \"Vietnam\",\n        \"France\",\n        \"Australia\",\n        \"Peru\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagSlider.useEffect\": ()=>{\n            const fetchFlags = {\n                \"FlagSlider.useEffect.fetchFlags\": async ()=>{\n                    try {\n                        let data = world_countries__WEBPACK_IMPORTED_MODULE_10___default().filter({\n                            \"FlagSlider.useEffect.fetchFlags.data\": (country)=>{\n                                const commonName = country.name.common;\n                                return countriesForFlag.includes(commonName);\n                            }\n                        }[\"FlagSlider.useEffect.fetchFlags.data\"]).map({\n                            \"FlagSlider.useEffect.fetchFlags.data\": (country)=>{\n                                const name = country.name.common === \"United States\" ? \"USA\" : country.name.common;\n                                return {\n                                    id: country.cca3,\n                                    name,\n                                    flagImg: `https://flagcdn.com/w320/${country.cca2.toLowerCase()}.png`,\n                                    backgroundImg: countryImages[name] || \"\"\n                                };\n                            }\n                        }[\"FlagSlider.useEffect.fetchFlags.data\"]);\n                        // Sort data to match countriesForFlag order\n                        data.sort({\n                            \"FlagSlider.useEffect.fetchFlags\": (a, b)=>{\n                                const idxA = countriesForFlag.indexOf(a.name === \"USA\" ? \"United States\" : a.name);\n                                const idxB = countriesForFlag.indexOf(b.name === \"USA\" ? \"United States\" : b.name);\n                                return idxA - idxB;\n                            }\n                        }[\"FlagSlider.useEffect.fetchFlags\"]);\n                        // Preload flag images before showing them\n                        await Promise.all(data.map({\n                            \"FlagSlider.useEffect.fetchFlags\": (item)=>new Promise({\n                                    \"FlagSlider.useEffect.fetchFlags\": (resolve)=>{\n                                        const img = new window.Image();\n                                        img.src = item.flagImg;\n                                        img.onload = resolve;\n                                        img.onerror = resolve;\n                                    }\n                                }[\"FlagSlider.useEffect.fetchFlags\"])\n                        }[\"FlagSlider.useEffect.fetchFlags\"]));\n                        setFlags(data);\n                        setIsLoading(false); // ✅ Set loading to false after all done\n                    } catch (error) {\n                        console.error(\"Error fetching flags:\", error);\n                        setIsLoading(false); // ✅ Stop loading on error as well\n                    }\n                }\n            }[\"FlagSlider.useEffect.fetchFlags\"];\n            fetchFlags();\n        }\n    }[\"FlagSlider.useEffect\"], [\n        (world_countries__WEBPACK_IMPORTED_MODULE_10___default()),\n        countriesForFlag,\n        countryImages\n    ]);\n    // const SkeletonItem = () => (\n    //   <div className=\"flex flex-col items-center animate-pulse opacity-30\">\n    //     <div className=\"rounded-full bg-slate-200 xs:min-w-12 xs:min-h-12 xs:w-10 xs:h-10 min-w-10 min-h-10 w-10 h-10 mb-1\"></div>\n    //     <div className=\"w-12 h-3 bg-slate-200 rounded\"></div>\n    //   </div>\n    // );\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlagSlider.useEffect\": ()=>{\n            const mediaQuery = window.matchMedia(\"(min-width: 768px)\");\n            setIsDesktop(mediaQuery.matches);\n            setIsReady(true); // prevent rendering until screen size is detected\n        }\n    }[\"FlagSlider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative container z-10 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container w-full xs:pt-2 xs:px-2 px-0 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex justify-end items-center gap-2 mb-4 mr-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>swiperRef.current?.slidePrev(),\n                            className: \"p-2 rounded-full bg-white/20 backdrop-blur-sm shadow-md hover:bg-white/30 transition-colors duration-200\",\n                            \"aria-label\": \"Previous slide\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-4 w-4 text-white\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 19l-7-7 7-7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>swiperRef.current?.slideNext(),\n                            className: \"p-2 rounded-full bg-white/20 backdrop-blur-sm shadow-md hover:bg-white/30 transition-colors duration-200\",\n                            \"aria-label\": \"Next slide\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-4 w-4 text-white\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 5l7 7-7 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_2__.Swiper, {\n                    onSwiper: (swiper)=>swiperRef.current = swiper,\n                    onSlideChange: (swiper)=>setActiveIndex(swiper.realIndex),\n                    slidesPerView: 5,\n                    breakpoints: {\n                        0: {\n                            slidesPerView: 3\n                        },\n                        480: {\n                            slidesPerView: 3\n                        },\n                        640: {\n                            slidesPerView: 3\n                        },\n                        768: {\n                            slidesPerView: 5\n                        },\n                        1024: {\n                            slidesPerView: 5\n                        },\n                        1280: {\n                            slidesPerView: 5\n                        }\n                    },\n                    centeredSlides: true,\n                    watchSlidesProgress: true,\n                    spaceBetween: 0,\n                    pagination: false,\n                    navigation: false,\n                    loop: true,\n                    // autoplay={\n                    //   window.innerWidth < 640\n                    //     ? {\n                    //         delay: 3000,\n                    //         disableOnInteraction: false,\n                    //       }\n                    //     : false\n                    // }\n                    modules: [\n                        swiper_modules__WEBPACK_IMPORTED_MODULE_3__.Autoplay\n                    ],\n                    className: \"mySwiper w-full mx-auto pt-10 2xl:pb-20 sm:pb-10 pb-5 relative z-20 md:px-0 sm:px-[11px] xs:px-[8px] px-[6px] overflow-hidden\",\n                    children: isLoading ? [\n                        ...Array(5)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_2__.SwiperSlide, {\n                            className: `transition-all duration-300 transform-gpu bg-slate-200 animate-pulse ${getScale(index)} relative`,\n                            style: {\n                                zIndex: getZIndex(index)\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-[400px] rounded-lg bg-slate-200 animate-pulse relative flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-white font-bold font-manrope text-center mt-8\",\n                                        children: \"MixDorm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                        lineNumber: 373,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 w-full bg-gradient-to-t from-black/40 to-transparent p-4 rounded-b-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2 h-4 bg-slate-200 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                                lineNumber: 377,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/3 h-3 bg-slate-200 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                lineNumber: 372,\n                                columnNumber: 19\n                            }, undefined)\n                        }, `skeleton-${index}`, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                            lineNumber: 365,\n                            columnNumber: 17\n                        }, undefined)) : isReady && flags.map((country, index)=>{\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_2__.SwiperSlide, {\n                            className: `transition-all duration-300 transform-gpu ${getScale(index)} relative`,\n                            style: {\n                                zIndex: getZIndex(index),\n                                width: \"auto\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                className: \"h-full w-full z-30\",\n                                href: `/tophostel?country=${country.name}`,\n                                prefetch: false,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden z-30 shadow-lg hover:shadow-primary-blue/40 transition-transform duration-300 relative rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                            src: country.backgroundImg,\n                                            width: 300,\n                                            height: 400,\n                                            alt: \"Country\",\n                                            quality: 90,\n                                            sizes: \"(max-width: 480px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 50vw, 300px\",\n                                            ...isDesktop ? {\n                                                priority: true\n                                            } : {\n                                                loading: \"lazy\"\n                                            },\n                                            className: `object-cover w-full h-full rounded-lg ${isLoading ? \"bg-slate-200 animate-pulse\" : \"bg-slate-200\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                            lineNumber: 399,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 rounded-lg bg-gradient-to-t xs:from-black/70 from-black/40 to-transparent text-white sm:p-4 p-2 w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"sm:text-lg text-sm xs:font-semibold font-medium hidden md:block\",\n                                                    children: [\n                                                        \"Hostel in \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                            className: \"md:hidden block\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        \" \",\n                                                        country.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"sm:text-lg text-sm xs:font-semibold font-medium block md:hidden\",\n                                                    children: country.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                popertyCount?.[country.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs 2xl:text-sm font-normal\",\n                                                    children: popertyCount?.[country.name] ? `${popertyCount[country.name]} Hostels` : \"\\u00A0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                            lineNumber: 415,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                    lineNumber: 398,\n                                    columnNumber: 23\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                                lineNumber: 393,\n                                columnNumber: 21\n                            }, undefined)\n                        }, country.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                            lineNumber: 386,\n                            columnNumber: 19\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\FlagSlider.js\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlagSlider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/home/<USER>");

/***/ })

};
;