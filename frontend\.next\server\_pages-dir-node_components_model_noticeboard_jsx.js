"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_model_noticeboard_jsx";
exports.ids = ["_pages-dir-node_components_model_noticeboard_jsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/model/noticeBoardDetail.jsx":
/*!************************************************!*\
  !*** ./components/model/noticeBoardDetail.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ChevronRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Backdrop,Fade,Modal!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/browserSetting */ \"(pages-dir-node)/./utils/browserSetting.jsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_FaCaretUp_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaCaretUp!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaCaretUp!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// import { BiSolidHeartCircle } from \"react-icons/bi\";\n// import { MdOutlineChat } from \"react-icons/md\";\n// import { FaCalendarDays } from \"react-icons/fa6\";\n// import { PiMapPinLineFill } from \"react-icons/pi\";\n// import { IoFilterSharp } from \"react-icons/io5\";\n\n// import zIndex from \"@mui/material/styles/zIndex\";\n// const notices = [\n//   {\n//     title: \"Notice 1\",\n//     text: \"Mad monkey updated new roof top cafe with edm vibes 5 min ago\",\n//   },\n//   {\n//     title: \"Notice 2\",\n//     text: \"Mad monkey updated new roof top cafe with edm vibes 5 min ago\",\n//   },\n//   {\n//     title: \"Notice 3\",\n//     text: \"Mad monkey updated new roof top cafe with edm vibes 5 min ago\",\n//   },\n// ];\nconst NoticeBoardDetail = ({ open, close })=>{\n    const style = {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"70%\",\n        transform: \"translate(-50%, -50%)\",\n        width: \"100%\",\n        bgcolor: \"background.paper\",\n        boxShadow: 24,\n        borderRadius: \"20px\"\n    };\n    // eslint-disable-next-line no-unused-vars\n    const [age, setAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // eslint-disable-next-line no-unused-vars\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // eslint-disable-next-line no-unused-vars\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const handleOpenDetail = ()=>{\n        router.push(\"/noticeboard-detail\");\n        close(); // Close the main modal\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoticeBoardDetail.useEffect\": ()=>{\n            const fetchNotices = {\n                \"NoticeBoardDetail.useEffect.fetchNotices\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.getNoticeApi)();\n                        setNotices(response?.data?.data);\n                    } catch (error) {\n                        console.error(\"Error fetching payment data:\", error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"NoticeBoardDetail.useEffect.fetchNotices\"];\n            if (!isFirstRender.current && (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.getItemLocalStorage)(\"token\") && open) {\n                fetchNotices();\n            } else if (open) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Please Login !!!\");\n                close();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"NoticeBoardDetail.useEffect\"], [\n        open\n    ]);\n    // eslint-disable-next-line no-unused-vars\n    const handleChange = (event)=>{\n        setAge(event.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n            \"aria-labelledby\": \"transition-modal-title\",\n            \"aria-describedby\": \"transition-modal-description\",\n            open: open,\n            onClose: close,\n            closeAfterTransition: true,\n            slots: {\n                backdrop: _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__.Backdrop\n            },\n            slotProps: {\n                backdrop: {\n                    timeout: 500\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__.Fade, {\n                in: open,\n                sx: style,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl max-w-[450px] md:w-auto xs:w-[320px] w-[280px] lg:end-[10%] end-[5%] lg:mt-28 mt-24 float-right relative z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCaretUp_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaCaretUp, {\n                                    className: \"xs:top-[-25px] top-[-22px] absolute lg:start-[40%] md:start-[70%] xs:start-[60%] start-[52%]\",\n                                    size: 40,\n                                    color: \"#40E0D0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center pe-4 border-b bg-[#40E0D0] rounded-t-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"sm:text-[27px] text-xl leading-none rounded-t-2xl text-center font-bold text-black p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#fff]\",\n                                                    children: \"Notice\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"board\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleOpenDetail,\n                                            className: \"bg-[#fff] xs:text-sm text-xs font-semibold rounded-lg border border-white hover:border-black transition-all flex items-center justify-center py-1 ps-2 pe-1\",\n                                            children: [\n                                                \"View All \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__.ChevronRight, {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 28\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"xs:pb-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-around\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Mate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-semibold text-gray-400 text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Priyanka\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- You got your first like!\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-evenly\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Event\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Mad Monkey\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- Created New Event - Club Dance\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Creator\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Mad Monkey - Mix\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-[#56edde]\",\n                                                                            children: \"Creator \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Sent offer request to Ayush Jain\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-around\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Mate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-semibold text-gray-400 text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Priyanka\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- You got your first like!\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-evenly\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Event\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Mad Monkey\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- Created New Event - Club Dance\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoticeBoardDetail);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/model/noticeBoardDetail.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/model/noticeboard.jsx":
/*!******************************************!*\
  !*** ./components/model/noticeboard.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Backdrop,Fade,Modal!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./noticeBoardDetail */ \"(pages-dir-node)/./components/model/noticeBoardDetail.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([_noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Noticeboard = ({ close, open, openNoticeBoardDetails })=>{\n    const style = {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"50%\",\n        transform: \"translate(-50%, -50%)\",\n        maxWidth: 384,\n        bgcolor: \"background.paper\",\n        boxShadow: 24,\n        borderRadius: \"20px\",\n        p: 4\n    };\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [openBoardDetail, setOpenBoardDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenBoardDetail = ()=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Please Do Atleast One Booking First.\");\n        router.push(\"/\");\n        close(); // Close the main modal\n    };\n    const handleCloseBoardDetail = ()=>{\n        setOpenBoardDetail(false);\n        close(); // Close the main modal when nested modal closes\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Noticeboard.useEffect\": ()=>{\n            if (openNoticeBoardDetails) {\n                setOpenBoardDetail(true);\n            }\n        }\n    }[\"Noticeboard.useEffect\"], [\n        openNoticeBoardDetails\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                open: open,\n                onClose: close,\n                \"aria-labelledby\": \"child-modal-title\",\n                \"aria-describedby\": \"child-modal-description\",\n                closeAfterTransition: true,\n                slots: {\n                    backdrop: _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__.Backdrop\n                },\n                slotProps: {\n                    backdrop: {\n                        timeout: 500\n                    }\n                },\n                className: \"bg-black/70\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__.Fade, {\n                    in: open,\n                    sx: style,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl max-w-[384px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-[27px] leading-none text-center font-bold text-gray-800 border-b p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-blue\",\n                                        children: \"Notice\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"board\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center pt-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/noticeboard_img.png`,\n                                        alt: \"Noticeboard Image\",\n                                        width: 245,\n                                        height: 151,\n                                        className: \"max-w-full h-auto mb-6 block mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-center text-[#888888] mb-6\",\n                                        children: \"Please Book Your Stay To Visit Noticeboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOpenBoardDetail,\n                                        className: \"bg-[#40E0D0] text-sm font-semibold w-full py-4 rounded-3xl hover:bg-sky-blue-750 hover:text-white transition-all\",\n                                        children: \"Go To Booking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                open: openBoardDetail,\n                close: handleCloseBoardDetail\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Noticeboard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/model/noticeboard.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js":
/*!**************************************************************************************************!*\
  !*** __barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "(pages-dir-node)/./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ChevronRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*****************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaCaretUp!=!./node_modules/react-icons/fa/index.mjs":
/*!*************************************************************************************!*\
  !*** __barrel_optimize__?names=FaCaretUp!=!./node_modules/react-icons/fa/index.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;