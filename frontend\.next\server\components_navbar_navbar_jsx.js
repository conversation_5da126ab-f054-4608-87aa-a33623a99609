"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_navbar_navbar_jsx";
exports.ids = ["components_navbar_navbar_jsx"];
exports.modules = {

/***/ "./components/navbar/navbar.jsx":
/*!**************************************!*\
  !*** ./components/navbar/navbar.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_MdOutlineNotificationsActive_react_icons_md__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineNotificationsActive!=!react-icons/md */ \"__barrel_optimize__?names=MdOutlineNotificationsActive!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe,Grip,User,X!=!lucide-react */ \"__barrel_optimize__?names=ChevronDown,Globe,Grip,User,X!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_HiMenuAlt3_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=HiMenuAlt3!=!react-icons/hi */ \"__barrel_optimize__?names=HiMenuAlt3!=!./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Divider,Drawer,Paper!=!@mui/material */ \"__barrel_optimize__?names=Box,Divider,Drawer,Paper!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/webflowServices */ \"./services/webflowServices.jsx\");\n/* harmony import */ var _popup_menuPopup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../popup/menuPopup */ \"./components/popup/menuPopup.jsx\");\n/* harmony import */ var _popup_menuPopupMobile__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../popup/menuPopupMobile */ \"./components/popup/menuPopupMobile.jsx\");\n/* harmony import */ var _toast_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../toast/toast */ \"./components/toast/toast.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=FaRegUser!=!react-icons/fa */ \"__barrel_optimize__?names=FaRegUser!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_webflowServices__WEBPACK_IMPORTED_MODULE_7__, _popup_menuPopup__WEBPACK_IMPORTED_MODULE_8__, _popup_menuPopupMobile__WEBPACK_IMPORTED_MODULE_9__, framer_motion__WEBPACK_IMPORTED_MODULE_12__]);\n([_services_webflowServices__WEBPACK_IMPORTED_MODULE_7__, _popup_menuPopup__WEBPACK_IMPORTED_MODULE_8__, _popup_menuPopupMobile__WEBPACK_IMPORTED_MODULE_9__, framer_motion__WEBPACK_IMPORTED_MODULE_12__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unknown-property */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n// import { Toaster, toast } from 'sonner'\n\n\n\n\n\n\n\n// import { PiHandCoinsFill } from \"react-icons/pi\";\n// import axios from \"axios\";\nconst CountryModal = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"components_model_countryModel_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../model/countryModel */ \"./components/model/countryModel.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../model/countryModel\"\n        ]\n    },\n    ssr: false\n});\nconst LoginPopup = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/react-icons\"), __webpack_require__.e(\"vendor-chunks/@mui\"), __webpack_require__.e(\"components_popup_loginPopup_jsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../popup/loginPopup */ \"./components/popup/loginPopup.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../popup/loginPopup\"\n        ]\n    },\n    ssr: false\n});\n// const MenuPopup = dynamic(() => import(\"../popup/menuPopup\"), { ssr: false });\nconst Noticeboard = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"components_model_noticeboard_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../model/noticeboard */ \"./components/model/noticeboard.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../model/noticeboard\"\n        ]\n    },\n    ssr: false\n});\nconst MyProfile = next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>__webpack_require__.e(/*! import() */ \"components_model_myProfile_jsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../model/myProfile */ \"./components/model/myProfile.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\navbar\\\\navbar.jsx -> \" + \"../model/myProfile\"\n        ]\n    },\n    ssr: false\n});\nconst Navbar = ()=>{\n    const [openCountryModal, setOpenCountryModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenCountryModal = ()=>setOpenCountryModal(true);\n    const handleCloseCountryModal = ()=>setOpenCountryModal(false);\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLoginPopup, setShowLoginPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveringTop, setHoveringTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpenMobile, setIsMenuOpenMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // eslint-disable-next-line no-unused-vars\n    const [hasToken, setHasToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // eslint-disable-next-line no-unused-vars\n    const [roles, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMyProfileOpen, setIsMyProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [flagUrl, setFlagUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currencyCode, setCurrencyCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isPopupOpen, setIsPopupOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [timeLeft, setTimeLeft] = useState(2 * 24 * 60 * 60); // 2 days in seconds\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // const containerRef = useRef(null);\n    const toggleMyProfile = ()=>setIsMyProfileOpen(!isMyProfileOpen);\n    const toggleMenu = ()=>{\n        if (!token || role !== \"user\") {\n            _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                subText: \"You need to be Logged In\"\n            });\n        } else {\n            setIsMenuOpen(!isMenuOpen);\n        }\n    };\n    const toggleMenuMobile = ()=>{\n        if (!token || role !== \"user\") {\n            _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                subText: \"You need to be Logged In\"\n            });\n        } else {\n            setIsMenuOpenMobile(!isMenuOpenMobile);\n        }\n    };\n    // const toggleMenuMobile = () => setIsMenuOpenMobile(!isMenuOpenMobile);\n    const toggleLoginPopup = ()=>setShowLoginPopup(!showLoginPopup);\n    // const [user, setUser] = useState(null);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleRouteChange = {\n                \"Navbar.useEffect.handleRouteChange\": ()=>{\n                    if (isMenuOpen) toggleMenu(false);\n                    if (isMenuOpenMobile) toggleMenuMobile(false);\n                }\n            }[\"Navbar.useEffect.handleRouteChange\"];\n            router.events.on(\"routeChangeStart\", handleRouteChange);\n            // Cleanup\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    router.events.off(\"routeChangeStart\", handleRouteChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        isMenuOpen,\n        isMenuOpenMobile\n    ]);\n    const updateTokenState = ()=>{\n        const token = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"token\");\n        setHasToken(!!token);\n    };\n    const updateRoleState = ()=>{\n        const role = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"role\");\n        setRole(role);\n    };\n    const updateCountry = ()=>{\n        const flag = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCountryFlag\");\n        const code = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCurrencyCode\");\n        setFlagUrl(flag);\n        setCurrencyCode(code);\n        updateCountry2(flag, code);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            updateTokenState();\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            updateRoleState();\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleStorageChange = {\n                \"Navbar.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === \"token\") {\n                        updateTokenState();\n                    }\n                }\n            }[\"Navbar.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleStorageChange = {\n                \"Navbar.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === \"role\") {\n                        updateRoleState();\n                    }\n                }\n            }[\"Navbar.useEffect.handleStorageChange\"];\n            window.addEventListener(\"storage\", handleStorageChange);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"storage\", handleStorageChange);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const originalSetItem = localStorage.setItem;\n            localStorage.setItem = ({\n                \"Navbar.useEffect\": function(key) {\n                    const event = new Event(\"itemInserted\");\n                    originalSetItem.apply(this, arguments);\n                    if (key === \"token\") {\n                        window.dispatchEvent(event);\n                    }\n                }\n            })[\"Navbar.useEffect\"];\n            const handleItemInserted = {\n                \"Navbar.useEffect.handleItemInserted\": ()=>{\n                    updateTokenState();\n                }\n            }[\"Navbar.useEffect.handleItemInserted\"];\n            window.addEventListener(\"itemInserted\", handleItemInserted);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"itemInserted\", handleItemInserted);\n                    localStorage.setItem = originalSetItem;\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const originalSetItem = localStorage.setItem;\n            localStorage.setItem = ({\n                \"Navbar.useEffect\": function(key) {\n                    const event = new Event(\"itemInserted\");\n                    originalSetItem.apply(this, arguments);\n                    if (key === \"token\") {\n                        window.dispatchEvent(event);\n                    }\n                }\n            })[\"Navbar.useEffect\"];\n            const handleItemInserted = {\n                \"Navbar.useEffect.handleItemInserted\": ()=>{\n                    updateRoleState();\n                }\n            }[\"Navbar.useEffect.handleItemInserted\"];\n            window.addEventListener(\"itemInserted\", handleItemInserted);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"itemInserted\", handleItemInserted);\n                    localStorage.setItem = originalSetItem;\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 0);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            const handleMouseMove = {\n                \"Navbar.useEffect.handleMouseMove\": (event)=>{\n                    setHoveringTop(event.clientY < 85);\n                }\n            }[\"Navbar.useEffect.handleMouseMove\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            window.addEventListener(\"mousemove\", handleMouseMove);\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    window.removeEventListener(\"scroll\", handleScroll);\n                    window.removeEventListener(\"mousemove\", handleMouseMove);\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const selectedCountryFlag = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCountryFlag\");\n            const selectedCurrencyCode = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCurrencyCode\");\n            if (selectedCountryFlag) {\n                const url = selectedCountryFlag;\n                setFlagUrl(url);\n                setCurrencyCode(selectedCurrencyCode);\n            }\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Notice Board Modal\n    const [openNoticeBoard, setOpenNoticeBoard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openNoticeBoardDetails, setOpenNoticeBoardDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenNoticeBoard = async ()=>{\n        if ((0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"token\") && role === \"user\") {\n            const propertyCountResponse = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_7__.getPropertyCountApi)();\n            if (propertyCountResponse?.data?.data?.totalBooking > 0) {\n                setOpenNoticeBoardDetails(true);\n            } else if (propertyCountResponse?.data?.data?.totalBooking === 0) {\n                setOpenNoticeBoard(true);\n            }\n        } else {\n            _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                subText: \"You need to be Logged In\"\n            });\n        }\n    };\n    const handleCloseNoticeBoard = ()=>{\n        setOpenNoticeBoard(false);\n        setOpenNoticeBoardDetails(false);\n    };\n    // Mobile Drawer\n    const [openDrawer, setOpenDrawer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleDrawer = (newOpen)=>()=>{\n            setOpenDrawer(newOpen);\n        };\n    const { updateCountry2, token, role } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_6__.useNavbar)();\n    // Update country when the component is mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const selectedCountryFlag = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCountryFlag\");\n            const selectedCurrencyCode = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"selectedCurrencyCode\");\n            if (selectedCountryFlag && selectedCurrencyCode) {\n                updateCountry2(selectedCountryFlag, selectedCurrencyCode);\n            }\n            setFlagUrl(selectedCountryFlag);\n            setCurrencyCode(selectedCurrencyCode);\n        }\n    }[\"Navbar.useEffect\"], [\n        updateCountry2\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    if (window.innerWidth < 768) return;\n                    if (window.scrollY > 900) {\n                        setIsScrolled(true);\n                    } else {\n                        setIsScrolled(false);\n                    }\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll);\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Auto show popup on load, close after 4 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            setIsPopupOpen(true);\n            const closeTimer = setTimeout({\n                \"Navbar.useEffect.closeTimer\": ()=>{\n                    setIsPopupOpen(false);\n                }\n            }[\"Navbar.useEffect.closeTimer\"], 4000);\n            return ({\n                \"Navbar.useEffect\": ()=>clearTimeout(closeTimer)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Countdown timer\n    // useEffect(() => {\n    //   if (!isPopupOpen) return;\n    //   const interval = setInterval(() => {\n    //     setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));\n    //   }, 1000);\n    //   return () => clearInterval(interval);\n    // }, [isPopupOpen]);\n    // Close on outside click\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (popupRef.current && !popupRef.current.contains(event.target)) {\n                    setIsPopupOpen(false);\n                }\n            }\n            if (isPopupOpen) {\n                document.addEventListener(\"mousedown\", handleClickOutside);\n            } else {\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n            }\n            return ({\n                \"Navbar.useEffect\": ()=>document.removeEventListener(\"mousedown\", handleClickOutside)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], [\n        isPopupOpen\n    ]);\n    // Format time left as hh:mm:ss\n    // const formatTime = (seconds) => {\n    //   const days = Math.floor(seconds / (24 * 3600));\n    //   const hrs = Math.floor((seconds % (24 * 3600)) / 3600);\n    //   const mins = Math.floor((seconds % 3600) / 60);\n    //   const secs = seconds % 60;\n    //   return `${days}d:${hrs.toString().padStart(2, \"0\")}:${mins\n    //     .toString()\n    //     .padStart(2, \"0\")}:${secs.toString().padStart(2, \"0\")}`;\n    // };\n    //   useEffect(() => {\n    //   const fetchUserData = async () => {\n    //     try {\n    //       const res = await axios.get(\"/api/user/me\", {\n    //         headers: {\n    //           Authorization: `Bearer ${token}`,\n    //         },\n    //       });\n    //       setUser(res.data); // Make sure the API returns { name: \"User Name\", ... }\n    //     } catch (error) {\n    //       console.error(\"Error fetching user data:\", error);\n    //     }\n    //   };\n    //   if (token) fetchUserData();\n    // }, [token]);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const isHomePage = router.pathname === \"/\";\n    const isTopHostelPage = router.pathname === \"/tophostel\" || router.pathname === \"/exploreworld\" || router.pathname === \"/featuredhostel\" || router.pathname === \"/travelactivity\" || router.pathname === \"/meetbuddies\" || router.pathname === \"/discover-event\" || router.pathname === \"/search\";\n    console.log(\"token\", token);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: `w-full duration-300 ease-in-out sticky top-0 z-40 overflow-visible ${scrolled && !hoveringTop ? \"-top-10\" : \"sticky top-0\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center px-5 py-2.5 bg-black w-full min-h-[44px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white xs:text-sm text-xs leading-tight text-center\",\n                                children: [\n                                    \"Get 1 Month Free\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/my-profile?section=membership\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-blue\",\n                                            children: \" Mix Premium \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Membership – Sign Up Now!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            !token || role !== \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"absolute inset-0 w-full h-full\",\n                                \"aria-label\": \"Login required to access Mix Premium Membership\",\n                                onClick: ()=>{\n                                    _toast_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error(\"Please Login !\", {\n                                        subText: \"You need to be Logged In\"\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, undefined) : null\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full hidden lg:flex ${isTopHostelPage ? \"absolute z-50 bg-black bg-opacity-10 shadow-md backdrop-blur-sm\" : isHomePage ? isScrolled ? \"bg-white shadow-md\" : \"bg-white bg-opacity-10 fixed top-10 z-50 backdrop-blur-sm\" : \"bg-transparent lg:bg-white shadow-md\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-50 flex items-start justify-between py-4 container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[45%] flex items-center gap-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"md:hidden block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                // href=\"#\"\n                                                rel: \"canonical\",\n                                                className: `text-2xl font-manrope justify-center items-center font-bold cursor-pointer  duration-300 ease-in-out  ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                onClick: toggleDrawer(true),\n                                                prefetch: false,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiMenuAlt3_react_icons_hi__WEBPACK_IMPORTED_MODULE_13__.HiMenuAlt3, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/\",\n                                            rel: \"canonical\",\n                                            prefetch: false,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: isTopHostelPage ? `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Mixdorm-white-D.svg` : isHomePage ? isScrolled ? `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/logo.svg` : `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Mixdorm-white-D.svg` : `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/logo.svg`,\n                                                width: 155,\n                                                height: 40,\n                                                alt: \"Mixdorm\",\n                                                title: \"Mixdorm\",\n                                                className: \"max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out\",\n                                                loading: \"lazy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[55%] flex justify-end items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"flex items-center justify-center md:gap-x-5 gap-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"md:block hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/owner/list-your-hostel\",\n                                                    passHref: true,\n                                                    className: \"text-xs text-center font-manrope min-w-[140px] w-full block font-bold bg-primary-blue cursor-pointer rounded-9xl text-black duration-300 ease-in-out px-5 py-3\",\n                                                    prefetch: false,\n                                                    children: \"List Your Hostel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: !token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    // href=\"#\"\n                                                    className: `text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                    onClick: toggleLoginPopup,\n                                                    prefetch: false,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.User, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: \"Traveller\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, undefined) : token && role === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    // href=\"#\"\n                                                    rel: \"canonical\",\n                                                    className: `text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                    // onClick={toggleMyProfile} // Open MyProfile if token exists\n                                                    prefetch: false,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.User, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"name\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"#\",\n                                                    className: `text-sm font-manrope flex items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                    onClick: toggleLoginPopup,\n                                                    prefetch: false,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.User, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Traveller\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: `text-sm font-manrope items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 flex ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                    onClick: handleOpenCountryModal,\n                                                    children: [\n                                                        flagUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            src: flagUrl,\n                                                            alt: \"Country Flag\",\n                                                            width: 20,\n                                                            height: 20,\n                                                            loading: \"lazy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 23\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Globe, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        currencyCode ? currencyCode : \"Country\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.ChevronDown, {\n                                                            size: 18\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        \"aria-label\": \"Mobile Menu\",\n                                                        className: `sm:hidden text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                        onClick: toggleMenuMobile,\n                                                        prefetch: false,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Grip, {\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        \"aria-label\": \"Mobile Menu\",\n                                                        className: `hidden sm:block text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out ${isTopHostelPage ? \"text-white\" : isHomePage ? isScrolled ? \"text-black\" : \"text-white\" : \"text-black\"}`,\n                                                        onClick: toggleMenu,\n                                                        prefetch: false,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Grip, {\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, undefined),\n                    !isHomePage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-full flex lg:hidden ${isTopHostelPage ? \"absolute z-50 bg-black bg-opacity-10 shadow-md backdrop-blur-sm\" : \"bg-white border-y border-white\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-50 flex items-start justify-between py-4 container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[30%] flex items-center gap-x-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/\",\n                                        rel: \"canonical\",\n                                        prefetch: false,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: isTopHostelPage ? `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Mixdorm-white-D.svg` : `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/logo.svg`,\n                                            width: 155,\n                                            height: 40,\n                                            alt: \"Content Ai\",\n                                            title: \"Content Ai\",\n                                            className: \"max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out\",\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-[55%] flex justify-end items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"flex items-center justify-center md:gap-x-5 gap-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        // href=\"0\"\n                                                        rel: \"canonical\",\n                                                        className: `text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl  duration-300 ease-in-out gap-x-2 flex ${isTopHostelPage ? \"text-white\" : \"text-black\"}`,\n                                                        onClick: handleOpenNoticeBoard,\n                                                        \"aria-label\": \"Notification\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineNotificationsActive_react_icons_md__WEBPACK_IMPORTED_MODULE_15__.MdOutlineNotificationsActive, {\n                                                                size: 26,\n                                                                className: `font-normal ${isTopHostelPage ? \"text-white\" : \"text-black\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \" \"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 animate-ping rounded-full bg-primary-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute flex items-center justify-center left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 rounded-full bg-primary-blue text-[10px] font-medium text-black text-center leading-none\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: !token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    // href=\"#\"\n                                                    className: `text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 ${isTopHostelPage ? \"text-white\" : \"text-black\"}`,\n                                                    onClick: toggleLoginPopup,\n                                                    \"aria-label\": \"User\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_16__.FaRegUser, {\n                                                            size: 20,\n                                                            className: `font-bold ${isTopHostelPage ? \"text-white\" : \"text-black\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 746,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: \"Traveller\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 23\n                                                }, undefined) : token && role === \"user\" ? // <button\n                                                //   // href=\"#\"\n                                                //   rel=\"canonical\"\n                                                //   className={`text-sm font-manrope flex items-center  justify-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 border-2 border-black-100 rounded-full h-7 w-7  ${isTopHostelPage ? \"text-white\" : \"text-black\"}`}\n                                                //   // onClick={toggleMyProfile} // Open MyProfile if token exists\n                                                // >\n                                                //   {/* <FaRegUser size={20} />  */}\n                                                //   A\n                                                // </button>\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `text-sm font-manrope flex items-center justify-center font-bold cursor-pointer duration-300 ease-in-out gap-x-2  rounded-full h-7 w-7  ${isTopHostelPage ? \"bg-primary-blue text-black\" : \"bg-primary-blue text-black\"}`,\n                                                            onMouseEnter: ()=>setShowTooltip(true),\n                                                            onMouseLeave: ()=>setShowTooltip(false),\n                                                            children: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"name\")?.charAt(0)?.toUpperCase() || \"A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n                                                            children: showTooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    y: 5\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    y: 0\n                                                                },\n                                                                exit: {\n                                                                    opacity: 0,\n                                                                    y: 5\n                                                                },\n                                                                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 bg-white text-black text-xs rounded py-1 px-2 whitespace-nowrap\",\n                                                                children: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_5__.getItemLocalStorage)(\"name\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"#\",\n                                                    className: `text-sm font-manrope flex items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 ${isTopHostelPage ? \"text-white\" : \"text-black\"} `,\n                                                    onClick: toggleLoginPopup,\n                                                    prefetch: false,\n                                                    \"aria-label\": \"User\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_16__.FaRegUser, {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 802,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden md:block\",\n                                                            children: \"Traveller\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: `text-sm font-manrope items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 flex ${isTopHostelPage ? \"text-white\" : \"text-black\"}`,\n                                                    onClick: handleOpenCountryModal,\n                                                    \"aria-label\": \"Flag\",\n                                                    children: flagUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-6 h-6 rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            src: flagUrl,\n                                                            alt: \"Country Flag\",\n                                                            fill: true,\n                                                            className: \"object-cover rounded-full\",\n                                                            sizes: \"24px\",\n                                                            loading: \"lazy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Globe, {\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    \"aria-label\": \"Mobile Menu\",\n                                                    className: `block text-sm font-manrope font-bold cursor-pointer   duration-300 ease-in-out ${isTopHostelPage ? \"text-white\" : \"text-black\"}`,\n                                                    onClick: toggleMenuMobile,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Grip, {\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 686,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Drawer, {\n                open: openDrawer,\n                onClose: toggleDrawer(false),\n                className: \"nav-bar-humburger fadeInLeft animated\",\n                anchor: \"left\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Box, {\n                    sx: {\n                        width: 346\n                    },\n                    role: \"presentation\",\n                    borderRadius: 10,\n                    onClick: toggleDrawer(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Paper, {\n                        sx: {\n                            width: 326,\n                            background: \"#fff\",\n                            borderRadius: \"0 0 10px 10px\",\n                            height: \"100vh\",\n                            elevation: 0\n                        },\n                        elevation: 0,\n                        borderRadius: 10,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                rel: \"canonical\",\n                                className: \"p-4 block\",\n                                prefetch: false,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/logo.svg`,\n                                    width: 155,\n                                    height: 40,\n                                    alt: \"Mixdorm\",\n                                    title: \"Mixdorm\",\n                                    className: \"max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out\",\n                                    loading: \"lazy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 883,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"absolute top-4 right-4\",\n                                onClick: toggleDrawer(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.X, {\n                                    size: 22\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 899,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Divider_Drawer_Paper_mui_material__WEBPACK_IMPORTED_MODULE_17__.Divider, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 905,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"\",\n                                        className: \"text-sm sm:text-base flex items-center gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Globe, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \" Select Currency\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center gap-x-2 text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out\",\n                                        onClick: handleOpenCountryModal,\n                                        children: [\n                                            flagUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: flagUrl,\n                                                alt: \"Country Flag\",\n                                                width: 20,\n                                                height: 20,\n                                                loading: \"lazy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.Globe, {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            currencyCode ? currencyCode : \"Country\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_Grip_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__.ChevronDown, {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 906,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/owner/hostel-login\",\n                                    rel: \"canonical\",\n                                    className: \"block px-5 py-3 text-center font-manrope text-base font-bold bg-primary-blue cursor-pointer rounded-4xl text-black duration-300 ease-in-out\",\n                                    prefetch: false,\n                                    children: \"List Your Hostel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                                lineNumber: 950,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                        lineNumber: 872,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                    lineNumber: 866,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 860,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Noticeboard, {\n                close: handleCloseNoticeBoard,\n                open: openNoticeBoard,\n                openNoticeBoardDetails: openNoticeBoardDetails\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 964,\n                columnNumber: 7\n            }, undefined),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_popup_menuPopup__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isMenuOpen,\n                toggleMenu: toggleMenu,\n                updateTokenState: updateTokenState,\n                toggleLoginPopup: toggleLoginPopup,\n                updateRoleState: updateRoleState\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 970,\n                columnNumber: 9\n            }, undefined),\n            isMenuOpenMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_popup_menuPopupMobile__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isMenuOpenMobile,\n                toggleMenu: toggleMenuMobile,\n                updateTokenState: updateTokenState,\n                toggleLoginPopup: toggleLoginPopup,\n                updateRoleState: updateRoleState\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 979,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MyProfile, {\n                isMenuOpen: isMyProfileOpen,\n                toggleMenu: toggleMyProfile,\n                updateTokenState: updateTokenState,\n                updateRoleState: updateRoleState\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 988,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginPopup, {\n                isOpen: showLoginPopup,\n                onClose: toggleLoginPopup\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 994,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CountryModal, {\n                openCountryModal: openCountryModal,\n                handleCloseCountryModal: handleCloseCountryModal,\n                updateCountry: updateCountry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\navbar\\\\navbar.jsx\",\n                lineNumber: 996,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_dynamic__WEBPACK_IMPORTED_MODULE_4___default()(()=>Promise.resolve(Navbar), {\n    ssr: false\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL25hdmJhci9uYXZiYXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw0Q0FBNEM7QUFHZTtBQUM1QjtBQUNGO0FBQ2lDO0FBQ0c7QUFDOUI7QUFDUztBQUNnQjtBQUNDO0FBQ1g7QUFDbEQsMENBQTBDO0FBQ3VCO0FBQ3RCO0FBQ1k7QUFDcEI7QUFDSztBQUNHO0FBQ2E7QUFDeEQsb0RBQW9EO0FBRXBELDZCQUE2QjtBQUU3QixNQUFNNEIsZUFBZWhCLG1EQUFPQSxDQUFDLElBQU0sa01BQStCOzs7Ozs7SUFDaEVpQixLQUFLOztBQUVQLE1BQU1DLGFBQWFsQixtREFBT0EsQ0FBQyxJQUFNLDJTQUE2Qjs7Ozs7O0lBQUlpQixLQUFLOztBQUN2RSxpRkFBaUY7QUFDakYsTUFBTUUsY0FBY25CLG1EQUFPQSxDQUFDLElBQU0sK0xBQThCOzs7Ozs7SUFDOURpQixLQUFLOztBQUVQLE1BQU1HLFlBQVlwQixtREFBT0EsQ0FBQyxJQUFNLHlMQUE0Qjs7Ozs7O0lBQUlpQixLQUFLOztBQUVyRSxNQUFNSSxTQUFTO0lBQ2IsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHaEMsK0NBQVFBLENBQUM7SUFDekQsTUFBTWlDLHlCQUF5QixJQUFNRCxvQkFBb0I7SUFDekQsTUFBTUUsMEJBQTBCLElBQU1GLG9CQUFvQjtJQUMxRCxNQUFNLENBQUNHLGFBQWFDLGVBQWUsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3FDLFVBQVVDLFlBQVksR0FBR3RDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3VDLGdCQUFnQkMsa0JBQWtCLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN5QyxhQUFhQyxlQUFlLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMyQyxZQUFZQyxjQUFjLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2QyxrQkFBa0JDLG9CQUFvQixHQUFHOUMsK0NBQVFBLENBQUM7SUFDekQsMENBQTBDO0lBQzFDLE1BQU0sQ0FBQytDLFVBQVVDLFlBQVksR0FBR2hELCtDQUFRQSxDQUFDO0lBQ3pDLDBDQUEwQztJQUMxQyxNQUFNLENBQUNpRCxPQUFPQyxRQUFRLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUNsQyxNQUFNLENBQUNtRCxpQkFBaUJDLG1CQUFtQixHQUFHcEQsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDcUQsU0FBU0MsV0FBVyxHQUFHdEQsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDdUQsY0FBY0MsZ0JBQWdCLEdBQUd4RCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN5RCxhQUFhQyxlQUFlLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUMvQyxtRkFBbUY7SUFDbkYsTUFBTTJELFdBQVc1RCw2Q0FBTUEsQ0FBQztJQUN4QixxQ0FBcUM7SUFFckMsTUFBTTZELGtCQUFrQixJQUFNUixtQkFBbUIsQ0FBQ0Q7SUFDbEQsTUFBTVUsYUFBYTtRQUNqQixJQUFJLENBQUNDLFNBQVNDLFNBQVMsUUFBUTtZQUM3QjNDLDJEQUFXLENBQUMsa0JBQWtCO2dCQUM1QjZDLFNBQVM7WUFDWDtRQUNGLE9BQU87WUFDTHJCLGNBQWMsQ0FBQ0Q7UUFDakI7SUFDRjtJQUNBLE1BQU11QixtQkFBbUI7UUFDdkIsSUFBSSxDQUFDSixTQUFTQyxTQUFTLFFBQVE7WUFDN0IzQywyREFBVyxDQUFDLGtCQUFrQjtnQkFDNUI2QyxTQUFTO1lBQ1g7UUFDRixPQUFPO1lBQ0xuQixvQkFBb0IsQ0FBQ0Q7UUFDdkI7SUFDRjtJQUNBLHlFQUF5RTtJQUN6RSxNQUFNc0IsbUJBQW1CLElBQU0zQixrQkFBa0IsQ0FBQ0Q7SUFDbEQsMENBQTBDO0lBQzFDLE1BQU0sQ0FBQzZCLFlBQVlDLGNBQWMsR0FBR3JFLCtDQUFRQSxDQUFDO0lBRTdDRixnREFBU0E7NEJBQUM7WUFDUixNQUFNd0U7c0RBQW9CO29CQUN4QixJQUFJM0IsWUFBWWtCLFdBQVc7b0JBQzNCLElBQUloQixrQkFBa0JxQixpQkFBaUI7Z0JBQ3pDOztZQUVBSyxPQUFPQyxNQUFNLENBQUNDLEVBQUUsQ0FBQyxvQkFBb0JIO1lBRXJDLFVBQVU7WUFDVjtvQ0FBTztvQkFDTEMsT0FBT0MsTUFBTSxDQUFDRSxHQUFHLENBQUMsb0JBQW9CSjtnQkFDeEM7O1FBQ0Y7MkJBQUc7UUFBQzNCO1FBQVlFO0tBQWlCO0lBRWpDLE1BQU04QixtQkFBbUI7UUFDdkIsTUFBTWIsUUFBUS9DLDBFQUFtQkEsQ0FBQztRQUNsQ2lDLFlBQVksQ0FBQyxDQUFDYztJQUNoQjtJQUVBLE1BQU1jLGtCQUFrQjtRQUN0QixNQUFNYixPQUFPaEQsMEVBQW1CQSxDQUFDO1FBQ2pDbUMsUUFBUWE7SUFDVjtJQUVBLE1BQU1jLGdCQUFnQjtRQUNwQixNQUFNQyxPQUFPL0QsMEVBQW1CQSxDQUFDO1FBQ2pDLE1BQU1nRSxPQUFPaEUsMEVBQW1CQSxDQUFDO1FBRWpDdUMsV0FBV3dCO1FBQ1h0QixnQkFBZ0J1QjtRQUNoQkMsZUFBZUYsTUFBTUM7SUFDdkI7SUFFQWpGLGdEQUFTQTs0QkFBQztZQUNSNkU7UUFDRjsyQkFBRyxFQUFFO0lBRUw3RSxnREFBU0E7NEJBQUM7WUFDUjhFO1FBQ0Y7MkJBQUcsRUFBRTtJQUVMOUUsZ0RBQVNBOzRCQUFDO1lBQ1IsTUFBTW1GO3dEQUFzQixDQUFDQztvQkFDM0IsSUFBSUEsTUFBTUMsR0FBRyxLQUFLLFNBQVM7d0JBQ3pCUjtvQkFDRjtnQkFDRjs7WUFFQVMsT0FBT0MsZ0JBQWdCLENBQUMsV0FBV0o7WUFFbkM7b0NBQU87b0JBQ0xHLE9BQU9FLG1CQUFtQixDQUFDLFdBQVdMO2dCQUN4Qzs7UUFDRjsyQkFBRyxFQUFFO0lBRUxuRixnREFBU0E7NEJBQUM7WUFDUixNQUFNbUY7d0RBQXNCLENBQUNDO29CQUMzQixJQUFJQSxNQUFNQyxHQUFHLEtBQUssUUFBUTt3QkFDeEJQO29CQUNGO2dCQUNGOztZQUVBUSxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXSjtZQUVuQztvQ0FBTztvQkFDTEcsT0FBT0UsbUJBQW1CLENBQUMsV0FBV0w7Z0JBQ3hDOztRQUNGOzJCQUFHLEVBQUU7SUFFTG5GLGdEQUFTQTs0QkFBQztZQUNSLE1BQU15RixrQkFBa0JDLGFBQWFDLE9BQU87WUFDNUNELGFBQWFDLE9BQU87b0NBQUcsU0FBVU4sR0FBRztvQkFDbEMsTUFBTUQsUUFBUSxJQUFJUSxNQUFNO29CQUN4QkgsZ0JBQWdCSSxLQUFLLENBQUMsSUFBSSxFQUFFQztvQkFDNUIsSUFBSVQsUUFBUSxTQUFTO3dCQUNuQkMsT0FBT1MsYUFBYSxDQUFDWDtvQkFDdkI7Z0JBQ0Y7O1lBRUEsTUFBTVk7dURBQXFCO29CQUN6Qm5CO2dCQUNGOztZQUVBUyxPQUFPQyxnQkFBZ0IsQ0FBQyxnQkFBZ0JTO1lBRXhDO29DQUFPO29CQUNMVixPQUFPRSxtQkFBbUIsQ0FBQyxnQkFBZ0JRO29CQUMzQ04sYUFBYUMsT0FBTyxHQUFHRjtnQkFDekI7O1FBQ0Y7MkJBQUcsRUFBRTtJQUVMekYsZ0RBQVNBOzRCQUFDO1lBQ1IsTUFBTXlGLGtCQUFrQkMsYUFBYUMsT0FBTztZQUM1Q0QsYUFBYUMsT0FBTztvQ0FBRyxTQUFVTixHQUFHO29CQUNsQyxNQUFNRCxRQUFRLElBQUlRLE1BQU07b0JBQ3hCSCxnQkFBZ0JJLEtBQUssQ0FBQyxJQUFJLEVBQUVDO29CQUM1QixJQUFJVCxRQUFRLFNBQVM7d0JBQ25CQyxPQUFPUyxhQUFhLENBQUNYO29CQUN2QjtnQkFDRjs7WUFFQSxNQUFNWTt1REFBcUI7b0JBQ3pCbEI7Z0JBQ0Y7O1lBRUFRLE9BQU9DLGdCQUFnQixDQUFDLGdCQUFnQlM7WUFFeEM7b0NBQU87b0JBQ0xWLE9BQU9FLG1CQUFtQixDQUFDLGdCQUFnQlE7b0JBQzNDTixhQUFhQyxPQUFPLEdBQUdGO2dCQUN6Qjs7UUFDRjsyQkFBRyxFQUFFO0lBRUx6RixnREFBU0E7NEJBQUM7WUFDUixNQUFNaUc7aURBQWU7b0JBQ25CekQsWUFBWThDLE9BQU9ZLE9BQU8sR0FBRztnQkFDL0I7O1lBRUEsTUFBTUM7b0RBQWtCLENBQUNmO29CQUN2QnhDLGVBQWV3QyxNQUFNZ0IsT0FBTyxHQUFHO2dCQUNqQzs7WUFFQWQsT0FBT0MsZ0JBQWdCLENBQUMsVUFBVVU7WUFDbENYLE9BQU9DLGdCQUFnQixDQUFDLGFBQWFZO1lBRXJDO29DQUFPO29CQUNMYixPQUFPRSxtQkFBbUIsQ0FBQyxVQUFVUztvQkFDckNYLE9BQU9FLG1CQUFtQixDQUFDLGFBQWFXO2dCQUMxQzs7UUFDRjsyQkFBRyxFQUFFO0lBRUxuRyxnREFBU0E7NEJBQUM7WUFDUixNQUFNcUcsc0JBQXNCcEYsMEVBQW1CQSxDQUFDO1lBQ2hELE1BQU1xRix1QkFBdUJyRiwwRUFBbUJBLENBQUM7WUFFakQsSUFBSW9GLHFCQUFxQjtnQkFDdkIsTUFBTUUsTUFBTUY7Z0JBQ1o3QyxXQUFXK0M7Z0JBQ1g3QyxnQkFBZ0I0QztZQUNsQjtRQUNGOzJCQUFHLEVBQUU7SUFDTCxxQkFBcUI7SUFDckIsTUFBTSxDQUFDRSxpQkFBaUJDLG1CQUFtQixHQUFHdkcsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDd0csd0JBQXdCQywwQkFBMEIsR0FBR3pHLCtDQUFRQSxDQUFDO0lBRXJFLE1BQU0wRyx3QkFBd0I7UUFDNUIsSUFBSTNGLDBFQUFtQkEsQ0FBQyxZQUFZZ0QsU0FBUyxRQUFRO1lBQ25ELE1BQU00Qyx3QkFBd0IsTUFBTTFGLDhFQUFtQkE7WUFDdkQsSUFBSTBGLHVCQUF1QkMsTUFBTUEsTUFBTUMsZUFBZSxHQUFHO2dCQUN2REosMEJBQTBCO1lBQzVCLE9BQU8sSUFBSUUsdUJBQXVCQyxNQUFNQSxNQUFNQyxpQkFBaUIsR0FBRztnQkFDaEVOLG1CQUFtQjtZQUNyQjtRQUNGLE9BQU87WUFDTG5GLDJEQUFXLENBQUMsa0JBQWtCO2dCQUM1QjZDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFDQSxNQUFNNkMseUJBQXlCO1FBQzdCUCxtQkFBbUI7UUFDbkJFLDBCQUEwQjtJQUM1QjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNLENBQUNNLFlBQVlDLGNBQWMsR0FBR2hILCtDQUFRQSxDQUFDO0lBRTdDLE1BQU1pSCxlQUFlLENBQUNDLFVBQVk7WUFDaENGLGNBQWNFO1FBQ2hCO0lBRUEsTUFBTSxFQUFFbEMsY0FBYyxFQUFFbEIsS0FBSyxFQUFFQyxJQUFJLEVBQUUsR0FBRy9DLDhEQUFTQTtJQUVqRCwrQ0FBK0M7SUFDL0NsQixnREFBU0E7NEJBQUM7WUFDUixNQUFNcUcsc0JBQXNCcEYsMEVBQW1CQSxDQUFDO1lBQ2hELE1BQU1xRix1QkFBdUJyRiwwRUFBbUJBLENBQUM7WUFFakQsSUFBSW9GLHVCQUF1QkMsc0JBQXNCO2dCQUMvQ3BCLGVBQWVtQixxQkFBcUJDO1lBQ3RDO1lBQ0E5QyxXQUFXNkM7WUFDWDNDLGdCQUFnQjRDO1FBQ2xCOzJCQUFHO1FBQUNwQjtLQUFlO0lBRW5CbEYsZ0RBQVNBOzRCQUFDO1lBQ1IsTUFBTWlHO2lEQUFlO29CQUNuQixJQUFJWCxPQUFPK0IsVUFBVSxHQUFHLEtBQUs7b0JBQzdCLElBQUkvQixPQUFPWSxPQUFPLEdBQUcsS0FBSzt3QkFDeEIzQixjQUFjO29CQUNoQixPQUFPO3dCQUNMQSxjQUFjO29CQUNoQjtnQkFDRjs7WUFFQWUsT0FBT0MsZ0JBQWdCLENBQUMsVUFBVVU7WUFDbEM7b0NBQU8sSUFBTVgsT0FBT0UsbUJBQW1CLENBQUMsVUFBVVM7O1FBQ3BEOzJCQUFHLEVBQUU7SUFFTCxpREFBaUQ7SUFDakRqRyxnREFBU0E7NEJBQUM7WUFDUjRELGVBQWU7WUFDZixNQUFNMEQsYUFBYUM7K0NBQVc7b0JBQzVCM0QsZUFBZTtnQkFDakI7OENBQUc7WUFDSDtvQ0FBTyxJQUFNNEQsYUFBYUY7O1FBQzVCOzJCQUFHLEVBQUU7SUFFTCxrQkFBa0I7SUFDbEIsb0JBQW9CO0lBQ3BCLDhCQUE4QjtJQUU5Qix5Q0FBeUM7SUFDekMsd0RBQXdEO0lBQ3hELGNBQWM7SUFDZCwwQ0FBMEM7SUFDMUMscUJBQXFCO0lBRXJCLHlCQUF5QjtJQUN6QnRILGdEQUFTQTs0QkFBQztZQUNSLFNBQVN5SCxtQkFBbUJyQyxLQUFLO2dCQUMvQixJQUFJdkIsU0FBUzZELE9BQU8sSUFBSSxDQUFDN0QsU0FBUzZELE9BQU8sQ0FBQ0MsUUFBUSxDQUFDdkMsTUFBTXdDLE1BQU0sR0FBRztvQkFDaEVoRSxlQUFlO2dCQUNqQjtZQUNGO1lBRUEsSUFBSUQsYUFBYTtnQkFDZmtFLFNBQVN0QyxnQkFBZ0IsQ0FBQyxhQUFha0M7WUFDekMsT0FBTztnQkFDTEksU0FBU3JDLG1CQUFtQixDQUFDLGFBQWFpQztZQUM1QztZQUVBO29DQUFPLElBQU1JLFNBQVNyQyxtQkFBbUIsQ0FBQyxhQUFhaUM7O1FBQ3pEOzJCQUFHO1FBQUM5RDtLQUFZO0lBRWhCLCtCQUErQjtJQUMvQixvQ0FBb0M7SUFDcEMsb0RBQW9EO0lBQ3BELDREQUE0RDtJQUM1RCxvREFBb0Q7SUFDcEQsK0JBQStCO0lBRS9CLCtEQUErRDtJQUMvRCxrQkFBa0I7SUFDbEIsK0RBQStEO0lBQy9ELEtBQUs7SUFFTCxzQkFBc0I7SUFDdEIsd0NBQXdDO0lBQ3hDLFlBQVk7SUFDWixzREFBc0Q7SUFDdEQscUJBQXFCO0lBQ3JCLDhDQUE4QztJQUM5QyxhQUFhO0lBQ2IsWUFBWTtJQUNaLG1GQUFtRjtJQUNuRix3QkFBd0I7SUFDeEIsMkRBQTJEO0lBQzNELFFBQVE7SUFDUixPQUFPO0lBRVAsZ0NBQWdDO0lBQ2hDLGVBQWU7SUFFZixNQUFNYyxTQUFTbEQsdURBQVNBO0lBQ3hCLE1BQU11RyxhQUFhckQsT0FBT3NELFFBQVEsS0FBSztJQUN2QyxNQUFNQyxrQkFDSnZELE9BQU9zRCxRQUFRLEtBQUssZ0JBQ3BCdEQsT0FBT3NELFFBQVEsS0FBSyxtQkFDcEJ0RCxPQUFPc0QsUUFBUSxLQUFLLHFCQUNwQnRELE9BQU9zRCxRQUFRLEtBQUsscUJBQ3BCdEQsT0FBT3NELFFBQVEsS0FBSyxrQkFDcEJ0RCxPQUFPc0QsUUFBUSxLQUFLLHFCQUNwQnRELE9BQU9zRCxRQUFRLEtBQUs7SUFFdEJFLFFBQVFDLEdBQUcsQ0FBQyxTQUFTbEU7SUFFckIscUJBQ0U7OzBCQUVFLDhEQUFDbUU7Z0JBQ0NDLFdBQVcsQ0FBQyxtRUFBbUUsRUFDN0U3RixZQUFZLENBQUNJLGNBQWMsWUFBWSxnQkFDdkM7O2tDQUVGLDhEQUFDMEY7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBRUYsV0FBVTs7b0NBQTBEO2tEQUVyRSw4REFBQ2hJLGtEQUFJQTt3Q0FBQ21JLE1BQU07a0RBQ1YsNEVBQUNDOzRDQUFLSixXQUFVO3NEQUFvQjs7Ozs7Ozs7Ozs7b0NBQy9COzs7Ozs7OzRCQUdSLENBQUNwRSxTQUFTQyxTQUFTLHVCQUNsQiw4REFBQ3dFO2dDQUNDTCxXQUFVO2dDQUNWTSxjQUFXO2dDQUNYQyxTQUFTO29DQUNQckgsMkRBQVcsQ0FBQyxrQkFBa0I7d0NBQzVCNkMsU0FBUztvQ0FDWDtnQ0FDRjs7Ozs7NENBRUE7Ozs7Ozs7a0NBRU4sOERBQUNrRTt3QkFDQ0QsV0FBVyxDQUFDLHNCQUFzQixFQUNoQ0osa0JBQ0ksb0VBQ0FGLGFBQ0F4RCxhQUNFLHVCQUNBLDhEQUNGLHdDQUNKO2tDQUVGLDRFQUFDK0Q7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNROzRDQUFHUixXQUFVO3NEQUNaLDRFQUFDSztnREFDQyxXQUFXO2dEQUNYSSxLQUFJO2dEQUNKVCxXQUFXLENBQUMsc0dBQXNHLEVBQ2hISixrQkFDSSxlQUNBRixhQUNBeEQsYUFDRSxlQUNBLGVBQ0YsY0FDSjtnREFDRnFFLFNBQVN4QixhQUFhO2dEQUN0QjJCLFVBQVU7MERBRVYsNEVBQUNsSSx5RkFBVUE7Ozs7Ozs7Ozs7Ozs7OztzREFHZiw4REFBQ1Isa0RBQUlBOzRDQUFDbUksTUFBSzs0Q0FBSU0sS0FBSTs0Q0FBWUMsVUFBVTtzREFDdkMsNEVBQUMzSSxtREFBS0E7Z0RBQ0o0SSxLQUNFZixrQkFDSSxHQUFHZ0Isa0RBQWlDLENBQUMsaUNBQWlDLENBQUMsR0FDdkVsQixhQUNBeEQsYUFDRSxHQUFHMEUsa0RBQWlDLENBQUMsc0JBQXNCLENBQUMsR0FDNUQsR0FBR0Esa0RBQWlDLENBQUMsaUNBQWlDLENBQUMsR0FDekUsR0FBR0Esa0RBQWlDLENBQUMsc0JBQXNCLENBQUM7Z0RBRWxFRyxPQUFPO2dEQUNQQyxRQUFRO2dEQUNSQyxLQUFJO2dEQUNKQyxPQUFNO2dEQUNObEIsV0FBVTtnREFDVm1CLFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUlkLDhEQUFDbEI7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNvQjt3Q0FBR3BCLFdBQVU7OzBEQUNaLDhEQUFDUTtnREFBR1IsV0FBVTswREFDWiw0RUFBQ2hJLGtEQUFJQTtvREFDSG1JLE1BQUs7b0RBQ0xrQixRQUFRO29EQUNSckIsV0FBVTtvREFDVlUsVUFBVTs4REFDWDs7Ozs7Ozs7Ozs7MERBNkdILDhEQUFDRjswREFDRSxDQUFDNUUsc0JBQ0EsOERBQUN5RTtvREFDQyxXQUFXO29EQUNYTCxXQUFXLENBQUMsa0dBQWtHLEVBQzVHSixrQkFDSSxlQUNBRixhQUNBeEQsYUFDRSxlQUNBLGVBQ0YsY0FDSjtvREFDRnFFLFNBQVN0RTtvREFDVHlFLFVBQVU7O3NFQUVWLDhEQUFDckksb0dBQUlBOzREQUFDaUosTUFBTTs7Ozs7O3NFQUVaLDhEQUFDbEI7NERBQUtKLFdBQVU7c0VBQWtCOzs7Ozs7Ozs7OztnRUFFbENwRSxTQUFTQyxTQUFTLHVCQUNwQiw4REFBQ3dFO29EQUNDLFdBQVc7b0RBQ1hJLEtBQUk7b0RBQ0pULFdBQVcsQ0FBQyxrR0FBa0csRUFDNUdKLGtCQUNJLGVBQ0FGLGFBQ0F4RCxhQUNFLGVBQ0EsZUFDRixjQUNKO29EQUNGLDhEQUE4RDtvREFDOUR3RSxVQUFVOztzRUFFViw4REFBQ3JJLG9HQUFJQTs0REFBQ2lKLE1BQU07Ozs7Ozt3REFFWHpJLDBFQUFtQkEsQ0FBQzs7Ozs7OzhFQUd2Qiw4REFBQ2Isa0RBQUlBO29EQUNIbUksTUFBSztvREFDTEgsV0FBVyxDQUFDLDRHQUE0RyxFQUN0SEosa0JBQ0ksZUFDQUYsYUFDQXhELGFBQ0UsZUFDQSxlQUNGLGNBQ0o7b0RBQ0ZxRSxTQUFTdEU7b0RBQ1R5RSxVQUFVOztzRUFFViw4REFBQ3JJLG9HQUFJQTs0REFBQ2lKLE1BQU07Ozs7Ozt3REFBTTs7Ozs7Ozs7Ozs7OzBEQUt4Qiw4REFBQ2Q7Z0RBQUdSLFdBQVU7MERBQ1osNEVBQUNLO29EQUNDa0IsTUFBSztvREFDTHZCLFdBQVcsQ0FBQyw0R0FBNEcsRUFDdEhKLGtCQUNJLGVBQ0FGLGFBQ0F4RCxhQUNFLGVBQ0EsZUFDRixjQUNKO29EQUNGcUUsU0FBU3hHOzt3REFFUm9CLHdCQUNDLDhEQUFDcEQsbURBQUtBOzREQUNKNEksS0FBS3hGOzREQUNMOEYsS0FBSTs0REFDSkYsT0FBTzs0REFDUEMsUUFBUTs0REFDUkcsU0FBUTs7Ozs7c0ZBR1YsOERBQUNoSixxR0FBS0E7NERBQUNtSixNQUFNOzs7Ozs7d0RBRWRqRyxlQUFlQSxlQUFlO3NFQUMvQiw4REFBQ25ELDJHQUFXQTs0REFBQ29KLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUd2Qiw4REFBQ2Q7Z0RBQUdSLFdBQVU7O2tFQUVaLDhEQUFDSzt3REFDQ0MsY0FBVzt3REFDWE4sV0FBVyxDQUFDLDRGQUE0RixFQUN0R0osa0JBQ0ksZUFDQUYsYUFDQXhELGFBQ0UsZUFDQSxlQUNGLGNBQ0o7d0RBQ0ZxRSxTQUFTdkU7d0RBQ1QwRSxVQUFVO2tFQUVWLDRFQUFDdEksb0dBQUlBOzREQUFDa0osTUFBTTs7Ozs7Ozs7Ozs7a0VBR2QsOERBQUNqQjt3REFDQ0MsY0FBVzt3REFDWE4sV0FBVyxDQUFDLGtHQUFrRyxFQUM1R0osa0JBQ0ksZUFDQUYsYUFDQXhELGFBQ0UsZUFDQSxlQUNGLGNBQ0o7d0RBQ0ZxRSxTQUFTNUU7d0RBQ1QrRSxVQUFVO2tFQUVWLDRFQUFDdEksb0dBQUlBOzREQUFDa0osTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU92QixDQUFDNUIsNEJBQ0EsOERBQUNPO3dCQUNDRCxXQUFXLENBQUMsc0JBQXNCLEVBQ2hDSixrQkFDSSxvRUFDQSxrQ0FDSjtrQ0FFRiw0RUFBQ0s7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ2hJLGtEQUFJQTt3Q0FBQ21JLE1BQUs7d0NBQUlNLEtBQUk7d0NBQVlDLFVBQVU7a0RBQ3ZDLDRFQUFDM0ksbURBQUtBOzRDQUNKNEksS0FDRWYsa0JBQ0ksR0FBR2dCLGtEQUFpQyxDQUFDLGlDQUFpQyxDQUFDLEdBQ3ZFLEdBQUdBLGtEQUFpQyxDQUFDLHNCQUFzQixDQUFDOzRDQUVsRUcsT0FBTzs0Q0FDUEMsUUFBUTs0Q0FDUkMsS0FBSTs0Q0FDSkMsT0FBTTs0Q0FDTmxCLFdBQVU7NENBQ1ZtQixTQUFROzs7Ozs7Ozs7Ozs7Ozs7OzhDQUlkLDhEQUFDbEI7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNvQjt3Q0FBR3BCLFdBQVU7OzBEQUNaLDhEQUFDUTtnREFBR1IsV0FBVTs7a0VBQ1osOERBQUNLO3dEQUNDLFdBQVc7d0RBQ1hJLEtBQUk7d0RBQ0pULFdBQVcsQ0FBQyw4R0FBOEcsRUFDeEhKLGtCQUFrQixlQUFlLGNBQ2pDO3dEQUNGVyxTQUFTL0I7d0RBQ1Q4QixjQUFXOzswRUFFWCw4REFBQ3JJLDZIQUE0QkE7Z0VBQzNCcUosTUFBTTtnRUFDTnRCLFdBQVcsQ0FBQyxZQUFZLEVBQ3RCSixrQkFBa0IsZUFBZSxjQUNqQzs7Ozs7OzREQUNEOzs7Ozs7O2tFQUVMLDhEQUFDUTt3REFBS0osV0FBVTs7Ozs7O2tFQUNoQiw4REFBQ0k7d0RBQUtKLFdBQVU7a0VBQWdNOzs7Ozs7Ozs7Ozs7MERBS2xOLDhEQUFDUTswREFDRSxDQUFDNUUsc0JBQ0EsOERBQUN5RTtvREFDQyxXQUFXO29EQUNYTCxXQUFXLENBQUMsa0dBQWtHLEVBQzVHSixrQkFBa0IsZUFBZSxjQUNqQztvREFDRlcsU0FBU3RFO29EQUNUcUUsY0FBVzs7c0VBRVgsOERBQUNsSCx1RkFBU0E7NERBQ1JrSSxNQUFNOzREQUNOdEIsV0FBVyxDQUFDLFVBQVUsRUFDcEJKLGtCQUFrQixlQUFlLGNBQ2pDOzs7Ozs7c0VBR0osOERBQUNROzREQUFLSixXQUFVO3NFQUFrQjs7Ozs7Ozs7Ozs7Z0VBRWxDcEUsU0FBU0MsU0FBUyxTQUNwQixVQUFVO2dEQUNWLGdCQUFnQjtnREFDaEIsb0JBQW9CO2dEQUNwQixxT0FBcU87Z0RBQ3JPLG1FQUFtRTtnREFDbkUsSUFBSTtnREFDSixxQ0FBcUM7Z0RBQ3JDLE1BQU07Z0RBQ04sWUFBWTs4REFDWiw4REFBQ29FO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ0s7NERBQ0NMLFdBQVcsQ0FBQyx1SUFBdUksRUFDakpKLGtCQUNJLCtCQUNBLDhCQUNKOzREQUNGNEIsY0FBYyxJQUFNdEgsZUFBZTs0REFDbkN1SCxjQUFjLElBQU12SCxlQUFlO3NFQUVsQ3JCLDBFQUFtQkEsQ0FBQyxTQUNqQjZJLE9BQU8sSUFDUEMsaUJBQWlCOzs7Ozs7c0VBRXZCLDhEQUFDckksMkRBQWVBO3NFQUNiVyw2QkFDQyw4REFBQ1osa0RBQU1BLENBQUM0RyxHQUFHO2dFQUNUMkIsU0FBUztvRUFBRUMsU0FBUztvRUFBR0MsR0FBRztnRUFBRTtnRUFDNUJDLFNBQVM7b0VBQUVGLFNBQVM7b0VBQUdDLEdBQUc7Z0VBQUU7Z0VBQzVCRSxNQUFNO29FQUFFSCxTQUFTO29FQUFHQyxHQUFHO2dFQUFFO2dFQUN6QjlCLFdBQVU7MEVBRVRuSCwwRUFBbUJBLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBTTdCLDhEQUFDYixrREFBSUE7b0RBQ0htSSxNQUFLO29EQUNMSCxXQUFXLENBQUMsNEdBQTRHLEVBQ3RISixrQkFBa0IsZUFBZSxhQUNsQyxDQUFDLENBQUM7b0RBQ0hXLFNBQVN0RTtvREFDVHlFLFVBQVU7b0RBQ1ZKLGNBQVc7O3NFQUVYLDhEQUFDbEgsdUZBQVNBOzREQUFDa0ksTUFBTTs7Ozs7O3NFQUNqQiw4REFBQ2xCOzREQUFLSixXQUFVO3NFQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSXhDLDhEQUFDUTtnREFBR1IsV0FBVTswREFDWiw0RUFBQ0s7b0RBQ0NrQixNQUFLO29EQUNMdkIsV0FBVyxDQUFDLGtHQUFrRyxFQUM1R0osa0JBQWtCLGVBQWUsY0FDakM7b0RBQ0ZXLFNBQVN4RztvREFDVHVHLGNBQVc7OERBRVZuRix3QkFDQyw4REFBQzhFO3dEQUFJRCxXQUFVO2tFQUNiLDRFQUFDakksbURBQUtBOzREQUNKNEksS0FBS3hGOzREQUNMOEYsS0FBSTs0REFDSmdCLElBQUk7NERBQ0pqQyxXQUFVOzREQUNWa0MsT0FBTTs0REFDTmYsU0FBUTs7Ozs7Ozs7OztrRkFJWiw4REFBQ2hKLHFHQUFLQTt3REFBQ21KLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7MERBSW5CLDhEQUFDZDtnREFBR1IsV0FBVTswREFVWiw0RUFBQ0s7b0RBQ0NDLGNBQVc7b0RBQ1hOLFdBQVcsQ0FBQywrRUFBK0UsRUFDekZKLGtCQUFrQixlQUFlLGNBQ2pDO29EQUNGVyxTQUFTdkU7OERBRVQsNEVBQUM1RCxvR0FBSUE7d0RBQUNrSixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXNUIsOERBQUMzSSxpR0FBTUE7Z0JBQ0x3SixNQUFNdEQ7Z0JBQ051RCxTQUFTckQsYUFBYTtnQkFDdEJpQixXQUFVO2dCQUNWcUMsUUFBTzswQkFFUCw0RUFBQzVKLDhGQUFHQTtvQkFDRjZKLElBQUk7d0JBQUV2QixPQUFPO29CQUFJO29CQUNqQmxGLE1BQUs7b0JBQ0wwRyxjQUFjO29CQUNkaEMsU0FBU3hCLGFBQWE7OEJBRXRCLDRFQUFDbkcsZ0dBQUtBO3dCQUNKMEosSUFBSTs0QkFDRnZCLE9BQU87NEJBQ1B5QixZQUFZOzRCQUNaRCxjQUFjOzRCQUNkdkIsUUFBUTs0QkFDUnlCLFdBQVc7d0JBQ2I7d0JBQ0FBLFdBQVc7d0JBQ1hGLGNBQWM7OzBDQUVkLDhEQUFDdkssa0RBQUlBO2dDQUNIbUksTUFBSztnQ0FDTE0sS0FBSTtnQ0FDSlQsV0FBVTtnQ0FDVlUsVUFBVTswQ0FFViw0RUFBQzNJLG1EQUFLQTtvQ0FDSjRJLEtBQUssR0FBR0Msa0RBQWlDLENBQUMsc0JBQXNCLENBQUM7b0NBQ2pFRyxPQUFPO29DQUNQQyxRQUFRO29DQUNSQyxLQUFJO29DQUNKQyxPQUFNO29DQUNObEIsV0FBVTtvQ0FDVm1CLFNBQVE7Ozs7Ozs7Ozs7OzBDQUdaLDhEQUFDZDtnQ0FDQ0wsV0FBVTtnQ0FDVk8sU0FBU3hCLGFBQWE7MENBRXRCLDRFQUFDekcsaUdBQUNBO29DQUFDZ0osTUFBTTs7Ozs7Ozs7Ozs7MENBRVgsOERBQUM1SSxrR0FBT0E7Ozs7OzBDQUNSLDhEQUFDdUg7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDMEM7d0NBQ0NDLFNBQVE7d0NBQ1IzQyxXQUFVOzswREFFViw4REFBQzdILHFHQUFLQTtnREFBQ21KLE1BQU07Ozs7Ozs0Q0FBTTs7Ozs7OztrREFFckIsOERBQUNqQjt3Q0FDQ2tCLE1BQUs7d0NBQ0x2QixXQUFVO3dDQUNWTyxTQUFTeEc7OzRDQUVSb0Isd0JBQ0MsOERBQUNwRCxtREFBS0E7Z0RBQ0o0SSxLQUFLeEY7Z0RBQ0w4RixLQUFJO2dEQUNKRixPQUFPO2dEQUNQQyxRQUFRO2dEQUNSRyxTQUFROzs7OzswRUFHViw4REFBQ2hKLHFHQUFLQTtnREFBQ21KLE1BQU07Ozs7Ozs0Q0FFZGpHLGVBQWVBLGVBQWU7MERBQy9CLDhEQUFDbkQsMkdBQVdBO2dEQUFDb0osTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQW9CdkIsOERBQUNyQjtnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ2hJLGtEQUFJQTtvQ0FDSG1JLE1BQUs7b0NBQ0xNLEtBQUk7b0NBQ0pULFdBQVU7b0NBQ1ZVLFVBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRVCw4REFBQ2hIO2dCQUNDa0osT0FBT2hFO2dCQUNQdUQsTUFBTS9EO2dCQUNORSx3QkFBd0JBOzs7Ozs7WUFFekI3RCw0QkFDQyw4REFBQ3pCLHdEQUFTQTtnQkFDUjZKLFFBQVFwSTtnQkFDUmtCLFlBQVlBO2dCQUNaYyxrQkFBa0JBO2dCQUNsQlIsa0JBQWtCQTtnQkFDbEJTLGlCQUFpQkE7Ozs7OztZQUdwQi9CLGtDQUNDLDhEQUFDMUIsOERBQWVBO2dCQUNkNEosUUFBUWxJO2dCQUNSZ0IsWUFBWUs7Z0JBQ1pTLGtCQUFrQkE7Z0JBQ2xCUixrQkFBa0JBO2dCQUNsQlMsaUJBQWlCQTs7Ozs7OzBCQUlyQiw4REFBQy9DO2dCQUNDYyxZQUFZUTtnQkFDWlUsWUFBWUQ7Z0JBQ1plLGtCQUFrQkE7Z0JBQ2xCQyxpQkFBaUJBOzs7Ozs7MEJBRW5CLDhEQUFDakQ7Z0JBQVdvSixRQUFReEk7Z0JBQWdCK0gsU0FBU25HOzs7Ozs7MEJBRTdDLDhEQUFDMUM7Z0JBQ0NNLGtCQUFrQkE7Z0JBQ2xCRyx5QkFBeUJBO2dCQUN6QjJDLGVBQWVBOzs7Ozs7OztBQUl2QjtBQUVBLGlFQUFlcEUsbURBQU9BLENBQUMsSUFBTXVLLFFBQVFDLE9BQU8sQ0FBQ25KLFNBQVM7SUFBRUosS0FBSztBQUFNLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQT09KQVxcTWl4ZG9ybVxcTWl4ZG9ybS1XZWItMi4wXFxmcm9udGVuZFxcY29tcG9uZW50c1xcbmF2YmFyXFxuYXZiYXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qIGVzbGludC1kaXNhYmxlIHJlYWN0L25vLXVua25vd24tcHJvcGVydHkgKi9cclxuXHJcblwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcbmltcG9ydCB7IE1kT3V0bGluZU5vdGlmaWNhdGlvbnNBY3RpdmUgfSBmcm9tIFwicmVhY3QtaWNvbnMvbWRcIjtcclxuaW1wb3J0IHsgQ2hldnJvbkRvd24sIEdsb2JlLCBHcmlwLCBVc2VyLCBYIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgZHluYW1pYyBmcm9tIFwibmV4dC9keW5hbWljXCI7XHJcbmltcG9ydCB7IEhpTWVudUFsdDMgfSBmcm9tIFwicmVhY3QtaWNvbnMvaGlcIjtcclxuaW1wb3J0IHsgQm94LCBEaXZpZGVyLCBEcmF3ZXIsIFBhcGVyIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgZ2V0SXRlbUxvY2FsU3RvcmFnZSB9IGZyb20gXCJAL3V0aWxzL2Jyb3dzZXJTZXR0aW5nXCI7XHJcbmltcG9ydCB7IHVzZU5hdmJhciB9IGZyb20gXCIuLi9ob21lL25hdmJhckNvbnRleHRcIjtcclxuLy8gaW1wb3J0IHsgVG9hc3RlciwgdG9hc3QgfSBmcm9tICdzb25uZXInXHJcbmltcG9ydCB7IGdldFByb3BlcnR5Q291bnRBcGkgfSBmcm9tIFwiQC9zZXJ2aWNlcy93ZWJmbG93U2VydmljZXNcIjtcclxuaW1wb3J0IE1lbnVQb3B1cCBmcm9tIFwiLi4vcG9wdXAvbWVudVBvcHVwXCI7XHJcbmltcG9ydCBNZW51UG9wdXBNb2JpbGUgZnJvbSBcIi4uL3BvcHVwL21lbnVQb3B1cE1vYmlsZVwiO1xyXG5pbXBvcnQgdG9hc3QgZnJvbSBcIi4uL3RvYXN0L3RvYXN0XCI7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L3JvdXRlclwiO1xyXG5pbXBvcnQgeyBGYVJlZ1VzZXIgfSBmcm9tIFwicmVhY3QtaWNvbnMvZmFcIjtcclxuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xyXG4vLyBpbXBvcnQgeyBQaUhhbmRDb2luc0ZpbGwgfSBmcm9tIFwicmVhY3QtaWNvbnMvcGlcIjtcclxuXHJcbi8vIGltcG9ydCBheGlvcyBmcm9tIFwiYXhpb3NcIjtcclxuXHJcbmNvbnN0IENvdW50cnlNb2RhbCA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KFwiLi4vbW9kZWwvY291bnRyeU1vZGVsXCIpLCB7XHJcbiAgc3NyOiBmYWxzZSxcclxufSk7XHJcbmNvbnN0IExvZ2luUG9wdXAgPSBkeW5hbWljKCgpID0+IGltcG9ydChcIi4uL3BvcHVwL2xvZ2luUG9wdXBcIiksIHsgc3NyOiBmYWxzZSB9KTtcclxuLy8gY29uc3QgTWVudVBvcHVwID0gZHluYW1pYygoKSA9PiBpbXBvcnQoXCIuLi9wb3B1cC9tZW51UG9wdXBcIiksIHsgc3NyOiBmYWxzZSB9KTtcclxuY29uc3QgTm90aWNlYm9hcmQgPSBkeW5hbWljKCgpID0+IGltcG9ydChcIi4uL21vZGVsL25vdGljZWJvYXJkXCIpLCB7XHJcbiAgc3NyOiBmYWxzZSxcclxufSk7XHJcbmNvbnN0IE15UHJvZmlsZSA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KFwiLi4vbW9kZWwvbXlQcm9maWxlXCIpLCB7IHNzcjogZmFsc2UgfSk7XHJcblxyXG5jb25zdCBOYXZiYXIgPSAoKSA9PiB7XHJcbiAgY29uc3QgW29wZW5Db3VudHJ5TW9kYWwsIHNldE9wZW5Db3VudHJ5TW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGhhbmRsZU9wZW5Db3VudHJ5TW9kYWwgPSAoKSA9PiBzZXRPcGVuQ291bnRyeU1vZGFsKHRydWUpO1xyXG4gIGNvbnN0IGhhbmRsZUNsb3NlQ291bnRyeU1vZGFsID0gKCkgPT4gc2V0T3BlbkNvdW50cnlNb2RhbChmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dUb29sdGlwLCBzZXRTaG93VG9vbHRpcF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Njcm9sbGVkLCBzZXRTY3JvbGxlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dMb2dpblBvcHVwLCBzZXRTaG93TG9naW5Qb3B1cF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2hvdmVyaW5nVG9wLCBzZXRIb3ZlcmluZ1RvcF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTWVudU9wZW4sIHNldElzTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc01lbnVPcGVuTW9iaWxlLCBzZXRJc01lbnVPcGVuTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcclxuICBjb25zdCBbaGFzVG9rZW4sIHNldEhhc1Rva2VuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcclxuICBjb25zdCBbcm9sZXMsIHNldFJvbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc015UHJvZmlsZU9wZW4sIHNldElzTXlQcm9maWxlT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2ZsYWdVcmwsIHNldEZsYWdVcmxdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2N1cnJlbmN5Q29kZSwgc2V0Q3VycmVuY3lDb2RlXSA9IHVzZVN0YXRlKFwiXCIpO1xyXG4gIGNvbnN0IFtpc1BvcHVwT3Blbiwgc2V0SXNQb3B1cE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIC8vIGNvbnN0IFt0aW1lTGVmdCwgc2V0VGltZUxlZnRdID0gdXNlU3RhdGUoMiAqIDI0ICogNjAgKiA2MCk7IC8vIDIgZGF5cyBpbiBzZWNvbmRzXHJcbiAgY29uc3QgcG9wdXBSZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgLy8gY29uc3QgY29udGFpbmVyUmVmID0gdXNlUmVmKG51bGwpO1xyXG5cclxuICBjb25zdCB0b2dnbGVNeVByb2ZpbGUgPSAoKSA9PiBzZXRJc015UHJvZmlsZU9wZW4oIWlzTXlQcm9maWxlT3Blbik7XHJcbiAgY29uc3QgdG9nZ2xlTWVudSA9ICgpID0+IHtcclxuICAgIGlmICghdG9rZW4gfHwgcm9sZSAhPT0gXCJ1c2VyXCIpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJQbGVhc2UgTG9naW4gIVwiLCB7XHJcbiAgICAgICAgc3ViVGV4dDogXCJZb3UgbmVlZCB0byBiZSBMb2dnZWQgSW5cIixcclxuICAgICAgfSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRJc01lbnVPcGVuKCFpc01lbnVPcGVuKTtcclxuICAgIH1cclxuICB9O1xyXG4gIGNvbnN0IHRvZ2dsZU1lbnVNb2JpbGUgPSAoKSA9PiB7XHJcbiAgICBpZiAoIXRva2VuIHx8IHJvbGUgIT09IFwidXNlclwiKSB7XHJcbiAgICAgIHRvYXN0LmVycm9yKFwiUGxlYXNlIExvZ2luICFcIiwge1xyXG4gICAgICAgIHN1YlRleHQ6IFwiWW91IG5lZWQgdG8gYmUgTG9nZ2VkIEluXCIsXHJcbiAgICAgIH0pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0SXNNZW51T3Blbk1vYmlsZSghaXNNZW51T3Blbk1vYmlsZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuICAvLyBjb25zdCB0b2dnbGVNZW51TW9iaWxlID0gKCkgPT4gc2V0SXNNZW51T3Blbk1vYmlsZSghaXNNZW51T3Blbk1vYmlsZSk7XHJcbiAgY29uc3QgdG9nZ2xlTG9naW5Qb3B1cCA9ICgpID0+IHNldFNob3dMb2dpblBvcHVwKCFzaG93TG9naW5Qb3B1cCk7XHJcbiAgLy8gY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGUobnVsbCk7XHJcbiAgY29uc3QgW2lzU2Nyb2xsZWQsIHNldElzU2Nyb2xsZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlUm91dGVDaGFuZ2UgPSAoKSA9PiB7XHJcbiAgICAgIGlmIChpc01lbnVPcGVuKSB0b2dnbGVNZW51KGZhbHNlKTtcclxuICAgICAgaWYgKGlzTWVudU9wZW5Nb2JpbGUpIHRvZ2dsZU1lbnVNb2JpbGUoZmFsc2UpO1xyXG4gICAgfTtcclxuXHJcbiAgICByb3V0ZXIuZXZlbnRzLm9uKFwicm91dGVDaGFuZ2VTdGFydFwiLCBoYW5kbGVSb3V0ZUNoYW5nZSk7XHJcblxyXG4gICAgLy8gQ2xlYW51cFxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgcm91dGVyLmV2ZW50cy5vZmYoXCJyb3V0ZUNoYW5nZVN0YXJ0XCIsIGhhbmRsZVJvdXRlQ2hhbmdlKTtcclxuICAgIH07XHJcbiAgfSwgW2lzTWVudU9wZW4sIGlzTWVudU9wZW5Nb2JpbGVdKTtcclxuXHJcbiAgY29uc3QgdXBkYXRlVG9rZW5TdGF0ZSA9ICgpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0SXRlbUxvY2FsU3RvcmFnZShcInRva2VuXCIpO1xyXG4gICAgc2V0SGFzVG9rZW4oISF0b2tlbik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdXBkYXRlUm9sZVN0YXRlID0gKCkgPT4ge1xyXG4gICAgY29uc3Qgcm9sZSA9IGdldEl0ZW1Mb2NhbFN0b3JhZ2UoXCJyb2xlXCIpO1xyXG4gICAgc2V0Um9sZShyb2xlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGVDb3VudHJ5ID0gKCkgPT4ge1xyXG4gICAgY29uc3QgZmxhZyA9IGdldEl0ZW1Mb2NhbFN0b3JhZ2UoXCJzZWxlY3RlZENvdW50cnlGbGFnXCIpO1xyXG4gICAgY29uc3QgY29kZSA9IGdldEl0ZW1Mb2NhbFN0b3JhZ2UoXCJzZWxlY3RlZEN1cnJlbmN5Q29kZVwiKTtcclxuXHJcbiAgICBzZXRGbGFnVXJsKGZsYWcpO1xyXG4gICAgc2V0Q3VycmVuY3lDb2RlKGNvZGUpO1xyXG4gICAgdXBkYXRlQ291bnRyeTIoZmxhZywgY29kZSk7XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHVwZGF0ZVRva2VuU3RhdGUoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICB1cGRhdGVSb2xlU3RhdGUoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVTdG9yYWdlQ2hhbmdlID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwidG9rZW5cIikge1xyXG4gICAgICAgIHVwZGF0ZVRva2VuU3RhdGUoKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInN0b3JhZ2VcIiwgaGFuZGxlU3RvcmFnZUNoYW5nZSk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xyXG4gICAgfTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVTdG9yYWdlQ2hhbmdlID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwicm9sZVwiKSB7XHJcbiAgICAgICAgdXBkYXRlUm9sZVN0YXRlKCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwic3RvcmFnZVwiLCBoYW5kbGVTdG9yYWdlQ2hhbmdlKTtcclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3Qgb3JpZ2luYWxTZXRJdGVtID0gbG9jYWxTdG9yYWdlLnNldEl0ZW07XHJcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSA9IGZ1bmN0aW9uIChrZXkpIHtcclxuICAgICAgY29uc3QgZXZlbnQgPSBuZXcgRXZlbnQoXCJpdGVtSW5zZXJ0ZWRcIik7XHJcbiAgICAgIG9yaWdpbmFsU2V0SXRlbS5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xyXG4gICAgICBpZiAoa2V5ID09PSBcInRva2VuXCIpIHtcclxuICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChldmVudCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaGFuZGxlSXRlbUluc2VydGVkID0gKCkgPT4ge1xyXG4gICAgICB1cGRhdGVUb2tlblN0YXRlKCk7XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwiaXRlbUluc2VydGVkXCIsIGhhbmRsZUl0ZW1JbnNlcnRlZCk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJpdGVtSW5zZXJ0ZWRcIiwgaGFuZGxlSXRlbUluc2VydGVkKTtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0gPSBvcmlnaW5hbFNldEl0ZW07XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IG9yaWdpbmFsU2V0SXRlbSA9IGxvY2FsU3RvcmFnZS5zZXRJdGVtO1xyXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0gPSBmdW5jdGlvbiAoa2V5KSB7XHJcbiAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEV2ZW50KFwiaXRlbUluc2VydGVkXCIpO1xyXG4gICAgICBvcmlnaW5hbFNldEl0ZW0uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcclxuICAgICAgaWYgKGtleSA9PT0gXCJ0b2tlblwiKSB7XHJcbiAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGhhbmRsZUl0ZW1JbnNlcnRlZCA9ICgpID0+IHtcclxuICAgICAgdXBkYXRlUm9sZVN0YXRlKCk7XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwiaXRlbUluc2VydGVkXCIsIGhhbmRsZUl0ZW1JbnNlcnRlZCk7XHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJpdGVtSW5zZXJ0ZWRcIiwgaGFuZGxlSXRlbUluc2VydGVkKTtcclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0gPSBvcmlnaW5hbFNldEl0ZW07XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcclxuICAgICAgc2V0U2Nyb2xsZWQod2luZG93LnNjcm9sbFkgPiAwKTtcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgaGFuZGxlTW91c2VNb3ZlID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIHNldEhvdmVyaW5nVG9wKGV2ZW50LmNsaWVudFkgPCA4NSk7XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGhhbmRsZVNjcm9sbCk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcIm1vdXNlbW92ZVwiLCBoYW5kbGVNb3VzZU1vdmUpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGhhbmRsZVNjcm9sbCk7XHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwibW91c2Vtb3ZlXCIsIGhhbmRsZU1vdXNlTW92ZSk7XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHNlbGVjdGVkQ291bnRyeUZsYWcgPSBnZXRJdGVtTG9jYWxTdG9yYWdlKFwic2VsZWN0ZWRDb3VudHJ5RmxhZ1wiKTtcclxuICAgIGNvbnN0IHNlbGVjdGVkQ3VycmVuY3lDb2RlID0gZ2V0SXRlbUxvY2FsU3RvcmFnZShcInNlbGVjdGVkQ3VycmVuY3lDb2RlXCIpO1xyXG5cclxuICAgIGlmIChzZWxlY3RlZENvdW50cnlGbGFnKSB7XHJcbiAgICAgIGNvbnN0IHVybCA9IHNlbGVjdGVkQ291bnRyeUZsYWc7XHJcbiAgICAgIHNldEZsYWdVcmwodXJsKTtcclxuICAgICAgc2V0Q3VycmVuY3lDb2RlKHNlbGVjdGVkQ3VycmVuY3lDb2RlKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcbiAgLy8gTm90aWNlIEJvYXJkIE1vZGFsXHJcbiAgY29uc3QgW29wZW5Ob3RpY2VCb2FyZCwgc2V0T3Blbk5vdGljZUJvYXJkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbb3Blbk5vdGljZUJvYXJkRGV0YWlscywgc2V0T3Blbk5vdGljZUJvYXJkRGV0YWlsc10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZU9wZW5Ob3RpY2VCb2FyZCA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmIChnZXRJdGVtTG9jYWxTdG9yYWdlKFwidG9rZW5cIikgJiYgcm9sZSA9PT0gXCJ1c2VyXCIpIHtcclxuICAgICAgY29uc3QgcHJvcGVydHlDb3VudFJlc3BvbnNlID0gYXdhaXQgZ2V0UHJvcGVydHlDb3VudEFwaSgpO1xyXG4gICAgICBpZiAocHJvcGVydHlDb3VudFJlc3BvbnNlPy5kYXRhPy5kYXRhPy50b3RhbEJvb2tpbmcgPiAwKSB7XHJcbiAgICAgICAgc2V0T3Blbk5vdGljZUJvYXJkRGV0YWlscyh0cnVlKTtcclxuICAgICAgfSBlbHNlIGlmIChwcm9wZXJ0eUNvdW50UmVzcG9uc2U/LmRhdGE/LmRhdGE/LnRvdGFsQm9va2luZyA9PT0gMCkge1xyXG4gICAgICAgIHNldE9wZW5Ob3RpY2VCb2FyZCh0cnVlKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJQbGVhc2UgTG9naW4gIVwiLCB7XHJcbiAgICAgICAgc3ViVGV4dDogXCJZb3UgbmVlZCB0byBiZSBMb2dnZWQgSW5cIixcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuICBjb25zdCBoYW5kbGVDbG9zZU5vdGljZUJvYXJkID0gKCkgPT4ge1xyXG4gICAgc2V0T3Blbk5vdGljZUJvYXJkKGZhbHNlKTtcclxuICAgIHNldE9wZW5Ob3RpY2VCb2FyZERldGFpbHMoZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIC8vIE1vYmlsZSBEcmF3ZXJcclxuICBjb25zdCBbb3BlbkRyYXdlciwgc2V0T3BlbkRyYXdlcl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IHRvZ2dsZURyYXdlciA9IChuZXdPcGVuKSA9PiAoKSA9PiB7XHJcbiAgICBzZXRPcGVuRHJhd2VyKG5ld09wZW4pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHsgdXBkYXRlQ291bnRyeTIsIHRva2VuLCByb2xlIH0gPSB1c2VOYXZiYXIoKTtcclxuXHJcbiAgLy8gVXBkYXRlIGNvdW50cnkgd2hlbiB0aGUgY29tcG9uZW50IGlzIG1vdW50ZWRcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3Qgc2VsZWN0ZWRDb3VudHJ5RmxhZyA9IGdldEl0ZW1Mb2NhbFN0b3JhZ2UoXCJzZWxlY3RlZENvdW50cnlGbGFnXCIpO1xyXG4gICAgY29uc3Qgc2VsZWN0ZWRDdXJyZW5jeUNvZGUgPSBnZXRJdGVtTG9jYWxTdG9yYWdlKFwic2VsZWN0ZWRDdXJyZW5jeUNvZGVcIik7XHJcblxyXG4gICAgaWYgKHNlbGVjdGVkQ291bnRyeUZsYWcgJiYgc2VsZWN0ZWRDdXJyZW5jeUNvZGUpIHtcclxuICAgICAgdXBkYXRlQ291bnRyeTIoc2VsZWN0ZWRDb3VudHJ5RmxhZywgc2VsZWN0ZWRDdXJyZW5jeUNvZGUpO1xyXG4gICAgfVxyXG4gICAgc2V0RmxhZ1VybChzZWxlY3RlZENvdW50cnlGbGFnKTtcclxuICAgIHNldEN1cnJlbmN5Q29kZShzZWxlY3RlZEN1cnJlbmN5Q29kZSk7XHJcbiAgfSwgW3VwZGF0ZUNvdW50cnkyXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVTY3JvbGwgPSAoKSA9PiB7XHJcbiAgICAgIGlmICh3aW5kb3cuaW5uZXJXaWR0aCA8IDc2OCkgcmV0dXJuO1xyXG4gICAgICBpZiAod2luZG93LnNjcm9sbFkgPiA5MDApIHtcclxuICAgICAgICBzZXRJc1Njcm9sbGVkKHRydWUpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldElzU2Nyb2xsZWQoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGhhbmRsZVNjcm9sbCk7XHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgaGFuZGxlU2Nyb2xsKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIEF1dG8gc2hvdyBwb3B1cCBvbiBsb2FkLCBjbG9zZSBhZnRlciA0IHNlY29uZHNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0SXNQb3B1cE9wZW4odHJ1ZSk7XHJcbiAgICBjb25zdCBjbG9zZVRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgIHNldElzUG9wdXBPcGVuKGZhbHNlKTtcclxuICAgIH0sIDQwMDApO1xyXG4gICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dChjbG9zZVRpbWVyKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIENvdW50ZG93biB0aW1lclxyXG4gIC8vIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgLy8gICBpZiAoIWlzUG9wdXBPcGVuKSByZXR1cm47XHJcblxyXG4gIC8vICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XHJcbiAgLy8gICAgIHNldFRpbWVMZWZ0KChwcmV2KSA9PiAocHJldiA+IDAgPyBwcmV2IC0gMSA6IDApKTtcclxuICAvLyAgIH0sIDEwMDApO1xyXG4gIC8vICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xyXG4gIC8vIH0sIFtpc1BvcHVwT3Blbl0pO1xyXG5cclxuICAvLyBDbG9zZSBvbiBvdXRzaWRlIGNsaWNrXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZ1bmN0aW9uIGhhbmRsZUNsaWNrT3V0c2lkZShldmVudCkge1xyXG4gICAgICBpZiAocG9wdXBSZWYuY3VycmVudCAmJiAhcG9wdXBSZWYuY3VycmVudC5jb250YWlucyhldmVudC50YXJnZXQpKSB7XHJcbiAgICAgICAgc2V0SXNQb3B1cE9wZW4oZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGlzUG9wdXBPcGVuKSB7XHJcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJtb3VzZWRvd25cIiwgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJtb3VzZWRvd25cIiwgaGFuZGxlQ2xpY2tPdXRzaWRlKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLCBoYW5kbGVDbGlja091dHNpZGUpO1xyXG4gIH0sIFtpc1BvcHVwT3Blbl0pO1xyXG5cclxuICAvLyBGb3JtYXQgdGltZSBsZWZ0IGFzIGhoOm1tOnNzXHJcbiAgLy8gY29uc3QgZm9ybWF0VGltZSA9IChzZWNvbmRzKSA9PiB7XHJcbiAgLy8gICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcihzZWNvbmRzIC8gKDI0ICogMzYwMCkpO1xyXG4gIC8vICAgY29uc3QgaHJzID0gTWF0aC5mbG9vcigoc2Vjb25kcyAlICgyNCAqIDM2MDApKSAvIDM2MDApO1xyXG4gIC8vICAgY29uc3QgbWlucyA9IE1hdGguZmxvb3IoKHNlY29uZHMgJSAzNjAwKSAvIDYwKTtcclxuICAvLyAgIGNvbnN0IHNlY3MgPSBzZWNvbmRzICUgNjA7XHJcblxyXG4gIC8vICAgcmV0dXJuIGAke2RheXN9ZDoke2hycy50b1N0cmluZygpLnBhZFN0YXJ0KDIsIFwiMFwiKX06JHttaW5zXHJcbiAgLy8gICAgIC50b1N0cmluZygpXHJcbiAgLy8gICAgIC5wYWRTdGFydCgyLCBcIjBcIil9OiR7c2Vjcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsIFwiMFwiKX1gO1xyXG4gIC8vIH07XHJcblxyXG4gIC8vICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAvLyAgIGNvbnN0IGZldGNoVXNlckRhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgLy8gICAgIHRyeSB7XHJcbiAgLy8gICAgICAgY29uc3QgcmVzID0gYXdhaXQgYXhpb3MuZ2V0KFwiL2FwaS91c2VyL21lXCIsIHtcclxuICAvLyAgICAgICAgIGhlYWRlcnM6IHtcclxuICAvLyAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgLy8gICAgICAgICB9LFxyXG4gIC8vICAgICAgIH0pO1xyXG4gIC8vICAgICAgIHNldFVzZXIocmVzLmRhdGEpOyAvLyBNYWtlIHN1cmUgdGhlIEFQSSByZXR1cm5zIHsgbmFtZTogXCJVc2VyIE5hbWVcIiwgLi4uIH1cclxuICAvLyAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAvLyAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgdXNlciBkYXRhOlwiLCBlcnJvcik7XHJcbiAgLy8gICAgIH1cclxuICAvLyAgIH07XHJcblxyXG4gIC8vICAgaWYgKHRva2VuKSBmZXRjaFVzZXJEYXRhKCk7XHJcbiAgLy8gfSwgW3Rva2VuXSk7XHJcblxyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IGlzSG9tZVBhZ2UgPSByb3V0ZXIucGF0aG5hbWUgPT09IFwiL1wiO1xyXG4gIGNvbnN0IGlzVG9wSG9zdGVsUGFnZSA9XHJcbiAgICByb3V0ZXIucGF0aG5hbWUgPT09IFwiL3RvcGhvc3RlbFwiIHx8XHJcbiAgICByb3V0ZXIucGF0aG5hbWUgPT09IFwiL2V4cGxvcmV3b3JsZFwiIHx8XHJcbiAgICByb3V0ZXIucGF0aG5hbWUgPT09IFwiL2ZlYXR1cmVkaG9zdGVsXCIgfHxcclxuICAgIHJvdXRlci5wYXRobmFtZSA9PT0gXCIvdHJhdmVsYWN0aXZpdHlcIiB8fFxyXG4gICAgcm91dGVyLnBhdGhuYW1lID09PSBcIi9tZWV0YnVkZGllc1wiIHx8XHJcbiAgICByb3V0ZXIucGF0aG5hbWUgPT09IFwiL2Rpc2NvdmVyLWV2ZW50XCIgfHxcclxuICAgIHJvdXRlci5wYXRobmFtZSA9PT0gXCIvc2VhcmNoXCI7XHJcblxyXG4gIGNvbnNvbGUubG9nKFwidG9rZW5cIiwgdG9rZW4pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgey8qIDxUb2FzdGVyIHJpY2hDb2xvcnMgcG9zaXRpb249XCJ0b3AtcmlnaHRcIiAvPiAqL31cclxuICAgICAgPHNlY3Rpb25cclxuICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IHN0aWNreSB0b3AtMCB6LTQwIG92ZXJmbG93LXZpc2libGUgJHtcclxuICAgICAgICAgIHNjcm9sbGVkICYmICFob3ZlcmluZ1RvcCA/IFwiLXRvcC0xMFwiIDogXCJzdGlja3kgdG9wLTBcIlxyXG4gICAgICAgIH1gfVxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTUgcHktMi41IGJnLWJsYWNrIHctZnVsbCBtaW4taC1bNDRweF0nPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPSd0ZXh0LXdoaXRlIHhzOnRleHQtc20gdGV4dC14cyBsZWFkaW5nLXRpZ2h0IHRleHQtY2VudGVyJz5cclxuICAgICAgICAgICAgR2V0IDEgTW9udGggRnJlZVxyXG4gICAgICAgICAgICA8TGluayBocmVmPXtcIi9teS1wcm9maWxlP3NlY3Rpb249bWVtYmVyc2hpcFwifT5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtcHJpbWFyeS1ibHVlJz4gTWl4IFByZW1pdW0gPC9zcGFuPlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIE1lbWJlcnNoaXAg4oCTIFNpZ24gVXAgTm93IVxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgeyF0b2tlbiB8fCByb2xlICE9PSBcInVzZXJcIiA/IChcclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT0nYWJzb2x1dGUgaW5zZXQtMCB3LWZ1bGwgaC1mdWxsJ1xyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9J0xvZ2luIHJlcXVpcmVkIHRvIGFjY2VzcyBNaXggUHJlbWl1bSBNZW1iZXJzaGlwJ1xyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiUGxlYXNlIExvZ2luICFcIiwge1xyXG4gICAgICAgICAgICAgICAgICBzdWJUZXh0OiBcIllvdSBuZWVkIHRvIGJlIExvZ2dlZCBJblwiLFxyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICkgOiBudWxsfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBoaWRkZW4gbGc6ZmxleCAke1xyXG4gICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICA/IFwiYWJzb2x1dGUgei01MCBiZy1ibGFjayBiZy1vcGFjaXR5LTEwIHNoYWRvdy1tZCBiYWNrZHJvcC1ibHVyLXNtXCJcclxuICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICA/IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgICAgID8gXCJiZy13aGl0ZSBzaGFkb3ctbWRcIlxyXG4gICAgICAgICAgICAgICAgOiBcImJnLXdoaXRlIGJnLW9wYWNpdHktMTAgZml4ZWQgdG9wLTEwIHotNTAgYmFja2Ryb3AtYmx1ci1zbVwiXHJcbiAgICAgICAgICAgICAgOiBcImJnLXRyYW5zcGFyZW50IGxnOmJnLXdoaXRlIHNoYWRvdy1tZFwiXHJcbiAgICAgICAgICB9YH1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncmVsYXRpdmUgei01MCBmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBweS00IGNvbnRhaW5lcic+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSd3LVs0NSVdIGZsZXggaXRlbXMtY2VudGVyIGdhcC14LTMnPlxyXG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9J21kOmhpZGRlbiBibG9jayc+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIC8vIGhyZWY9XCIjXCJcclxuICAgICAgICAgICAgICAgICAgcmVsPSdjYW5vbmljYWwnXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtMnhsIGZvbnQtbWFucm9wZSBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyICBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgICR7XHJcbiAgICAgICAgICAgICAgICAgICAgaXNUb3BIb3N0ZWxQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgID8gaXNTY3JvbGxlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZURyYXdlcih0cnVlKX1cclxuICAgICAgICAgICAgICAgICAgcHJlZmV0Y2g9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8SGlNZW51QWx0MyAvPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICA8TGluayBocmVmPScvJyByZWw9J2Nhbm9uaWNhbCcgcHJlZmV0Y2g9e2ZhbHNlfT5cclxuICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICBzcmM9e1xyXG4gICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgPyBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TM19VUkxfRkV9L2Zyb250LWltYWdlcy9NaXhkb3JtLXdoaXRlLUQuc3ZnYFxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBpc0hvbWVQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TM19VUkxfRkV9L2Zyb250LWltYWdlcy9sb2dvLnN2Z2BcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TM19VUkxfRkV9L2Zyb250LWltYWdlcy9NaXhkb3JtLXdoaXRlLUQuc3ZnYFxyXG4gICAgICAgICAgICAgICAgICAgICAgOiBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TM19VUkxfRkV9L2Zyb250LWltYWdlcy9sb2dvLnN2Z2BcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB3aWR0aD17MTU1fVxyXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ9ezQwfVxyXG4gICAgICAgICAgICAgICAgICBhbHQ9J01peGRvcm0nXHJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPSdNaXhkb3JtJ1xyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J21heC13LVsxMTBweF0gbWQ6bWF4LXctWzE1NXB4XSBtZDptYXgtaC0yNCByZWxhdGl2ZSB6LTUwIHctZml0IG9iamVjdC1jb250YWluIGJnLWJsZW5kLWNvbG9yLWJ1cm4gY3Vyc29yLXBvaW50ZXIgaG92ZXI6c2NhbGUtOTUgZHVyYXRpb24tNTAwIGVhc2UtaW4tb3V0J1xyXG4gICAgICAgICAgICAgICAgICBsb2FkaW5nPSdsYXp5J1xyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndy1bNTUlXSBmbGV4IGp1c3RpZnktZW5kIGl0ZW1zLWNlbnRlcic+XHJcbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT0nZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWQ6Z2FwLXgtNSBnYXAteC0zJz5cclxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9J21kOmJsb2NrIGhpZGRlbic+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj0nL293bmVyL2xpc3QteW91ci1ob3N0ZWwnXHJcbiAgICAgICAgICAgICAgICAgICAgcGFzc0hyZWZcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J3RleHQteHMgdGV4dC1jZW50ZXIgZm9udC1tYW5yb3BlIG1pbi13LVsxNDBweF0gdy1mdWxsIGJsb2NrIGZvbnQtYm9sZCBiZy1wcmltYXJ5LWJsdWUgY3Vyc29yLXBvaW50ZXIgcm91bmRlZC05eGwgdGV4dC1ibGFjayBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgcHgtNSBweS0zJ1xyXG4gICAgICAgICAgICAgICAgICAgIHByZWZldGNoPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIExpc3QgWW91ciBIb3N0ZWxcclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgIHsvKiA8bGkgY2xhc3NOYW1lPSdoaWRkZW4gbWQ6YmxvY2sgcmVsYXRpdmUnPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gaHJlZj1cIjBcIlxyXG4gICAgICAgICAgICAgICAgICAgIHJlbD0nY2Fub25pY2FsJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tYW5yb3BlIGl0ZW1zLWNlbnRlciBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgcm91bmRlZC05eGwgdGV4dC1ibGFjayBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgZ2FwLXgtMiBmbGV4ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtd2hpdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBpc1Njcm9sbGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU9wZW5Ob3RpY2VCb2FyZH1cclxuICAgICAgICAgICAgICAgICAgICBwcmVmZXRjaD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8TWRPdXRsaW5lTm90aWZpY2F0aW9uc0FjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb250LW5vcm1hbCB0ZXh0LWJsYWNrICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAvPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICBOb3RpY2Vib2FyZFxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdhYnNvbHV0ZSBsZWZ0LTQgdG9wLVstMTBweF0gbWluLWgtMy41IG1pbi13LTMuNSBtYXgtaC00LjUgbWF4LXctNC41IGFuaW1hdGUtcGluZyByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS1ibHVlJz48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWJzb2x1dGUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbGVmdC00IHRvcC1bLTEwcHhdIG1pbi1oLTMuNSBtaW4tdy0zLjUgbWF4LWgtNC41IG1heC13LTQuNSByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS1ibHVlIHRleHQtWzEwcHhdIGZvbnQtbWVkaXVtIHRleHQtYmxhY2sgdGV4dC1jZW50ZXIgbGVhZGluZy1ub25lJz5cclxuICAgICAgICAgICAgICAgICAgICAyXHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvbGk+ICovfVxyXG4gICAgICAgICAgICAgICAgey8qIDxsaSBjbGFzc05hbWU9J2hpZGRlbiBtZDpibG9jayByZWxhdGl2ZScgcmVmPXtjb250YWluZXJSZWZ9PlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1hbnJvcGUgaXRlbXMtY2VudGVyIGZvbnQtYm9sZCBjdXJzb3ItcG9pbnRlciByb3VuZGVkLTl4bCB0ZXh0LWJsYWNrIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBnYXAteC0yIGZsZXggJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogaXNIb21lUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtd2hpdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNQb3B1cE9wZW4odHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8UGlIYW5kQ29pbnNGaWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPXsyMH1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIFNldHRsZVxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdhYnNvbHV0ZSBsZWZ0LTQgdG9wLVstMTBweF0gbWluLWgtMy41IG1pbi13LTMuNSBtYXgtaC00LjUgbWF4LXctNC41IGFuaW1hdGUtcGluZyByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS1ibHVlJz48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWJzb2x1dGUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbGVmdC00IHRvcC1bLTEwcHhdIG1pbi1oLTMuNSBtaW4tdy0zLjUgbWF4LWgtNC41IG1heC13LTQuNSByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS1ibHVlIHRleHQtWzEwcHhdIGZvbnQtbWVkaXVtIHRleHQtYmxhY2sgdGV4dC1jZW50ZXIgbGVhZGluZy1ub25lJz5cclxuICAgICAgICAgICAgICAgICAgICAyXHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAge2lzUG9wdXBPcGVuICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICByZWY9e3BvcHVwUmVmfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSdhYnNvbHV0ZSB6LTUwIG10LTIgYmctd2hpdGUgcC01IHJvdW5kZWQteGwgc2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0xMDAgdy04MCdcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzUG9wdXBPcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSdhYnNvbHV0ZSB0b3AtMyByaWdodC0zIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMCB0ZXh0LXhsIHRyYW5zaXRpb24tY29sb3JzJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAmdGltZXM7XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J3RleHQtZ3JheS01MDAgdGV4dC14cyBmb250LW1lZGl1bSB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZSBtYi0xJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgU2V0dGxlIHlvdXIgYm9va2luZyBwYXltZW50IG5vdy5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J215LTMgZm9udC1tYW5yb3BlIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGhvdmVyOnRleHQtcHJpbWFyeS1ibHVlIGN1cnNvci1wb2ludGVyIHRleHQteGwgdHJhbnNpdGlvbi1jb2xvcnMnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPXtcIi9teS1wcm9maWxlP3NlY3Rpb249c3RheVwifT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBUYW1hc2hhIFVkYWlwdXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHktMiBib3JkZXItYiBib3JkZXItZ3JheS0xMDAnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3RleHQtc20gdGV4dC1ncmF5LTcwMCc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsdXggcm9vbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwJz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICDigrkxMjk5LjAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdtdC00IHAtMyBiZy1yZWQtNTAgcm91bmRlZC1sZyc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT0ndGV4dC1zbSB0ZXh0LXJlZC02MDAgZm9udC1zZW1pYm9sZCB0ZXh0LWNlbnRlcic+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgVGltZSBsZWZ0OiB7Zm9ybWF0VGltZSh0aW1lTGVmdCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9saT4gKi99XHJcbiAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgIHshdG9rZW4gPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gaHJlZj1cIiNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1hbnJvcGUgZmxleCBpdGVtcy1jZW50ZXIgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyICBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgZ2FwLXgtMiAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBpc0hvbWVQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBpc1Njcm9sbGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVMb2dpblBvcHVwfSAvLyBPcGVuIExvZ2luIFBvcHVwIGlmIG5vIHRva2VuXHJcbiAgICAgICAgICAgICAgICAgICAgICBwcmVmZXRjaD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFVzZXIgc2l6ZT17MjB9IC8+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdoaWRkZW4gbWQ6YmxvY2snPlRyYXZlbGxlcjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgKSA6IHRva2VuICYmIHJvbGUgPT09IFwidXNlclwiID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIC8vIGhyZWY9XCIjXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHJlbD0nY2Fub25pY2FsJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1hbnJvcGUgZmxleCBpdGVtcy1jZW50ZXIgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyICBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgZ2FwLXgtMiAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBpc0hvbWVQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBpc1Njcm9sbGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyBvbkNsaWNrPXt0b2dnbGVNeVByb2ZpbGV9IC8vIE9wZW4gTXlQcm9maWxlIGlmIHRva2VuIGV4aXN0c1xyXG4gICAgICAgICAgICAgICAgICAgICAgcHJlZmV0Y2g9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxVc2VyIHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgey8qIHt1c2VyPy5uYW1lIHx8IFwiUHJvZmlsZVwifSAqL31cclxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRJdGVtTG9jYWxTdG9yYWdlKFwibmFtZVwiKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgaHJlZj0nIydcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tYW5yb3BlIGZsZXggaXRlbXMtY2VudGVyIGZvbnQtYm9sZCBjdXJzb3ItcG9pbnRlciB0ZXh0LWJsYWNrIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBnYXAteC0yICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZUxvZ2luUG9wdXB9IC8vIE9wZW4gTG9naW4gUG9wdXAgaWYgbm8gdG9rZW5cclxuICAgICAgICAgICAgICAgICAgICAgIHByZWZldGNoPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VXNlciBzaXplPXsyMH0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIFRyYXZlbGxlclxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPSdoaWRkZW4gbWQ6YmxvY2snPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT0nYnV0dG9uJ1xyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tYW5yb3BlIGl0ZW1zLWNlbnRlciBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgdGV4dC1ibGFjayBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgZ2FwLXgtMiBmbGV4ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtd2hpdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBpc1Njcm9sbGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU9wZW5Db3VudHJ5TW9kYWx9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7ZmxhZ1VybCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2ZsYWdVcmx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsdD0nQ291bnRyeSBGbGFnJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aD17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9J2xhenknXHJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8R2xvYmUgc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICB7Y3VycmVuY3lDb2RlID8gY3VycmVuY3lDb2RlIDogXCJDb3VudHJ5XCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIHNpemU9ezE4fSAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPSdmbGV4Jz5cclxuICAgICAgICAgICAgICAgICAgey8qIEZvciBtb2JpbGUgc2NyZWVucyAoMC01NzVweCkgKi99XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPSdNb2JpbGUgTWVudSdcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BzbTpoaWRkZW4gdGV4dC1zbSBmb250LW1hbnJvcGUgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyIHRleHQtYmxhY2sgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtd2hpdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGlzSG9tZVBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBpc1Njcm9sbGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZU1lbnVNb2JpbGV9XHJcbiAgICAgICAgICAgICAgICAgICAgcHJlZmV0Y2g9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEdyaXAgc2l6ZT17MjR9IC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICB7LyogRm9yIGxhcmdlciBzY3JlZW5zICg1NzZweCBhbmQgYWJvdmUpICovfVxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD0nTW9iaWxlIE1lbnUnXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaGlkZGVuIHNtOmJsb2NrIHRleHQtc20gZm9udC1tYW5yb3BlIGZvbnQtYm9sZCBjdXJzb3ItcG9pbnRlciB0ZXh0LWJsYWNrIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgaXNUb3BIb3N0ZWxQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXdoaXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBpc0hvbWVQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gaXNTY3JvbGxlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVNZW51fVxyXG4gICAgICAgICAgICAgICAgICAgIHByZWZldGNoPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxHcmlwIHNpemU9ezI0fSAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICB7IWlzSG9tZVBhZ2UgJiYgKFxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZmxleCBsZzpoaWRkZW4gJHtcclxuICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2VcclxuICAgICAgICAgICAgICAgID8gXCJhYnNvbHV0ZSB6LTUwIGJnLWJsYWNrIGJnLW9wYWNpdHktMTAgc2hhZG93LW1kIGJhY2tkcm9wLWJsdXItc21cIlxyXG4gICAgICAgICAgICAgICAgOiBcImJnLXdoaXRlIGJvcmRlci15IGJvcmRlci13aGl0ZVwiXHJcbiAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncmVsYXRpdmUgei01MCBmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBweS00IGNvbnRhaW5lcic+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3ctWzMwJV0gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLXgtMyc+XHJcbiAgICAgICAgICAgICAgICA8TGluayBocmVmPScvJyByZWw9J2Nhbm9uaWNhbCcgcHJlZmV0Y2g9e2ZhbHNlfT5cclxuICAgICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtcclxuICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1MzX1VSTF9GRX0vZnJvbnQtaW1hZ2VzL01peGRvcm0td2hpdGUtRC5zdmdgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUzNfVVJMX0ZFfS9mcm9udC1pbWFnZXMvbG9nby5zdmdgXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXsxNTV9XHJcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXs0MH1cclxuICAgICAgICAgICAgICAgICAgICBhbHQ9J0NvbnRlbnQgQWknXHJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9J0NvbnRlbnQgQWknXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSdtYXgtdy1bMTEwcHhdIG1kOm1heC13LVsxNTVweF0gbWQ6bWF4LWgtMjQgcmVsYXRpdmUgei01MCB3LWZpdCBvYmplY3QtY29udGFpbiBiZy1ibGVuZC1jb2xvci1idXJuIGN1cnNvci1wb2ludGVyIGhvdmVyOnNjYWxlLTk1IGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCdcclxuICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPSdsYXp5J1xyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSd3LVs1NSVdIGZsZXgganVzdGlmeS1lbmQgaXRlbXMtY2VudGVyJz5cclxuICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1kOmdhcC14LTUgZ2FwLXgtMyc+XHJcbiAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9J3JlbGF0aXZlJz5cclxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyBocmVmPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICByZWw9J2Nhbm9uaWNhbCdcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tYW5yb3BlIGl0ZW1zLWNlbnRlciBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgcm91bmRlZC05eGwgIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBnYXAteC0yIGZsZXggJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXNUb3BIb3N0ZWxQYWdlID8gXCJ0ZXh0LXdoaXRlXCIgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVPcGVuTm90aWNlQm9hcmR9XHJcbiAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPSdOb3RpZmljYXRpb24nXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPE1kT3V0bGluZU5vdGlmaWNhdGlvbnNBY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MjZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZvbnQtbm9ybWFsICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNUb3BIb3N0ZWxQYWdlID8gXCJ0ZXh0LXdoaXRlXCIgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWJzb2x1dGUgbGVmdC00IHRvcC1bLTEwcHhdIG1pbi1oLTMuNSBtaW4tdy0zLjUgbWF4LWgtNC41IG1heC13LTQuNSBhbmltYXRlLXBpbmcgcm91bmRlZC1mdWxsIGJnLXByaW1hcnktYmx1ZSc+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0nYWJzb2x1dGUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbGVmdC00IHRvcC1bLTEwcHhdIG1pbi1oLTMuNSBtaW4tdy0zLjUgbWF4LWgtNC41IG1heC13LTQuNSByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS1ibHVlIHRleHQtWzEwcHhdIGZvbnQtbWVkaXVtIHRleHQtYmxhY2sgdGV4dC1jZW50ZXIgbGVhZGluZy1ub25lJz5cclxuICAgICAgICAgICAgICAgICAgICAgIDJcclxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDwvbGk+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgeyF0b2tlbiA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gaHJlZj1cIiNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWFucm9wZSBmbGV4IGl0ZW1zLWNlbnRlciBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBnYXAteC0yICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNUb3BIb3N0ZWxQYWdlID8gXCJ0ZXh0LXdoaXRlXCIgOiBcInRleHQtYmxhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTG9naW5Qb3B1cH0gLy8gT3BlbiBMb2dpbiBQb3B1cCBpZiBubyB0b2tlblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPSdVc2VyJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8RmFSZWdVc2VyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZm9udC1ib2xkICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2UgPyBcInRleHQtd2hpdGVcIiA6IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J2hpZGRlbiBtZDpibG9jayc+VHJhdmVsbGVyPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IHRva2VuICYmIHJvbGUgPT09IFwidXNlclwiID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gICAvLyBocmVmPVwiI1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyAgIHJlbD1cImNhbm9uaWNhbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tYW5yb3BlIGZsZXggaXRlbXMtY2VudGVyICBqdXN0aWZ5LWNlbnRlciBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBnYXAteC0yIGJvcmRlci0yIGJvcmRlci1ibGFjay0xMDAgcm91bmRlZC1mdWxsIGgtNyB3LTcgICR7aXNUb3BIb3N0ZWxQYWdlID8gXCJ0ZXh0LXdoaXRlXCIgOiBcInRleHQtYmxhY2tcIn1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gICAvLyBvbkNsaWNrPXt0b2dnbGVNeVByb2ZpbGV9IC8vIE9wZW4gTXlQcm9maWxlIGlmIHRva2VuIGV4aXN0c1xyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gPlxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gICB7LyogPEZhUmVnVXNlciBzaXplPXsyMH0gLz4gICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gICBBXHJcbiAgICAgICAgICAgICAgICAgICAgICAvLyA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyZWxhdGl2ZSc+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWFucm9wZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IGdhcC14LTIgIHJvdW5kZWQtZnVsbCBoLTcgdy03ICAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNUb3BIb3N0ZWxQYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1wcmltYXJ5LWJsdWUgdGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJiZy1wcmltYXJ5LWJsdWUgdGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoKSA9PiBzZXRTaG93VG9vbHRpcCh0cnVlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IHNldFNob3dUb29sdGlwKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRJdGVtTG9jYWxTdG9yYWdlKFwibmFtZVwiKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPy5jaGFyQXQoMClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8udG9VcHBlckNhc2UoKSB8fCBcIkFcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dUb29sdGlwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogNSB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwLCB5OiA1IH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0nYWJzb2x1dGUgYm90dG9tLWZ1bGwgbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgYmctd2hpdGUgdGV4dC1ibGFjayB0ZXh0LXhzIHJvdW5kZWQgcHktMSBweC0yIHdoaXRlc3BhY2Utbm93cmFwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0SXRlbUxvY2FsU3RvcmFnZShcIm5hbWVcIil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj0nIydcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1hbnJvcGUgZmxleCBpdGVtcy1jZW50ZXIgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyIHRleHQtYmxhY2sgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IGdhcC14LTIgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBpc1RvcEhvc3RlbFBhZ2UgPyBcInRleHQtd2hpdGVcIiA6IFwidGV4dC1ibGFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTG9naW5Qb3B1cH0gLy8gT3BlbiBMb2dpbiBQb3B1cCBpZiBubyB0b2tlblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwcmVmZXRjaD17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9J1VzZXInXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxGYVJlZ1VzZXIgc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0naGlkZGVuIG1kOmJsb2NrJz5UcmF2ZWxsZXI8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT0nJz5cclxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPSdidXR0b24nXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWFucm9wZSBpdGVtcy1jZW50ZXIgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyICBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgZ2FwLXgtMiBmbGV4ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZSA/IFwidGV4dC13aGl0ZVwiIDogXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlT3BlbkNvdW50cnlNb2RhbH1cclxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9J0ZsYWcnXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2ZsYWdVcmwgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdyZWxhdGl2ZSB3LTYgaC02IHJvdW5kZWQtZnVsbCBvdmVyZmxvdy1oaWRkZW4nPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtmbGFnVXJsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PSdDb3VudHJ5IEZsYWcnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J29iamVjdC1jb3ZlciByb3VuZGVkLWZ1bGwnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplcz0nMjRweCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9J2xhenknXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8R2xvYmUgc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPSdmbGV4Jz5cclxuICAgICAgICAgICAgICAgICAgICB7LyogRm9yIG1vYmlsZSBzY3JlZW5zICgwLTU3NXB4KSAqL31cclxuICAgICAgICAgICAgICAgICAgICB7LyogPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIk1vYmlsZSBNZW51XCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHNtOmhpZGRlbiB0ZXh0LXNtIGZvbnQtbWFucm9wZSBmb250LWJvbGQgY3Vyc29yLXBvaW50ZXIgdGV4dC1ibGFjayBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgJHtpc1RvcEhvc3RlbFBhZ2UgPyBcInRleHQtd2hpdGVcIiA6IFwidGV4dC1ibGFja1wifWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVNZW51TW9iaWxlfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxHcmlwIHNpemU9ezI0fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPiAqL31cclxuICAgICAgICAgICAgICAgICAgICB7LyogRm9yIGxhcmdlciBzY3JlZW5zICg1NzZweCBhbmQgYWJvdmUpICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9J01vYmlsZSBNZW51J1xyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYmxvY2sgdGV4dC1zbSBmb250LW1hbnJvcGUgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyICAgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVG9wSG9zdGVsUGFnZSA/IFwidGV4dC13aGl0ZVwiIDogXCJ0ZXh0LWJsYWNrXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlTWVudU1vYmlsZX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8R3JpcCBzaXplPXsyNH0gLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9zZWN0aW9uPlxyXG5cclxuICAgICAgey8qIDxEcmF3ZXIgb3Blbj17b3BlbkRyYXdlcn0gb25DbG9zZT17dG9nZ2xlRHJhd2VyKGZhbHNlKX0gY2xhc3NOYW1lPVwibmF2LWJhci1odW1idXJnZXIgZmFkZUluTGVmdCBhbmltYXRlZFwiIGFuY2hvcj1cImxlZnRcIj4gKi99XHJcbiAgICAgIDxEcmF3ZXJcclxuICAgICAgICBvcGVuPXtvcGVuRHJhd2VyfVxyXG4gICAgICAgIG9uQ2xvc2U9e3RvZ2dsZURyYXdlcihmYWxzZSl9XHJcbiAgICAgICAgY2xhc3NOYW1lPSduYXYtYmFyLWh1bWJ1cmdlciBmYWRlSW5MZWZ0IGFuaW1hdGVkJ1xyXG4gICAgICAgIGFuY2hvcj0nbGVmdCdcclxuICAgICAgPlxyXG4gICAgICAgIDxCb3hcclxuICAgICAgICAgIHN4PXt7IHdpZHRoOiAzNDYgfX1cclxuICAgICAgICAgIHJvbGU9J3ByZXNlbnRhdGlvbidcclxuICAgICAgICAgIGJvcmRlclJhZGl1cz17MTB9XHJcbiAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVEcmF3ZXIoZmFsc2UpfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxQYXBlclxyXG4gICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgIHdpZHRoOiAzMjYsXHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogXCIjZmZmXCIsXHJcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjAgMCAxMHB4IDEwcHhcIixcclxuICAgICAgICAgICAgICBoZWlnaHQ6IFwiMTAwdmhcIixcclxuICAgICAgICAgICAgICBlbGV2YXRpb246IDAsXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIGVsZXZhdGlvbj17MH1cclxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzPXsxMH1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPScvJ1xyXG4gICAgICAgICAgICAgIHJlbD0nY2Fub25pY2FsJ1xyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT0ncC00IGJsb2NrJ1xyXG4gICAgICAgICAgICAgIHByZWZldGNoPXtmYWxzZX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgc3JjPXtgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TM19VUkxfRkV9L2Zyb250LWltYWdlcy9sb2dvLnN2Z2B9XHJcbiAgICAgICAgICAgICAgICB3aWR0aD17MTU1fVxyXG4gICAgICAgICAgICAgICAgaGVpZ2h0PXs0MH1cclxuICAgICAgICAgICAgICAgIGFsdD0nTWl4ZG9ybSdcclxuICAgICAgICAgICAgICAgIHRpdGxlPSdNaXhkb3JtJ1xyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPSdtYXgtdy1bMTEwcHhdIG1kOm1heC13LVsxNTVweF0gbWQ6bWF4LWgtMjQgcmVsYXRpdmUgei01MCB3LWZpdCBvYmplY3QtY29udGFpbiBiZy1ibGVuZC1jb2xvci1idXJuIGN1cnNvci1wb2ludGVyIGhvdmVyOnNjYWxlLTk1IGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCdcclxuICAgICAgICAgICAgICAgIGxvYWRpbmc9J2xhenknXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPSdhYnNvbHV0ZSB0b3AtNCByaWdodC00J1xyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZURyYXdlcihmYWxzZSl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8WCBzaXplPXsyMn0gLz5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxEaXZpZGVyIC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdwLTMnPlxyXG4gICAgICAgICAgICAgIDxsYWJlbFxyXG4gICAgICAgICAgICAgICAgaHRtbEZvcj0nJ1xyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPSd0ZXh0LXNtIHNtOnRleHQtYmFzZSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCBtYi00J1xyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxHbG9iZSBzaXplPXsyMH0gLz4gU2VsZWN0IEN1cnJlbmN5XHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICB0eXBlPSdidXR0b24nXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGdhcC14LTIgdGV4dC1zbSBmb250LW1hbnJvcGUgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyIHRleHQtYmxhY2sgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0J1xyXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlT3BlbkNvdW50cnlNb2RhbH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7ZmxhZ1VybCA/IChcclxuICAgICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgc3JjPXtmbGFnVXJsfVxyXG4gICAgICAgICAgICAgICAgICAgIGFsdD0nQ291bnRyeSBGbGFnJ1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXsyMH1cclxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezIwfVxyXG4gICAgICAgICAgICAgICAgICAgIGxvYWRpbmc9J2xhenknXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICA8R2xvYmUgc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAge2N1cnJlbmN5Q29kZSA/IGN1cnJlbmN5Q29kZSA6IFwiQ291bnRyeVwifVxyXG4gICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIHNpemU9ezE4fSAvPlxyXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHsvKiA8ZGl2IGNsYXNzTmFtZT0ncC0zJz5cclxuICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgaHJlZj0nIydcclxuICAgICAgICAgICAgICAgIHJlbD0nY2Fub25pY2FsJ1xyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPSd0ZXh0LXNtIGZvbnQtbWFucm9wZSBpdGVtcy1jZW50ZXIgZm9udC1ib2xkIGN1cnNvci1wb2ludGVyIHJvdW5kZWQtOXhsIHRleHQtYmxhY2sgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IGdhcC14LTIgZmxleCdcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU9wZW5Ob3RpY2VCb2FyZH1cclxuICAgICAgICAgICAgICAgIHByZWZldGNoPXtmYWxzZX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8TWRPdXRsaW5lTm90aWZpY2F0aW9uc0FjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICBzaXplPXsyMH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSdmb250LW5vcm1hbCB0ZXh0LWJsYWNrJ1xyXG4gICAgICAgICAgICAgICAgLz57XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICBOb3RpY2Vib2FyZFxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPC9kaXY+ICovfVxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3AtMyc+XHJcbiAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgIGhyZWY9Jy9vd25lci9ob3N0ZWwtbG9naW4nXHJcbiAgICAgICAgICAgICAgICByZWw9J2Nhbm9uaWNhbCdcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0nYmxvY2sgcHgtNSBweS0zIHRleHQtY2VudGVyIGZvbnQtbWFucm9wZSB0ZXh0LWJhc2UgZm9udC1ib2xkIGJnLXByaW1hcnktYmx1ZSBjdXJzb3ItcG9pbnRlciByb3VuZGVkLTR4bCB0ZXh0LWJsYWNrIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCdcclxuICAgICAgICAgICAgICAgIHByZWZldGNoPXtmYWxzZX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBMaXN0IFlvdXIgSG9zdGVsXHJcbiAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvUGFwZXI+XHJcbiAgICAgICAgPC9Cb3g+XHJcbiAgICAgIDwvRHJhd2VyPlxyXG5cclxuICAgICAgPE5vdGljZWJvYXJkXHJcbiAgICAgICAgY2xvc2U9e2hhbmRsZUNsb3NlTm90aWNlQm9hcmR9XHJcbiAgICAgICAgb3Blbj17b3Blbk5vdGljZUJvYXJkfVxyXG4gICAgICAgIG9wZW5Ob3RpY2VCb2FyZERldGFpbHM9e29wZW5Ob3RpY2VCb2FyZERldGFpbHN9XHJcbiAgICAgIC8+XHJcbiAgICAgIHtpc01lbnVPcGVuICYmIChcclxuICAgICAgICA8TWVudVBvcHVwXHJcbiAgICAgICAgICBpc09wZW49e2lzTWVudU9wZW59XHJcbiAgICAgICAgICB0b2dnbGVNZW51PXt0b2dnbGVNZW51fVxyXG4gICAgICAgICAgdXBkYXRlVG9rZW5TdGF0ZT17dXBkYXRlVG9rZW5TdGF0ZX1cclxuICAgICAgICAgIHRvZ2dsZUxvZ2luUG9wdXA9e3RvZ2dsZUxvZ2luUG9wdXB9XHJcbiAgICAgICAgICB1cGRhdGVSb2xlU3RhdGU9e3VwZGF0ZVJvbGVTdGF0ZX1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG4gICAgICB7aXNNZW51T3Blbk1vYmlsZSAmJiAoXHJcbiAgICAgICAgPE1lbnVQb3B1cE1vYmlsZVxyXG4gICAgICAgICAgaXNPcGVuPXtpc01lbnVPcGVuTW9iaWxlfVxyXG4gICAgICAgICAgdG9nZ2xlTWVudT17dG9nZ2xlTWVudU1vYmlsZX1cclxuICAgICAgICAgIHVwZGF0ZVRva2VuU3RhdGU9e3VwZGF0ZVRva2VuU3RhdGV9XHJcbiAgICAgICAgICB0b2dnbGVMb2dpblBvcHVwPXt0b2dnbGVMb2dpblBvcHVwfVxyXG4gICAgICAgICAgdXBkYXRlUm9sZVN0YXRlPXt1cGRhdGVSb2xlU3RhdGV9XHJcbiAgICAgICAgLz5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIDxNeVByb2ZpbGVcclxuICAgICAgICBpc01lbnVPcGVuPXtpc015UHJvZmlsZU9wZW59XHJcbiAgICAgICAgdG9nZ2xlTWVudT17dG9nZ2xlTXlQcm9maWxlfVxyXG4gICAgICAgIHVwZGF0ZVRva2VuU3RhdGU9e3VwZGF0ZVRva2VuU3RhdGV9XHJcbiAgICAgICAgdXBkYXRlUm9sZVN0YXRlPXt1cGRhdGVSb2xlU3RhdGV9XHJcbiAgICAgIC8+XHJcbiAgICAgIDxMb2dpblBvcHVwIGlzT3Blbj17c2hvd0xvZ2luUG9wdXB9IG9uQ2xvc2U9e3RvZ2dsZUxvZ2luUG9wdXB9IC8+XHJcblxyXG4gICAgICA8Q291bnRyeU1vZGFsXHJcbiAgICAgICAgb3BlbkNvdW50cnlNb2RhbD17b3BlbkNvdW50cnlNb2RhbH1cclxuICAgICAgICBoYW5kbGVDbG9zZUNvdW50cnlNb2RhbD17aGFuZGxlQ2xvc2VDb3VudHJ5TW9kYWx9XHJcbiAgICAgICAgdXBkYXRlQ291bnRyeT17dXBkYXRlQ291bnRyeX1cclxuICAgICAgLz5cclxuICAgIDwvPlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBkeW5hbWljKCgpID0+IFByb21pc2UucmVzb2x2ZShOYXZiYXIpLCB7IHNzcjogZmFsc2UgfSk7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiSW1hZ2UiLCJMaW5rIiwiTWRPdXRsaW5lTm90aWZpY2F0aW9uc0FjdGl2ZSIsIkNoZXZyb25Eb3duIiwiR2xvYmUiLCJHcmlwIiwiVXNlciIsIlgiLCJkeW5hbWljIiwiSGlNZW51QWx0MyIsIkJveCIsIkRpdmlkZXIiLCJEcmF3ZXIiLCJQYXBlciIsImdldEl0ZW1Mb2NhbFN0b3JhZ2UiLCJ1c2VOYXZiYXIiLCJnZXRQcm9wZXJ0eUNvdW50QXBpIiwiTWVudVBvcHVwIiwiTWVudVBvcHVwTW9iaWxlIiwidG9hc3QiLCJ1c2VSb3V0ZXIiLCJGYVJlZ1VzZXIiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJDb3VudHJ5TW9kYWwiLCJzc3IiLCJMb2dpblBvcHVwIiwiTm90aWNlYm9hcmQiLCJNeVByb2ZpbGUiLCJOYXZiYXIiLCJvcGVuQ291bnRyeU1vZGFsIiwic2V0T3BlbkNvdW50cnlNb2RhbCIsImhhbmRsZU9wZW5Db3VudHJ5TW9kYWwiLCJoYW5kbGVDbG9zZUNvdW50cnlNb2RhbCIsInNob3dUb29sdGlwIiwic2V0U2hvd1Rvb2x0aXAiLCJzY3JvbGxlZCIsInNldFNjcm9sbGVkIiwic2hvd0xvZ2luUG9wdXAiLCJzZXRTaG93TG9naW5Qb3B1cCIsImhvdmVyaW5nVG9wIiwic2V0SG92ZXJpbmdUb3AiLCJpc01lbnVPcGVuIiwic2V0SXNNZW51T3BlbiIsImlzTWVudU9wZW5Nb2JpbGUiLCJzZXRJc01lbnVPcGVuTW9iaWxlIiwiaGFzVG9rZW4iLCJzZXRIYXNUb2tlbiIsInJvbGVzIiwic2V0Um9sZSIsImlzTXlQcm9maWxlT3BlbiIsInNldElzTXlQcm9maWxlT3BlbiIsImZsYWdVcmwiLCJzZXRGbGFnVXJsIiwiY3VycmVuY3lDb2RlIiwic2V0Q3VycmVuY3lDb2RlIiwiaXNQb3B1cE9wZW4iLCJzZXRJc1BvcHVwT3BlbiIsInBvcHVwUmVmIiwidG9nZ2xlTXlQcm9maWxlIiwidG9nZ2xlTWVudSIsInRva2VuIiwicm9sZSIsImVycm9yIiwic3ViVGV4dCIsInRvZ2dsZU1lbnVNb2JpbGUiLCJ0b2dnbGVMb2dpblBvcHVwIiwiaXNTY3JvbGxlZCIsInNldElzU2Nyb2xsZWQiLCJoYW5kbGVSb3V0ZUNoYW5nZSIsInJvdXRlciIsImV2ZW50cyIsIm9uIiwib2ZmIiwidXBkYXRlVG9rZW5TdGF0ZSIsInVwZGF0ZVJvbGVTdGF0ZSIsInVwZGF0ZUNvdW50cnkiLCJmbGFnIiwiY29kZSIsInVwZGF0ZUNvdW50cnkyIiwiaGFuZGxlU3RvcmFnZUNoYW5nZSIsImV2ZW50Iiwia2V5Iiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJvcmlnaW5hbFNldEl0ZW0iLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwiRXZlbnQiLCJhcHBseSIsImFyZ3VtZW50cyIsImRpc3BhdGNoRXZlbnQiLCJoYW5kbGVJdGVtSW5zZXJ0ZWQiLCJoYW5kbGVTY3JvbGwiLCJzY3JvbGxZIiwiaGFuZGxlTW91c2VNb3ZlIiwiY2xpZW50WSIsInNlbGVjdGVkQ291bnRyeUZsYWciLCJzZWxlY3RlZEN1cnJlbmN5Q29kZSIsInVybCIsIm9wZW5Ob3RpY2VCb2FyZCIsInNldE9wZW5Ob3RpY2VCb2FyZCIsIm9wZW5Ob3RpY2VCb2FyZERldGFpbHMiLCJzZXRPcGVuTm90aWNlQm9hcmREZXRhaWxzIiwiaGFuZGxlT3Blbk5vdGljZUJvYXJkIiwicHJvcGVydHlDb3VudFJlc3BvbnNlIiwiZGF0YSIsInRvdGFsQm9va2luZyIsImhhbmRsZUNsb3NlTm90aWNlQm9hcmQiLCJvcGVuRHJhd2VyIiwic2V0T3BlbkRyYXdlciIsInRvZ2dsZURyYXdlciIsIm5ld09wZW4iLCJpbm5lcldpZHRoIiwiY2xvc2VUaW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJoYW5kbGVDbGlja091dHNpZGUiLCJjdXJyZW50IiwiY29udGFpbnMiLCJ0YXJnZXQiLCJkb2N1bWVudCIsImlzSG9tZVBhZ2UiLCJwYXRobmFtZSIsImlzVG9wSG9zdGVsUGFnZSIsImNvbnNvbGUiLCJsb2ciLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwicCIsImhyZWYiLCJzcGFuIiwiYnV0dG9uIiwiYXJpYS1sYWJlbCIsIm9uQ2xpY2siLCJsaSIsInJlbCIsInByZWZldGNoIiwic3JjIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1MzX1VSTF9GRSIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwidGl0bGUiLCJsb2FkaW5nIiwidWwiLCJwYXNzSHJlZiIsInNpemUiLCJ0eXBlIiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJhbmltYXRlIiwiZXhpdCIsImZpbGwiLCJzaXplcyIsIm9wZW4iLCJvbkNsb3NlIiwiYW5jaG9yIiwic3giLCJib3JkZXJSYWRpdXMiLCJiYWNrZ3JvdW5kIiwiZWxldmF0aW9uIiwibGFiZWwiLCJodG1sRm9yIiwiY2xvc2UiLCJpc09wZW4iLCJQcm9taXNlIiwicmVzb2x2ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/navbar/navbar.jsx\n");

/***/ }),

/***/ "./components/popup/contactPopup.jsx":
/*!*******************************************!*\
  !*** ./components/popup/contactPopup.jsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/webflowServices */ \"./services/webflowServices.jsx\");\n/* harmony import */ var _barrel_optimize_names_Modal_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Modal!=!@mui/material */ \"__barrel_optimize__?names=Modal!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_MdKeyboardArrowDown_MdKeyboardArrowUp_react_icons_md__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MdKeyboardArrowDown,MdKeyboardArrowUp!=!react-icons/md */ \"__barrel_optimize__?names=MdKeyboardArrowDown,MdKeyboardArrowUp!=!./node_modules/react-icons/md/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__]);\n([_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ContactPopup = ({ open, close })=>{\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedOption, setSelectedOption] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"Categories\");\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [subject, setSubject] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const toggleDropdown = ()=>setIsDropdownOpen(!isDropdownOpen);\n    const handleOptionClick = (option)=>{\n        setSelectedOption(option);\n        setIsDropdownOpen(false);\n    };\n    const validate = ()=>{\n        let tempErrors = {};\n        if (!name) tempErrors.name = \"Name is required.\";\n        if (!email) {\n            tempErrors.email = \"Email is required.\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\n            tempErrors.email = \"Email is not valid.\";\n        }\n        if (!subject) tempErrors.subject = \"Subject is required.\";\n        if (selectedOption === \"Categories\") tempErrors.category = \"Please select a category.\";\n        if (!description) tempErrors.description = \"Description is required.\";\n        setErrors(tempErrors);\n        return Object.keys(tempErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (validate()) {\n            try {\n                const payload = {\n                    name,\n                    email,\n                    subject,\n                    categories: selectedOption,\n                    description\n                };\n                await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__.contactUsApi)(payload);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"Your message has been sent successfully.\");\n                setName(\"\");\n                setEmail(\"\");\n                setSubject(\"\");\n                setSelectedOption(\"Categories\");\n                setDescription(\"\");\n                setErrors({});\n                close();\n            } catch (error) {\n                console.error(\"Error sending message:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"There was an error sending your message. Please try again.\");\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ContactPopup.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"ContactPopup.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsDropdownOpen(false);\n                    }\n                }\n            }[\"ContactPopup.useEffect.handleClickOutside\"];\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"ContactPopup.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"ContactPopup.useEffect\"];\n        }\n    }[\"ContactPopup.useEffect\"], []);\n    if (!open) return null;\n    const style = {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"50%\",\n        transform: \"translate(-50%, -50%)\",\n        width: 400,\n        bgcolor: \"background.paper\",\n        border: \"2px solid #000\",\n        boxShadow: 24,\n        p: 4\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Modal_mui_material__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n        open: open,\n        onClose: close,\n        \"aria-labelledby\": \"modal-modal-title\",\n        \"aria-describedby\": \"modal-modal-description\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: style,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl max-w-[700px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pb-6 font-manrope\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col justify-center items-center mb-6 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-[#40E0D0] flex text-2xl font-extrabold mb-3\",\n                                    children: [\n                                        \"Mix\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-black text-2xl font-extrabold\",\n                                            children: \"Dorm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 20\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-center\",\n                                    children: \"\\uD83D\\uDC4B Contact Us\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: close,\n                                    className: \"text-black ml-auto text-xl font-semibold hover:text-gray-600 transition duration-150 absolute right-0 top-0\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"h-[445px] overflow-auto gap-4 fancy_y_scroll grid grid-cols-1 md:grid-cols-2 pt-1 px-4 md:px-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            className: `w-full px-3 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${errors.name && \"border-red-500\"}`,\n                                            type: \"text\",\n                                            placeholder: \"Name\",\n                                            value: name,\n                                            onChange: (e)=>setName(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            className: `w-full px-3 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${errors.email && \"border-red-500\"}`,\n                                            type: \"email\",\n                                            placeholder: \"Email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            className: `w-full px-3 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${errors.subject && \"border-red-500\"}`,\n                                            type: \"text\",\n                                            placeholder: \"Subject\",\n                                            value: subject,\n                                            onChange: (e)=>setSubject(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.subject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.subject\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full\",\n                                            ref: dropdownRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-full flex items-center border rounded-xl focus-within:ring-1 focus-within:ring-teal-500 focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 cursor-pointer ${errors.category && \"border-red-500\"}`,\n                                                    onClick: toggleDropdown,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"px-3 py-3 w-full focus:outline-none cursor-pointer\",\n                                                            value: selectedOption,\n                                                            readOnly: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mr-2 text-xl\",\n                                                            children: isDropdownOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdKeyboardArrowDown_MdKeyboardArrowUp_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdKeyboardArrowUp, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdKeyboardArrowDown_MdKeyboardArrowUp_react_icons_md__WEBPACK_IMPORTED_MODULE_5__.MdKeyboardArrowDown, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 w-full bg-white border rounded-md shadow-lg mt-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm\",\n                                                                onClick: ()=>handleOptionClick(\"General\"),\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm\",\n                                                                onClick: ()=>handleOptionClick(\"Support\"),\n                                                                children: \"Support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm\",\n                                                                onClick: ()=>handleOptionClick(\"Feedback\"),\n                                                                children: \"Feedback\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            className: `w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${errors.description && \"border-red-500\"}`,\n                                            placeholder: \"Description\",\n                                            rows: \"5\",\n                                            value: description,\n                                            onChange: (e)=>setDescription(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-black/55 text-sm mb-3 col-span-1 md:col-span-2\",\n                                    children: \"Please enter the details of your request. A member of our support staff will respond as soon as possible. Please ensure that you do not enter credit card details/username/ passwords in this form.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"bg-[#40E0D0] text-white col-span-1 md:col-span-2 font-semibold w-full py-3 rounded-full hover:bg-sky-blue-750 transition duration-150\",\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\contactPopup.jsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContactPopup);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/popup/contactPopup.jsx\n");

/***/ }),

/***/ "./components/popup/menuPopup.jsx":
/*!****************************************!*\
  !*** ./components/popup/menuPopup.jsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var _contactPopup__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contactPopup */ \"./components/popup/contactPopup.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaRegUser!=!react-icons/fa */ \"__barrel_optimize__?names=FaRegUser!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MdCardMembership,MdLogout,MdOutlineTravelExplore!=!react-icons/md */ \"__barrel_optimize__?names=MdCardMembership,MdLogout,MdOutlineTravelExplore!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_GrEdit_react_icons_gr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=GrEdit!=!react-icons/gr */ \"__barrel_optimize__?names=GrEdit!=!./node_modules/react-icons/gr/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_IoWalletOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IoWalletOutline!=!react-icons/io5 */ \"__barrel_optimize__?names=IoWalletOutline!=!./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineQuestionMarkCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineQuestionMarkCircle!=!react-icons/hi */ \"__barrel_optimize__?names=HiOutlineQuestionMarkCircle!=!./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/ownerflowServices */ \"./services/ownerflowServices.jsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_GoHeart_react_icons_go__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=GoHeart!=!react-icons/go */ \"__barrel_optimize__?names=GoHeart!=!./node_modules/react-icons/go/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contactPopup__WEBPACK_IMPORTED_MODULE_4__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__, react_hot_toast__WEBPACK_IMPORTED_MODULE_8__]);\n([_contactPopup__WEBPACK_IMPORTED_MODULE_4__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__, react_hot_toast__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// import { ChevronDown, ChevronUp } from \"lucide-react\";\n// import toast from \"react-hot-toast\";\n\n\n// import { getPropertyCountApi } from \"@/services/webflowServices\";\n\n// import { removeFirebaseToken } from \"@/services/ownerflowServices\";\n// import { useNavbar } from \"../home/<USER>";\n\n\n\n\n\n\n\n\n\n\nconst MenuPopup = ({ isOpen, toggleMenu })=>{\n    // State to manage the open/close state of each section\n    // const { updateUserStatus,updateUserRole } = useNavbar();\n    // const [openSections, setOpenSections] = useState({\n    //   services: false,\n    //   company: false,\n    //   help: false,\n    //   account: false,\n    // });\n    // const [hasToken, setHasToken] = useState(false);\n    const [isContactPopupOpen, setIsContactPopupOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // const [role, setRole] = useState(false);\n    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_6__.useNavbar)();\n    const handleLogout = async ()=>{\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"token\");\n        updateUserStatus(\"\");\n        updateUserRole(\"\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"name\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"role\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"email\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"contact\");\n        toggleMenu();\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"Logged out successfully\");\n        const payload = {\n            token: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.getItemLocalStorage)(\"FCT\"),\n            userId: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.getItemLocalStorage)(\"id\")\n        };\n        try {\n            await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.removeFirebaseToken)(payload);\n            console.log(\"FCM token removed successfully.\");\n            (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"FCT\");\n        } catch (error) {\n            console.error(\"Error removing FCM token:\", error);\n        }\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"id\");\n        router.push(\"/\");\n    };\n    const menuItems = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaRegUser, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 78,\n                columnNumber: 13\n            }, undefined),\n            label: \"My Profile\",\n            href: \"/my-profile?section=profile\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GoHeart_react_icons_go__WEBPACK_IMPORTED_MODULE_10__.GoHeart, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 79,\n                columnNumber: 13\n            }, undefined),\n            label: \"Wishlist\",\n            href: \"/my-profile?section=wishlist\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GrEdit_react_icons_gr__WEBPACK_IMPORTED_MODULE_11__.GrEdit, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, undefined),\n            label: \"Edit Details\",\n            href: \"/my-profile?section=edit\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__.MdCardMembership, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, undefined),\n            label: \"Membership\",\n            href: \"/my-profile?section=membership\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__.MdOutlineTravelExplore, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 82,\n                columnNumber: 13\n            }, undefined),\n            label: \"My Trips\",\n            href: \"/my-profile?section=stay\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoWalletOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_13__.IoWalletOutline, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, undefined),\n            label: \"My Wallet\",\n            href: \"/my-profile?section=wallet\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineQuestionMarkCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_14__.HiOutlineQuestionMarkCircle, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, undefined),\n            label: \"Help\",\n            href: \"/my-profile?section=help\"\n        }\n    ];\n    // Toggle function for each section\n    // const toggleSection = (section) => {\n    //   setOpenSections((prevState) => ({\n    //     ...prevState,\n    //     [section]: !prevState[section],\n    //   }));\n    // };\n    // useEffect(() => {\n    //   const token = getItemLocalStorage(\"token\");\n    //   setHasToken(!!token);\n    // }, []);\n    // useEffect(() => {\n    //   const role = getItemLocalStorage(\"role\");\n    //   setRole(role);\n    // }, []);\n    // const handleLogout = async () => {\n    //   removeItemLocalStorage(\"token\");\n    //   removeItemLocalStorage(\"name\");\n    //   removeItemLocalStorage(\"id\");\n    //   removeItemLocalStorage(\"role\");\n    //   toggleMenu();\n    //   updateTokenState();\n    //   updateRoleState();\n    //   updateUserStatus(\"\")\n    //     updateUserRole(\"\")\n    //     toast.success(\"Logged out successfully\");\n    //     const payload = {\n    //       token: getItemLocalStorage(\"FCT\"),\n    //       userId: getItemLocalStorage(\"id\"),\n    //     };\n    //     try {\n    //       await removeFirebaseToken(payload);\n    //       console.log(\"FCM token removed successfully.\");\n    //       removeItemLocalStorage(\"FCT\");\n    //     } catch (error) {\n    //       console.error(\"Error removing FCM token:\", error);\n    //     }\n    //     removeItemLocalStorage(\"id\")\n    //     router.push(\"/\");\n    // };\n    // const openContactPopup = async () => {\n    //   setIsContactPopupOpen(true);\n    // };\n    const closeContactPopup = ()=>{\n        setIsContactPopupOpen(false);\n    };\n    // const handleLoginClick = () => {\n    //   toggleLoginPopup();\n    //   toggleMenu();\n    // };\n    // Close modal when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MenuPopup.useEffect\": ()=>{\n            const handleOutsideClick = {\n                \"MenuPopup.useEffect.handleOutsideClick\": (event)=>{\n                    if (modalRef.current && !modalRef.current.contains(event.target)) {\n                        toggleMenu();\n                    }\n                }\n            }[\"MenuPopup.useEffect.handleOutsideClick\"];\n            if (isOpen) {\n                document.addEventListener(\"mousedown\", handleOutsideClick);\n            }\n            return ({\n                \"MenuPopup.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleOutsideClick);\n                }\n            })[\"MenuPopup.useEffect\"];\n        }\n    }[\"MenuPopup.useEffect\"], [\n        isOpen,\n        toggleMenu\n    ]);\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 bottom-0 left-0 right-0 z-50 flex items-start justify-end bg-black bg-opacity-[70%]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: modalRef,\n                    className: \"bg-white rounded-2xl sm:w-[70%] w-[250px] max-w-xs sm:p-6 p-4 mx-10 mt-24 font-manrope\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between sm:mb-6 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#40E0D0] flex text-2xl font-extrabold\",\n                                    children: [\n                                        \"Mix\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-black text-2xl font-extrabold \",\n                                            children: \"Dorm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 18\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"text-black\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined),\n                        menuItems.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: \"flex items-center sm:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors sm:p-3 p-2.5 rounded-full text-sm font-medium text-gray-800 sm:mt-2 mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sm:text-lg text-base\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    item.label\n                                ]\n                            }, idx, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"flex items-center sm:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors p-3 rounded-full text-sm font-medium text-gray-800 sm:mt-2 mt-1 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sm:text-lg text-base\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__.MdLogout, {\n                                        className: \"text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 52\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Logout\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            isContactPopupOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contactPopup__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isContactPopupOpen,\n                onClose: closeContactPopup\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopup.jsx\",\n                lineNumber: 430,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MenuPopup);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/popup/menuPopup.jsx\n");

/***/ }),

/***/ "./components/popup/menuPopupMobile.jsx":
/*!**********************************************!*\
  !*** ./components/popup/menuPopupMobile.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var _contactPopup__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contactPopup */ \"./components/popup/contactPopup.jsx\");\n/* harmony import */ var _barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaRegUser!=!react-icons/fa */ \"__barrel_optimize__?names=FaRegUser!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MdCardMembership,MdLogout,MdOutlineTravelExplore!=!react-icons/md */ \"__barrel_optimize__?names=MdCardMembership,MdLogout,MdOutlineTravelExplore!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_GrEdit_react_icons_gr__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=GrEdit!=!react-icons/gr */ \"__barrel_optimize__?names=GrEdit!=!./node_modules/react-icons/gr/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_IoWalletOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=IoWalletOutline!=!react-icons/io5 */ \"__barrel_optimize__?names=IoWalletOutline!=!./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_HiOutlineQuestionMarkCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=HiOutlineQuestionMarkCircle!=!react-icons/hi */ \"__barrel_optimize__?names=HiOutlineQuestionMarkCircle!=!./node_modules/react-icons/hi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_GoHeart_react_icons_go__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=GoHeart!=!react-icons/go */ \"__barrel_optimize__?names=GoHeart!=!./node_modules/react-icons/go/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/ownerflowServices */ \"./services/ownerflowServices.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contactPopup__WEBPACK_IMPORTED_MODULE_4__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__]);\n([_contactPopup__WEBPACK_IMPORTED_MODULE_4__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// import React, { useEffect, useRef, useState } from \"react\";\n// import Link from \"next/link\";\n// import { ChevronDown, ChevronUp } from \"lucide-react\";\n// import toast from \"react-hot-toast\";\n// import { getItemLocalStorage, removeItemLocalStorage } from \"@/utils/browserSetting\";\n// import ContactPopup from \"./contactPopup\";\n// import { getPropertyCountApi } from \"@/services/webflowServices\";\n// import { useRouter } from \"next/router\";\n// const menuPopupMobile = ({ isOpen, toggleMenu, updateTokenState, toggleLoginPopup, updateRoleState }) => {\n//     // State to manage the open/close state of each section\n//     const [openSections, setOpenSections] = useState({\n//         services: false,\n//         company: false,\n//         help: false,\n//         account: false,\n//     });\n//     const [hasToken, setHasToken] = useState(false);\n//     const [isContactPopupOpen, setIsContactPopupOpen] = useState(false);\n//     const [role, setRole] = useState(false);\n//     const modalRef = useRef(null);\n//     const router = useRouter();\n//     // Toggle function for each section\n//     const toggleSection = (section) => {\n//         setOpenSections((prevState) => ({\n//             ...prevState,\n//             [section]: !prevState[section],\n//         }));\n//     };\n//     useEffect(() => {\n//         const token = getItemLocalStorage(\"token\");\n//         setHasToken(!!token);\n//     }, []);\n//     useEffect(() => {\n//         const role = getItemLocalStorage(\"role\");\n//         setRole(role);\n//     }, []);\n//     const handleLogout = () => {\n//         removeItemLocalStorage(\"token\")\n//         removeItemLocalStorage(\"name\")\n//         removeItemLocalStorage(\"id\")\n//         removeItemLocalStorage(\"role\")\n//         toggleMenu();\n//         updateTokenState();\n//         updateRoleState();\n//         toast.success(\"Logged out successfully\");\n//     };\n//     const openContactPopup = async () => {\n//         setIsContactPopupOpen(true);\n//     };\n//     const closeContactPopup = () => {\n//         setIsContactPopupOpen(false);\n//     };\n//     const handleLoginClick = () => {\n//         toggleLoginPopup();\n//         toggleMenu();\n//     };\n//     // Close modal when clicking outside\n//     useEffect(() => {\n//         const handleOutsideClick = (event) => {\n//             if (modalRef.current && !modalRef.current.contains(event.target)) {\n//                 toggleMenu();\n//             }\n//         };\n//         if (isOpen) {\n//             document.addEventListener(\"mousedown\", handleOutsideClick);\n//         }\n//         return () => {\n//             document.removeEventListener(\"mousedown\", handleOutsideClick);\n//         };\n//     }, [isOpen, toggleMenu]);\n//     if (!isOpen) return null;\n//     return (\n//       <>\n//         <div\n//           className={`fixed w-full h-[98%] sm:h-full sm:w-[400px] top-2 sm:top-[120px] right-0 sm:right-[120px] sm:left-auto left-2\n//        lg:bottom-0 rounded-tl-2xl rounded-bl-2xl pl-[2px] pb-[2px]   z-50 flex items-start justify-end sm:bg-transparent bg-black\n//         bg-opacity-[70%] animated ${\n//           isOpen ? \"sm:animate-none fadeInRight\" : \"\"\n//         }`}\n//         >\n//           <div\n//             ref={modalRef}\n//             className='bg-white rounded-tl-2xl rounded-bl-2xl w-[100%] max-w-full p-5 ml-0 mr-0 mt-0 h-full font-manrope'\n//           >\n//             <div className='flex items-center justify-between mb-6'>\n//               <span className='text-[#40E0D0] flex text-2xl font-extrabold'>\n//                 Mix<p className='text-black text-2xl font-extrabold '>Dorm</p>\n//               </span>\n//               <button onClick={toggleMenu} className='text-black'>\n//                 ✕\n//               </button>\n//             </div>\n//             <ul className='overflow-y-auto  max-h-[600px] sm:max-h-96 fancy_y_scroll pr-0'>\n//               {/* Services */}\n//               <li className=''>\n//                 <div\n//                   className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${\n//                     openSections.services &&\n//                     \"bg-[#D9F9F6] text-black !border-[#D9F9F6]\"\n//                   }`}\n//                   onClick={() => toggleSection(\"services\")}\n//                 >\n//                   <Link\n//                     href='/services'\n//                     className='sm:text-base text-sm font-[600] text-black'\n//                     prefetch={false}\n//                   >\n//                     Services\n//                   </Link>\n//                   {openSections.services ? <ChevronUp /> : <ChevronDown />}\n//                 </div>\n//                 {openSections.services && (\n//                   <ul className='text-base font-medium mt-[-20px] mb-3'>\n//                     <li>\n//                       <button\n//                         onClick={async () => {\n//                           if (getItemLocalStorage(\"token\") && role === \"user\") {\n//                             const propertyCountResponse =\n//                               await getPropertyCountApi();\n//                             if (\n//                               propertyCountResponse?.data?.data?.totalBooking >\n//                               0\n//                             ) {\n//                               router.push(\"/noticeboard-detail\");\n//                             } else if (\n//                               propertyCountResponse?.data?.data\n//                                 ?.totalBooking === 0\n//                             ) {\n//                               router.push(\"/noticeboard-detail\");\n//                             }\n//                           } else {\n//                             toast.error(\"Please Login !\", {\n//                               subText: \"You need to be Logged In\",\n//                             });\n//                           }\n//                         }}\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                       >\n//                         Noticeboard\n//                       </button>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         href='/services/mixride'\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         prefetch={false}\n//                       >\n//                         Mix Ride\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         href='/services/mixcreators'\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         prefetch={false}\n//                       >\n//                         Mix Creators\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         href='/services/mixmate'\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         prefetch={false}\n//                       >\n//                         Mix Mate\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         href='/services/events'\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         prefetch={false}\n//                       >\n//                         Events\n//                       </Link>\n//                     </li>\n//                   </ul>\n//                 )}\n//               </li>\n//               {/* Company */}\n//               <li>\n//                 <div\n//                   className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${\n//                     openSections.company &&\n//                     \"bg-[#D9F9F6] !mb-0 text-black !border-[#D9F9F6]\"\n//                   }`}\n//                   onClick={() => toggleSection(\"company\")}\n//                 >\n//                   <Link\n//                     href='/company'\n//                     className='sm:text-base text-sm font-[600] text-black'\n//                     prefetch={false}\n//                   >\n//                     Company\n//                   </Link>\n//                   {openSections.company ? <ChevronUp /> : <ChevronDown />}\n//                 </div>\n//                 {openSections.company && (\n//                   <ul className='text-base font-medium mt-[-20px] mb-3'>\n//                     <li>\n//                       <Link\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         href='/aboutus'\n//                         prefetch={false}\n//                       >\n//                         About Us\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         href='/company/rewards'\n//                         prefetch={false}\n//                       >\n//                         Rewards\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         href='blog'\n//                         prefetch={false}\n//                       >\n//                         Blogs\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <button\n//                         onClick={openContactPopup}\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                       >\n//                         Contact Us\n//                       </button>\n//                     </li>\n//                   </ul>\n//                 )}\n//               </li>\n//               {/* Help */}\n//               <li>\n//                 <div\n//                   className={`flex justify-between items-center mb-6 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${\n//                     openSections.help &&\n//                     \"bg-[#D9F9F6] text-black !border-[#D9F9F6]\"\n//                   }`}\n//                   onClick={() => toggleSection(\"help\")}\n//                 >\n//                   <Link\n//                     href='/help'\n//                     className='sm:text-base text-sm font-[600] text-black'\n//                     prefetch={false}\n//                   >\n//                     Help\n//                   </Link>\n//                   {openSections.help ? <ChevronUp /> : <ChevronDown />}\n//                 </div>\n//                 {openSections.help && (\n//                   <ul className='text-base font-medium mt-[-20px] mb-3'>\n//                     <li>\n//                       <Link\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         href='faqs'\n//                         prefetch={false}\n//                       >\n//                         FAQs\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         href='privacypolicy'\n//                         prefetch={false}\n//                       >\n//                         Privacy Policy\n//                       </Link>\n//                     </li>\n//                     <li>\n//                       <Link\n//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         href='terms-condition'\n//                         prefetch={false}\n//                       >\n//                         Terms and Conditions\n//                       </Link>\n//                     </li>\n//                   </ul>\n//                 )}\n//               </li>\n//               {/* My Account */}\n//               <li>\n//                 <div\n//                   className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${\n//                     openSections.account &&\n//                     \"bg-[#D9F9F6] text-black !border-[#D9F9F6]\"\n//                   }`}\n//                   onClick={() => toggleSection(\"account\")}\n//                 >\n//                   <Link\n//                     href='/account'\n//                     className='sm:text-base text-sm font-[600] text-black'\n//                     prefetch={false}\n//                   >\n//                     My Account\n//                   </Link>\n//                   {openSections.account ? <ChevronUp /> : <ChevronDown />}\n//                 </div>\n//                 {openSections.account &&\n//                   (!hasToken ? (\n//                     <ul className='text-base font-medium mt-[-20px] mb-3'>\n//                       <li>\n//                         <button\n//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                           onClick={handleLoginClick}\n//                         >\n//                           Login\n//                         </button>\n//                       </li>\n//                       <li>\n//                         <button\n//                           onClick={handleLoginClick}\n//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         >\n//                           Signup\n//                         </button>\n//                       </li>\n//                     </ul>\n//                   ) : (\n//                     <ul className='text-base font-medium mt-[-20px] mb-3'>\n//                       <li>\n//                         <Link\n//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                           href={\n//                             role === \"user\"\n//                               ? \"/my-profile\"\n//                               : \"/owner/dashboard/profile\"\n//                           }\n//                           prefetch={false}\n//                         >\n//                           Profile\n//                         </Link>\n//                       </li>\n//                       <li>\n//                         <button\n//                           onClick={handleLogout}\n//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'\n//                         >\n//                           Logout\n//                         </button>\n//                       </li>\n//                     </ul>\n//                   ))}\n//               </li>\n//             </ul>\n//           </div>\n//         </div>\n//         {isContactPopupOpen && (\n//           <ContactPopup\n//             isOpen={isContactPopupOpen}\n//             onClose={closeContactPopup}\n//           />\n//         )}\n//       </>\n//     );\n// };\n// export default menuPopupMobile;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst menuPopupMobile = ({ isOpen, toggleMenu })=>{\n    // eslint-disable-next-line no-unused-vars\n    const [hasToken, setHasToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isContactPopupOpen, setIsContactPopupOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // eslint-disable-next-line no-unused-vars\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const menuItems = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaRegUser, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 415,\n                columnNumber: 13\n            }, undefined),\n            label: \"My Profile\",\n            href: \"/my-profile?section=profile\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GoHeart_react_icons_go__WEBPACK_IMPORTED_MODULE_10__.GoHeart, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 419,\n                columnNumber: 13\n            }, undefined),\n            label: \"Wishlist\",\n            href: \"#\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GrEdit_react_icons_gr__WEBPACK_IMPORTED_MODULE_11__.GrEdit, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 421,\n                columnNumber: 13\n            }, undefined),\n            label: \"Edit Details\",\n            href: \"/my-profile?section=edit\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__.MdCardMembership, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 426,\n                columnNumber: 13\n            }, undefined),\n            label: \"Membership\",\n            href: \"/my-profile?section=membership\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__.MdOutlineTravelExplore, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 431,\n                columnNumber: 13\n            }, undefined),\n            label: \"My Trips\",\n            href: \"/my-profile?section=stay\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoWalletOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_13__.IoWalletOutline, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 436,\n                columnNumber: 13\n            }, undefined),\n            label: \"My Wallet\",\n            href: \"/my-profile?section=wallet\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HiOutlineQuestionMarkCircle_react_icons_hi__WEBPACK_IMPORTED_MODULE_14__.HiOutlineQuestionMarkCircle, {\n                className: \"text-xl\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 441,\n                columnNumber: 13\n            }, undefined),\n            label: \"Help\",\n            href: \"/my-profile?section=help\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"menuPopupMobile.useEffect\": ()=>{\n            const token = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.getItemLocalStorage)(\"token\");\n            setHasToken(!!token);\n        }\n    }[\"menuPopupMobile.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"menuPopupMobile.useEffect\": ()=>{\n            const role = (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.getItemLocalStorage)(\"role\");\n            setRole(role);\n        }\n    }[\"menuPopupMobile.useEffect\"], []);\n    const closeContactPopup = ()=>{\n        setIsContactPopupOpen(false);\n    };\n    const [isClosing, setIsClosing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Close modal when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"menuPopupMobile.useEffect\": ()=>{\n            const handleOutsideClick = {\n                \"menuPopupMobile.useEffect.handleOutsideClick\": (event)=>{\n                    if (modalRef.current && !modalRef.current.contains(event.target)) {\n                        setIsClosing(true);\n                        setTimeout({\n                            \"menuPopupMobile.useEffect.handleOutsideClick\": ()=>{\n                                setIsClosing(false);\n                                toggleMenu(); // this sets isOpen to false\n                            }\n                        }[\"menuPopupMobile.useEffect.handleOutsideClick\"], 300);\n                    }\n                }\n            }[\"menuPopupMobile.useEffect.handleOutsideClick\"];\n            if (isOpen) {\n                document.addEventListener(\"mousedown\", handleOutsideClick);\n            }\n            return ({\n                \"menuPopupMobile.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleOutsideClick);\n                }\n            })[\"menuPopupMobile.useEffect\"];\n        }\n    }[\"menuPopupMobile.useEffect\"], [\n        isOpen,\n        toggleMenu\n    ]);\n    if (!isOpen) return null;\n    const closeWithAnimation = ()=>{\n        setIsClosing(true);\n        setTimeout(()=>{\n            setIsClosing(false);\n            toggleMenu(); // this sets isOpen to false\n        }, 300); // match this with your animation duration\n    };\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_6__.useNavbar)();\n    const handleLogout = async ()=>{\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"token\");\n        updateUserStatus(\"\");\n        updateUserRole(\"\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"name\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"role\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"email\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"contact\");\n        toggleMenu();\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(\"Logged out successfully\");\n        const payload = {\n            token: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.getItemLocalStorage)(\"FCT\"),\n            userId: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.getItemLocalStorage)(\"id\")\n        };\n        try {\n            await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.removeFirebaseToken)(payload);\n            console.log(\"FCM token removed successfully.\");\n            (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"FCT\");\n        } catch (error) {\n            console.error(\"Error removing FCM token:\", error);\n        }\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_3__.removeItemLocalStorage)(\"id\");\n        router.push(\"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-0 bg-black bg-opacity-70 z-50 backdrop-blur-sm transition-all duration-300 ${isClosing ? \"animate-slideBackdropOut\" : \"animate-slideBackdropIn\"}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 525,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed xs:w-[60%] w-[80%] h-[98%] sm:h-full sm:w-[400px] top-2 sm:top-[120px] right-0 sm:right-[120px] sm:left-auto \n            lg:bottom-0 rounded-tl-2xl rounded-bl-2xl pl-[2px] pb-[2px] z-50 flex items-start justify-end sm:bg-transparent bg-black\n            bg-opacity-[70%] transition-all duration-300 animated ${isClosing ? \"animate-fadeOutRight\" : isOpen ? \"fadeInRight\" : \"\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: modalRef,\n                    className: \"bg-white rounded-tl-2xl rounded-bl-2xl w-[100%] max-w-full xs:p-5 p-3 ml-0 mr-0 mt-0 h-full font-manrope\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#40E0D0] flex text-2xl font-extrabold\",\n                                    children: [\n                                        \"Mix\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-black text-2xl font-extrabold \",\n                                            children: \"Dorm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 18\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: closeWithAnimation,\n                                    className: \"text-black\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, undefined),\n                        menuItems.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: \"flex items-center xs:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors xs:p-3 py-2 px-3 rounded-full text-sm font-medium text-gray-800 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:text-lg text-sm\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    item.label\n                                ]\n                            }, idx, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLogout,\n                            className: \"flex items-center sm:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors p-3 rounded-full text-sm font-medium text-gray-800 sm:mt-2 mt-1 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sm:text-lg text-base\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdCardMembership_MdLogout_MdOutlineTravelExplore_react_icons_md__WEBPACK_IMPORTED_MODULE_12__.MdLogout, {\n                                        className: \"text-xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Logout\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                    lineNumber: 539,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 532,\n                columnNumber: 7\n            }, undefined),\n            isContactPopupOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contactPopup__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isContactPopupOpen,\n                onClose: closeContactPopup\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\menuPopupMobile.jsx\",\n                lineNumber: 573,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (menuPopupMobile);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/popup/menuPopupMobile.jsx\n");

/***/ }),

/***/ "./components/toast/toast.js":
/*!***********************************!*\
  !*** ./components/toast/toast.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom/client */ \"react-dom/client\");\n/* harmony import */ var react_dom_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom_client__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CheckCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCheck,X!=!lucide-react */ \"__barrel_optimize__?names=CheckCheck,X!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_IoAlertOutline_IoInformationCircleOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=IoAlertOutline,IoInformationCircleOutline!=!react-icons/io5 */ \"__barrel_optimize__?names=IoAlertOutline,IoInformationCircleOutline!=!./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaCircle!=!react-icons/fa */ \"__barrel_optimize__?names=FaCircle!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_GoAlert_react_icons_go__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=GoAlert!=!react-icons/go */ \"__barrel_optimize__?names=GoAlert!=!./node_modules/react-icons/go/index.mjs\");\n// toast.js\n\n\n\n\n\n\n\n// Toast component remains the same\nconst Toast = ({ message, type, onClose, subText, actionText })=>{\n    const [isVisible, setIsVisible] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300);\n                }\n            }[\"Toast.useEffect.timer\"], 5000);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        onClose\n    ]);\n    const getIcon = ()=>{\n        switch(type){\n            case 'error':\n                // return <svg\n                //   xmlns=\"http://www.w3.org/2000/svg\"\n                //   width={24}\n                //   height={24}\n                //   viewBox=\"0 0 24 24\"\n                //   fill=\"currentColor\"\n                //   className=\"icon icon-tabler icons-tabler-filled icon-tabler-info-hexagon min-w-[30px] min-h-[30px] w-6 h-6 text-red-500\"\n                // >\n                //   <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                //   <path d=\"M10.425 1.414a3.33 3.33 0 0 1 3.026 -.097l.19 .097l6.775 3.995l.096 .063l.092 .077l.107 .075a3.224 3.224 0 0 1 1.266 2.188l.018 .202l.005 .204v7.284c0 1.106 -.57 2.129 -1.454 2.693l-.17 .1l-6.803 4.302c-.918 .504 -2.019 .535 -3.004 .068l-.196 -.1l-6.695 -4.237a3.225 3.225 0 0 1 -1.671 -2.619l-.007 -.207v-7.285c0 -1.106 .57 -2.128 1.476 -2.705l6.95 -4.098zm1.575 9.586h-1l-.117 .007a1 1 0 0 0 0 1.986l.117 .007v3l.007 .117a1 1 0 0 0 .876 .876l.117 .007h1l.117 -.007a1 1 0 0 0 .876 -.876l.007 -.117l-.007 -.117a1 1 0 0 0 -.764 -.857l-.112 -.02l-.117 -.006v-3l-.007 -.117a1 1 0 0 0 -.876 -.876l-.117 -.007zm.01 -3l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007z\" />\n                // </svg>\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoAlertOutline_IoInformationCircleOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_3__.IoAlertOutline, {\n                    className: \"font-extrabold\",\n                    color: \"#b52333\",\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 41,\n                    columnNumber: 16\n                }, undefined);\n            case 'success':\n                // return <svg\n                //   version=\"1.1\"\n                //   id=\"Capa_1\"\n                //   xmlns=\"http://www.w3.org/2000/svg\"\n                //   xmlnsXlink=\"http://www.w3.org/1999/xlink\"\n                //   viewBox=\"0 0 50 50\"\n                //   xmlSpace=\"preserve\"\n                //   className='w-6 h-6 text-white min-w-[30px] min-h-[30px]'\n                //   fill=\"#000000\"\n                // >\n                //   <g id=\"SVGRepo_bgCarrier\" strokeWidth={0} />\n                //   <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n                //   <g id=\"SVGRepo_iconCarrier\">\n                //     {\" \"}\n                //     <circle style={{ fill: \"#25AE88\" }} cx={25} cy={25} r={25} />{\" \"}\n                //     <polyline\n                //       style={{\n                //         fill: \"none\",\n                //         stroke: \"#FFFFFF\",\n                //         strokeWidth: 2,\n                //         strokeLinecap: \"round\",\n                //         strokeLinejoin: \"round\",\n                //         strokeMiterlimit: 10\n                //       }}\n                //       points=\" 38,15 22,33 12,25 \"\n                //     />{\" \"}\n                //   </g>\n                // </svg>;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.CheckCheck, {\n                    className: \"font-extrabold\",\n                    color: \"#489c79\",\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 71,\n                    columnNumber: 16\n                }, undefined);\n            case 'info':\n                //   return <svg\n                //   xmlns=\"http://www.w3.org/2000/svg\"\n                //   width={24}\n                //   height={24}\n                //   viewBox=\"0 0 24 24\"\n                //   fill=\"currentColor\"\n                //   className=\"icon icon-tabler icons-tabler-filled min-w-[30px] min-h-[30px] icon-tabler-alert-square-rounded w-6 h-6 text-blue-500\"\n                // >\n                //   <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                //   <path d=\"M12 2l.642 .005l.616 .017l.299 .013l.579 .034l.553 .046c4.687 .455 6.65 2.333 7.166 6.906l.03 .29l.046 .553l.041 .727l.006 .15l.017 .617l.005 .642l-.005 .642l-.017 .616l-.013 .299l-.034 .579l-.046 .553c-.455 4.687 -2.333 6.65 -6.906 7.166l-.29 .03l-.553 .046l-.727 .041l-.15 .006l-.617 .017l-.642 .005l-.642 -.005l-.616 -.017l-.299 -.013l-.579 -.034l-.553 -.046c-4.687 -.455 -6.65 -2.333 -7.166 -6.906l-.03 -.29l-.046 -.553l-.041 -.727l-.006 -.15l-.017 -.617l-.004 -.318v-.648l.004 -.318l.017 -.616l.013 -.299l.034 -.579l.046 -.553c.455 -4.687 2.333 -6.65 6.906 -7.166l.29 -.03l.553 -.046l.727 -.041l.15 -.006l.617 -.017c.21 -.003 .424 -.005 .642 -.005zm.01 13l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007zm-.01 -8a1 1 0 0 0 -.993 .883l-.007 .117v4l.007 .117a1 1 0 0 0 1.986 0l.007 -.117v-4l-.007 -.117a1 1 0 0 0 -.993 -.883z\" />\n                // </svg>;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoAlertOutline_IoInformationCircleOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_3__.IoInformationCircleOutline, {\n                    className: \"font-extrabold\",\n                    color: \"#3187f0\",\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 85,\n                    columnNumber: 14\n                }, undefined);\n            case 'warning':\n                // return <svg\n                //   xmlns=\"http://www.w3.org/2000/svg\"\n                //   width={24}\n                //   height={24}\n                //   viewBox=\"0 0 24 24\"\n                //   fill=\"currentColor\"\n                //   className=\"icon icon-tabler icons-tabler-filled min-w-[30px] min-h-[30px] icon-tabler-alert-triangle w-6 h-6 text-yellow-500\"\n                // >\n                //   <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                //   <path d=\"M12 1.67c.955 0 1.845 .467 2.39 1.247l.105 .16l8.114 13.548a2.914 2.914 0 0 1 -2.307 4.363l-.195 .008h-16.225a2.914 2.914 0 0 1 -2.582 -4.2l.099 -.185l8.11 -13.538a2.914 2.914 0 0 1 2.491 -1.403zm.01 13.33l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007zm-.01 -7a1 1 0 0 0 -.993 .883l-.007 .117v4l.007 .117a1 1 0 0 0 1.986 0l.007 -.117v-4l-.007 -.117a1 1 0 0 0 -.993 -.883z\" />\n                // </svg>;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GoAlert_react_icons_go__WEBPACK_IMPORTED_MODULE_5__.GoAlert, {\n                    className: \"font-extrabold\",\n                    color: \"#fac42b\",\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 99,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const getSubTextColor = ()=>{\n        switch(type){\n            case 'error':\n                return 'red-500';\n            case 'success':\n                return 'green-600';\n            case 'info':\n                return 'blue-500';\n            case 'warning':\n                return 'yellow-600';\n            default:\n                return 'gray-500';\n        }\n    };\n    const getBg = ()=>{\n        switch(type){\n            case 'error':\n                return 'bg-[#ffdde2]';\n            case 'success':\n                return 'bg-[#c4efd5]';\n            case 'info':\n                return 'bg-[#e7eefa]';\n            case 'warning':\n                return 'bg-[#fef7ea]';\n            default:\n                return 'bg-gray-100';\n        }\n    };\n    const getCircle = ()=>{\n        switch(type){\n            case 'error':\n                return '#eca6ad';\n            case 'success':\n                return '#99deb9';\n            case 'info':\n                return '#88beff';\n            case 'warning':\n                return '#ffdf7fc7';\n            default:\n                return 'bg-gray-100';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `transform transition-all duration-300 ease-in-out max-w-full w-full animated ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${getBg()} relative rounded-xl overflow-hidden shadow-lg shadow-gray-500 sm:p-4 p-2 mb-4 flex items-start justify-between w-full`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaCircle, {\n                            className: \"absolute -top-8 -left-8 z-10 w-[50px] h-[50px] sm:w-[60px] sm:h-[60px]\",\n                            color: `${getCircle()}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaCircle, {\n                            className: \"absolute -bottom-9 left-0 z-10\",\n                            color: `${getCircle()}`,\n                            size: 40\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCircle_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaCircle, {\n                            className: \"absolute top-4 left-7 z-10\",\n                            color: `${getCircle()}`,\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-[2px] rounded-full sm:min-w-10 sm:min-h-10 min-w-8 min-h-8 bg-white flex items-center z-20 justify-center\",\n                            children: getIcon()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-black sm:text-[15px] text-[14px] font-bold mt-0`,\n                                    children: message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                subText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-black/60 font-medium sm:text-[13px] text-[12px]\",\n                                    children: subText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined),\n                                actionText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-blue-500 text-[16px] mt-2 hover:text-blue-600\",\n                                    children: actionText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    className: \"text-black hover:text-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCheck_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.X, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-0 left-0 w-[97%] z-20 ml-1 h-[4px] bg-transparent overflow-hidden rounded-b-xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `h-full bg-${getSubTextColor()} animate-toastProgress rounded-e-0`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\n// Create a toast container manager\nconst createToastContainer = ()=>{\n    const container = document.createElement('div');\n    container.className = 'fixed top-[50px] right-[25px] z-[9999] space-y-4 sm:w-[300px] w-max max-w-full';\n    document.body.appendChild(container);\n    return (0,react_dom_client__WEBPACK_IMPORTED_MODULE_2__.createRoot)(container);\n};\nlet toastRoot = null;\nlet toasts = [];\nconst renderToasts = ()=>{\n    if (!toastRoot) {\n        toastRoot = createToastContainer();\n    }\n    toastRoot.render(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                ...toast,\n                onClose: ()=>{\n                    toasts = toasts.filter((t)=>t.id !== toast.id);\n                    renderToasts();\n                }\n            }, toast.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\toast\\\\toast.js\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, undefined));\n};\n// Toast API\nconst toast = {\n    show: (message, type, options = {})=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        toasts = [\n            ...toasts,\n            {\n                id,\n                message,\n                type,\n                ...options\n            }\n        ];\n        renderToasts();\n        return id;\n    },\n    success: (message, options = {})=>toast.show(message, 'success', options),\n    error: (message, options = {})=>toast.show(message, 'error', options),\n    info: (message, options = {})=>toast.show(message, 'info', options),\n    warning: (message, options = {})=>toast.show(message, 'warning', options)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (toast);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/toast/toast.js\n");

/***/ }),

/***/ "./services/webflowServices.jsx":
/*!**************************************!*\
  !*** ./services/webflowServices.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateOrder: () => (/* binding */ CreateOrder),\n/* harmony export */   PaymentVarification: () => (/* binding */ PaymentVarification),\n/* harmony export */   addAmountToWalletApi: () => (/* binding */ addAmountToWalletApi),\n/* harmony export */   addReviewApi: () => (/* binding */ addReviewApi),\n/* harmony export */   cancelBookingApi: () => (/* binding */ cancelBookingApi),\n/* harmony export */   checkoutLoginApi: () => (/* binding */ checkoutLoginApi),\n/* harmony export */   contactUsApi: () => (/* binding */ contactUsApi),\n/* harmony export */   deleteAccountApi: () => (/* binding */ deleteAccountApi),\n/* harmony export */   editProfileApi: () => (/* binding */ editProfileApi),\n/* harmony export */   eventApi: () => (/* binding */ eventApi),\n/* harmony export */   forgotPassApi: () => (/* binding */ forgotPassApi),\n/* harmony export */   getBlogApi: () => (/* binding */ getBlogApi),\n/* harmony export */   getBlogDetailsApi: () => (/* binding */ getBlogDetailsApi),\n/* harmony export */   getBookingDetailsApi: () => (/* binding */ getBookingDetailsApi),\n/* harmony export */   getCalenderApi: () => (/* binding */ getCalenderApi),\n/* harmony export */   getCityListApi: () => (/* binding */ getCityListApi),\n/* harmony export */   getFeaturedHostelApi: () => (/* binding */ getFeaturedHostelApi),\n/* harmony export */   getHomePagePropertyCountApi: () => (/* binding */ getHomePagePropertyCountApi),\n/* harmony export */   getHostelDeatil: () => (/* binding */ getHostelDeatil),\n/* harmony export */   getMyEventApi: () => (/* binding */ getMyEventApi),\n/* harmony export */   getMyRideApi: () => (/* binding */ getMyRideApi),\n/* harmony export */   getMyStayApi: () => (/* binding */ getMyStayApi),\n/* harmony export */   getNoticeApi: () => (/* binding */ getNoticeApi),\n/* harmony export */   getProfileApi: () => (/* binding */ getProfileApi),\n/* harmony export */   getProfileTravelingApi: () => (/* binding */ getProfileTravelingApi),\n/* harmony export */   getProfileViewsApi: () => (/* binding */ getProfileViewsApi),\n/* harmony export */   getPropertyCountApi: () => (/* binding */ getPropertyCountApi),\n/* harmony export */   getRecentSearchApi: () => (/* binding */ getRecentSearchApi),\n/* harmony export */   getReviewApi: () => (/* binding */ getReviewApi),\n/* harmony export */   getRoomData: () => (/* binding */ getRoomData),\n/* harmony export */   getTopHostelByCountryApi: () => (/* binding */ getTopHostelByCountryApi),\n/* harmony export */   getTopHostelByCountryForExploreWorldApi: () => (/* binding */ getTopHostelByCountryForExploreWorldApi),\n/* harmony export */   getTravelActivitesApi: () => (/* binding */ getTravelActivitesApi),\n/* harmony export */   getWalletDataApi: () => (/* binding */ getWalletDataApi),\n/* harmony export */   getlistApi: () => (/* binding */ getlistApi),\n/* harmony export */   getlistApiPagination: () => (/* binding */ getlistApiPagination),\n/* harmony export */   likeUnlikePropertyApi: () => (/* binding */ likeUnlikePropertyApi),\n/* harmony export */   logInApi: () => (/* binding */ logInApi),\n/* harmony export */   newsLetterSubscribeApi: () => (/* binding */ newsLetterSubscribeApi),\n/* harmony export */   registerApi: () => (/* binding */ registerApi),\n/* harmony export */   resendOtpApi: () => (/* binding */ resendOtpApi),\n/* harmony export */   resetPassApi: () => (/* binding */ resetPassApi),\n/* harmony export */   searchAutocompleteApi: () => (/* binding */ searchAutocompleteApi),\n/* harmony export */   verifyOtp: () => (/* binding */ verifyOtp)\n/* harmony export */ });\n/* harmony import */ var _httpServices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpServices */ \"./services/httpServices.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_httpServices__WEBPACK_IMPORTED_MODULE_0__]);\n_httpServices__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst getlistApi = (state)=>{\n    // return httpServices.get(`/otaProperties?search=${state}&checkIn=${checkIn}&checkOut=${checkOut}&guest=3`)\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/otaProperties?search=${state}`);\n};\nconst getlistApiPagination = (state, currentPage, propertiesPerPage, sort, checkIn, checkOut, currency, guest, category)=>{\n    // return httpServices.get(`/otaProperties?search=${state}&checkIn=${checkIn}&checkOut=${checkOut}&guest=3`)\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/otaProperties?search=${state}&sortCondition=${sort}&page=${currentPage}&limit=${propertiesPerPage}&checkIn=${checkIn}&checkOut=${checkOut}&currency=${currency}&guest=${guest}&tag=${category}`);\n};\nconst getHostelDeatil = (id, checkIn, checkOut, currency)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/otaProperties/property/${id}?checkIn=${checkIn}&checkOut=${checkOut}&currency=${currency}`);\n};\nconst registerApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/register`, payload);\n};\nconst logInApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/login/`, payload);\n};\nconst verifyOtp = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/verify-otp/`, payload);\n};\nconst resendOtpApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/verify-email/`, payload);\n};\nconst getCalenderApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`booking/check-booking-range`, payload);\n};\nconst forgotPassApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/forgot-password/`, payload);\n};\nconst resetPassApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/auth/reset-password/`, payload);\n};\nconst getRoomData = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/booking/checkout`, payload);\n};\nconst getProfileApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/auth/profile/`, payload);\n};\nconst editProfileApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/auth/profile/`, payload);\n};\nconst CreateOrder = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/api/payment/createOrder/`, payload);\n};\nconst PaymentVarification = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/api/payment/paymentVerification`, payload);\n};\nconst contactUsApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/contact-us`, payload);\n};\nconst addReviewApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/reviews`, payload);\n};\nconst getReviewApi = (id, currentPage, reviewPerPage)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/reviews/all/${id}?page=${currentPage}&limit=${reviewPerPage}`);\n};\nconst getMyStayApi = (currentPage, limit)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/users/my-stays?page=${currentPage}&limit=${limit}`);\n};\nconst getMyEventApi = ()=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/eventBookings/my-events`);\n};\nconst getPropertyCountApi = ()=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/property/my-properties-counts`);\n};\nconst getMyRideApi = (currentPage, limit)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/rides/user?page=${currentPage}&limit=${limit}`);\n};\nconst getProfileTravelingApi = ()=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/users/profile/traveling-details`);\n};\nconst getBlogApi = (currentPage, limit)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/blog?page=${currentPage}&limit=${limit}`);\n};\nconst getBlogDetailsApi = (id)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/blog/${id}`);\n};\nconst getFeaturedHostelApi = (currency)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/top-featured-hostels?currency=${currency}`);\n};\nconst getTopHostelByCountryApi = (country, currency, selectedCity)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/top-hostels/${country}?city=${selectedCity}&currency=${currency}`);\n};\nconst getTopHostelByCountryForExploreWorldApi = (country, currency)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/top-hostels?countries=${country}&currency=${currency}`);\n};\nconst getTravelActivitesApi = (category)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/travel-activities?category=${category}`);\n};\nconst getRecentSearchApi = ()=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/recent-searches`);\n};\nconst getNoticeApi = ()=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/noticeboard`);\n};\nconst newsLetterSubscribeApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/pages/news-letter/subscribe`, payload);\n};\nconst likeUnlikePropertyApi = (id, payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/wishlists/like/${id}`, payload);\n};\nconst searchAutocompleteApi = (search)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/search/autocomplete?search=${search}`);\n};\nconst deleteAccountApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/remove-account`, payload);\n};\n// export const getBookingDetailsApi=(id)=>{\n//   return httpServices.get(`booking/${id}`)\n// }\nconst getBookingDetailsApi = (id)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/users/my-stays?id=${id}`);\n};\nconst cancelBookingApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/booking/cancel`, payload);\n};\nconst eventApi = (currentPage, limit)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/events?page=${currentPage}&limit=${limit}`);\n};\nconst getProfileViewsApi = ()=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/profileViews`);\n};\nconst getHomePagePropertyCountApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/pages/properties-counts`, payload);\n};\nconst checkoutLoginApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/auth/loginOrRegister`, payload);\n};\nconst getWalletDataApi = (id)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/wallets/balance/${id}`);\n};\nconst addAmountToWalletApi = (payload)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/wallets/add-balance`, payload);\n};\nconst getCityListApi = (country)=>{\n    return _httpServices__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/pages/cities-list?country=${country}`);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./services/webflowServices.jsx\n");

/***/ }),

/***/ "__barrel_optimize__?names=Box,Divider,Drawer,Paper!=!./node_modules/@mui/material/node/index.js":
/*!*******************************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,Divider,Drawer,Paper!=!./node_modules/@mui/material/node/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=CheckCheck,X!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*****************************************************************************************************!*\
  !*** __barrel_optimize__?names=CheckCheck,X!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=ChevronDown,Globe,Grip,User,X!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!**********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronDown,Globe,Grip,User,X!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaCircle!=!./node_modules/react-icons/fa/index.mjs":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=FaCircle!=!./node_modules/react-icons/fa/index.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaRegUser!=!./node_modules/react-icons/fa/index.mjs":
/*!*************************************************************************************!*\
  !*** __barrel_optimize__?names=FaRegUser!=!./node_modules/react-icons/fa/index.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=GoAlert!=!./node_modules/react-icons/go/index.mjs":
/*!***********************************************************************************!*\
  !*** __barrel_optimize__?names=GoAlert!=!./node_modules/react-icons/go/index.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_go_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/go/index.mjs */ "./node_modules/react-icons/go/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_go_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_go_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=GoHeart!=!./node_modules/react-icons/go/index.mjs":
/*!***********************************************************************************!*\
  !*** __barrel_optimize__?names=GoHeart!=!./node_modules/react-icons/go/index.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_go_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/go/index.mjs */ "./node_modules/react-icons/go/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_go_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_go_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=GrEdit!=!./node_modules/react-icons/gr/index.mjs":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=GrEdit!=!./node_modules/react-icons/gr/index.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gr_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/gr/index.mjs */ "./node_modules/react-icons/gr/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gr_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gr_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=HiMenuAlt3!=!./node_modules/react-icons/hi/index.mjs":
/*!**************************************************************************************!*\
  !*** __barrel_optimize__?names=HiMenuAlt3!=!./node_modules/react-icons/hi/index.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_hi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/hi/index.mjs */ "./node_modules/react-icons/hi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_hi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_hi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=HiOutlineQuestionMarkCircle!=!./node_modules/react-icons/hi/index.mjs":
/*!*******************************************************************************************************!*\
  !*** __barrel_optimize__?names=HiOutlineQuestionMarkCircle!=!./node_modules/react-icons/hi/index.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_hi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/hi/index.mjs */ "./node_modules/react-icons/hi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_hi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_hi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=IoAlertOutline,IoInformationCircleOutline!=!./node_modules/react-icons/io5/index.mjs":
/*!**********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=IoAlertOutline,IoInformationCircleOutline!=!./node_modules/react-icons/io5/index.mjs ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/io5/index.mjs */ "./node_modules/react-icons/io5/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=IoWalletOutline!=!./node_modules/react-icons/io5/index.mjs":
/*!********************************************************************************************!*\
  !*** __barrel_optimize__?names=IoWalletOutline!=!./node_modules/react-icons/io5/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/io5/index.mjs */ "./node_modules/react-icons/io5/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=MdCardMembership,MdLogout,MdOutlineTravelExplore!=!./node_modules/react-icons/md/index.mjs":
/*!****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=MdCardMembership,MdLogout,MdOutlineTravelExplore!=!./node_modules/react-icons/md/index.mjs ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=MdKeyboardArrowDown,MdKeyboardArrowUp!=!./node_modules/react-icons/md/index.mjs":
/*!*****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=MdKeyboardArrowDown,MdKeyboardArrowUp!=!./node_modules/react-icons/md/index.mjs ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=MdOutlineNotificationsActive!=!./node_modules/react-icons/md/index.mjs":
/*!********************************************************************************************************!*\
  !*** __barrel_optimize__?names=MdOutlineNotificationsActive!=!./node_modules/react-icons/md/index.mjs ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=Modal!=!./node_modules/@mui/material/node/index.js":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=Modal!=!./node_modules/@mui/material/node/index.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;