"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_superadmin_SuperLayout_jsx";
exports.ids = ["_pages-dir-node_components_superadmin_SuperLayout_jsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/superadmin/SuperLayout.jsx":
/*!***********************************************!*\
  !*** ./components/superadmin/SuperLayout.jsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_MdManageAccounts_react_icons_md__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MdManageAccounts!=!react-icons/md */ \"(pages-dir-node)/__barrel_optimize__?names=MdManageAccounts!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaKey_FaUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaKey,FaUser!=!react-icons/fa6 */ \"(pages-dir-node)/__barrel_optimize__?names=FaKey,FaUser!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _SuperSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SuperSidebar */ \"(pages-dir-node)/./components/superadmin/SuperSidebar.jsx\");\n/* harmony import */ var _fontsource_nunito_sans__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @fontsource/nunito-sans */ \"(pages-dir-node)/./node_modules/@fontsource/nunito-sans/index.css\");\n/* harmony import */ var _fontsource_nunito_sans__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_fontsource_nunito_sans__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_SuperSidebar__WEBPACK_IMPORTED_MODULE_3__]);\n_SuperSidebar__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst SuperLayout = ()=>{\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"English\"); // Default selected language\n    const [isProfileDropdownOpen, setIsProfileDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false); // Profile dropdown state\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false); // State for sidebar visibility on mobile\n    const toggleDropdown = ()=>{\n        setIsDropdownOpen(!isDropdownOpen);\n    };\n    const toggleProfileDropdown = ()=>{\n        setIsProfileDropdownOpen(!isProfileDropdownOpen);\n    };\n    const selectLanguage = (language)=>{\n        setSelectedLanguage(language);\n        setIsDropdownOpen(false); // Close dropdown after selection\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SuperLayout.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                    setIsProfileDropdownOpen(false);\n                }\n            }\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"SuperLayout.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"SuperLayout.useEffect\"];\n        }\n    }[\"SuperLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuperSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                setIsSidebarOpen: setIsSidebarOpen,\n                isSidebarOpen: isSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center shadow justify-between w-full h-auto px-2 md:px-5 lg:px-5 py-3 bg-white dark:bg-[#0D0D0D] text-black-100 lg:pl-[255px] md:pl-[255px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n                                    className: \"duration-200 ease-in-out cursor-pointer text-black-100 hover:text-black dark:text-[#B6B6B6]\",\n                                    size: 20,\n                                    onClick: ()=>setIsSidebarOpen(!isSidebarOpen)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full sm:w-60 lg:w-80 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"search\",\n                                        placeholder: \"Search for rooms and offers\",\n                                        className: \"pl-8 bg-gray-100 text-gray-200 rounded-full py-2 w-full outline-none border-2 border-gray-200 text-xs font-poppins dark:bg-[#0D0D0D] dark:border dark:text-[#616161]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Search, {\n                                        className: \"absolute  text-gray-500 dark:text-[#616161] top-1/2 left-2.5 transform -translate-y-1/2\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-3 md:gap-x-4 lg:gap-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"cursor-pointer text-gray-540 dark:text-[#B6B6B6]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.BellRing, {\n                                    size: 20,\n                                    className: \"font-light\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center text-gray-540 dark:text-[#B6B6B6] gap-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:flex md:flex items-center hidden gap-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Globe, {\n                                                size: 20,\n                                                className: \"font-light\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer text-sm flex items-center gap-1\",\n                                                onClick: toggleDropdown,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: selectedLanguage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronDown, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden md:hidden\",\n                                        onClick: toggleDropdown,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Globe, {\n                                            size: 20,\n                                            className: \"font-light\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed inset-0 bg-black/30 backdrop-blur-sm z-10\",\n                                        onClick: toggleDropdown\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full right-0 mt-2 w-52 bg-white dark:bg-[#171616] rounded-xl shadow-lg z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"px-4 py-3 font-nunito text-sm lg:text-base  font-normal\",\n                                                children: \"Select Language\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    \"English\",\n                                                    \"French\",\n                                                    \"Spanish\"\n                                                ].map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"px-4 py-4 hover:bg-gray-100 dark:hover:bg-[#474646d0] cursor-pointer flex items-center justify-between\",\n                                                        onClick: ()=>selectLanguage(language),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 text-sm\",\n                                                                children: [\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                        src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/${language}.png`,\n                                                                        width: 44,\n                                                                        height: 30,\n                                                                        alt: language\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                        lineNumber: 162,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    language\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            selectedLanguage === language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                                                size: 20,\n                                                                className: \"text-black dark:text-[#B6B6B6]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, language, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center gap-x-2\",\n                                ref: dropdownRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex lg:hidden items-center justify-center text-neutral-850 dark:text-[#B6B6B6]\",\n                                        onClick: toggleProfileDropdown,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaKey_FaUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_6__.FaUser, {\n                                            className: \"text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:flex items-center gap-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center text-white rounded-full w-11 h-11 bg-yellow-550\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-base font-bold\",\n                                                    onClick: toggleProfileDropdown,\n                                                    children: \"M\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-bold text-neutral-700 dark:text-[#B6B6B6]\",\n                                                        onClick: toggleProfileDropdown,\n                                                        children: \"Moni Roy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs font-semibold text-neutral-850 dark:text-[#B6B6B6]\",\n                                                        children: \"Admin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:flex items-center justify-center w-5 h-5 duration-200 ease-in-out border rounded-full border-neutral-850 text-neutral-850 dark:text-[#B6B6B6]\",\n                                        onClick: toggleProfileDropdown,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronDown, {\n                                            size: 16,\n                                            className: `${isProfileDropdownOpen ? \"rotate-180 mb-0.5\" : \"\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isProfileDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full right-0 mt-2 w-56 bg-white dark:bg-[#171616] rounded-lg shadow-lg z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"lg:hidden items-center flex px-3 pt-2 gap-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center text-white rounded-full w-11 h-11 bg-yellow-550\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-base font-bold\",\n                                                                children: \"M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-bold text-neutral-700 dark:text-[#B6B6B6]\",\n                                                                    children: \"Moni Roy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-semibold text-neutral-850 dark:text-[#B6B6B6]\",\n                                                                    children: \"Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdManageAccounts_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdManageAccounts, {\n                                                            size: 22,\n                                                            className: \"text-sky-blue-650\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Manage Profiles\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaKey_FaUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_6__.FaKey, {\n                                                            size: 16,\n                                                            className: \"text-[#8080FF]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Change Password\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/LogOut.png`,\n                                                            width: 14,\n                                                            height: 12,\n                                                            alt: \"LogOut\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Log out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SuperLayout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/superadmin/SuperLayout.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/superadmin/SuperSidebar.jsx":
/*!************************************************!*\
  !*** ./components/superadmin/SuperSidebar.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(pages-dir-node)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Box!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/browserSetting */ \"(pages-dir-node)/./utils/browserSetting.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/ownerflowServices */ \"(pages-dir-node)/./services/ownerflowServices.jsx\");\n/* harmony import */ var _ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ownerFlow/headerContex */ \"(pages-dir-node)/./components/ownerFlow/headerContex.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../home/<USER>/ \"(pages-dir-node)/./components/home/<USER>");\n/* harmony import */ var _barrel_optimize_names_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=FiSettings!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiSettings!=!./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_PiSquaresFourBold_react_icons_pi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=PiSquaresFourBold!=!react-icons/pi */ \"(pages-dir-node)/__barrel_optimize__?names=PiSquaresFourBold!=!./node_modules/react-icons/pi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RxCross2_react_icons_rx__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=RxCross2!=!react-icons/rx */ \"(pages-dir-node)/__barrel_optimize__?names=RxCross2!=!./node_modules/react-icons/rx/index.mjs\");\n/* harmony import */ var _ThemeSwitcher__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ThemeSwitcher */ \"(pages-dir-node)/./components/superadmin/ThemeSwitcher.jsx\");\n/* harmony import */ var _barrel_optimize_names_BiLogOutCircle_react_icons_bi__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BiLogOutCircle!=!react-icons/bi */ \"(pages-dir-node)/__barrel_optimize__?names=BiLogOutCircle!=!./node_modules/react-icons/bi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaRegCircleUser_FaRegUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!react-icons/fa6 */ \"(pages-dir-node)/__barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_GiKeyCard_GiNotebook_react_icons_gi__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=GiKeyCard,GiNotebook!=!react-icons/gi */ \"(pages-dir-node)/__barrel_optimize__?names=GiKeyCard,GiNotebook!=!./node_modules/react-icons/gi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=RiArrowDropDownLine!=!react-icons/ri */ \"(pages-dir-node)/__barrel_optimize__?names=RiArrowDropDownLine!=!./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!react-icons/lu */ \"(pages-dir-node)/__barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!./node_modules/react-icons/lu/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbBrandWechat_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=TbBrandWechat!=!react-icons/tb */ \"(pages-dir-node)/__barrel_optimize__?names=TbBrandWechat!=!./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdOutlineRateReview_react_icons_md__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineRateReview!=!react-icons/md */ \"(pages-dir-node)/__barrel_optimize__?names=MdOutlineRateReview!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaHandHoldingUsd_FaHeadset_react_icons_fa__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _fontsource_poppins__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fontsource/poppins */ \"(pages-dir-node)/./node_modules/@fontsource/poppins/index.css\");\n/* harmony import */ var _fontsource_poppins__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_fontsource_poppins__WEBPACK_IMPORTED_MODULE_10__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__, _ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__]);\n([_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__, _ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SuperSidebar = ({ isCollapsed, isSidebarOpen, setIsSidebarOpen })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\"); // State for active menu item\n    // eslint-disable-next-line no-unused-vars\n    const [flags, setFlags] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [isContentOpen, setIsContentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isManagementOpen, setIsManagementOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isPaymentOpen, setIsPaymentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isLandingPageOpen, setIsLandingPageOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isOtherContentOpen, setIsOtherContentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { updateUserStatus, updateUserRole, updateHopId } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_8__.useNavbar)();\n    // const [activeItem, setActiveItem] = useState({ name: \"\", url: \"\" });\n    const [activeDropdownItem, setActiveDropdownItem] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\"); // Tracks the active dropdown item\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SuperSidebar.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {\n                    setIsSidebarOpen(false);\n                }\n            }\n            if (isSidebarOpen) {\n                document.addEventListener(\"mousedown\", handleClickOutside);\n            } else {\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n            }\n            return ({\n                \"SuperSidebar.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"SuperSidebar.useEffect\"];\n        }\n    }[\"SuperSidebar.useEffect\"], [\n        isSidebarOpen\n    ]);\n    const handleDropdownItemClick = (dropdownItem)=>{\n        setActiveDropdownItem(dropdownItem);\n        setActiveItem(\"Content\"); // Keep the parent \"Content\" option highlighted\n    };\n    // const handleItemClick = (item) => {\n    //   setActiveItem(item);\n    //   setActiveDropdownItem(\"\"); // Clear dropdown selection if another main item is selected\n    // };\n    const handleItemClick = (name)=>{\n        setActiveItem(name);\n        // setActiveItem({ name: itemName, url: itemUrl })// Set the active menu item\n        if (name === \"Multiple Property\") {\n            handleOpenMultipleProperty();\n            setActiveDropdownItem(\"\");\n        }\n        if (window.innerWidth < 640) {\n            setIsSidebarOpen(false); // Close sidebar on mobile after selecting\n        }\n    };\n    const handleLogout = async ()=>{\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"token\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"name\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"id\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"role\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"hopid\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"email\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"contact\");\n        updateUserStatus(\"\");\n        updateUserRole(\"\");\n        updateHopId(\"\");\n        const payload = {\n            token: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.getItemLocalStorage)(\"FCT\"),\n            userId: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.getItemLocalStorage)(\"uid\")\n        };\n        try {\n            await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__.removeFirebaseToken)(payload);\n            console.log(\"FCM token removed successfully.\");\n            (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"FCT\");\n        } catch (error) {\n            console.error(\"Error removing FCM token:\", error);\n        }\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"uid\");\n        router.push(\"/owner/login\");\n    };\n    // eslint-disable-next-line no-unused-vars\n    const [openMultipleProperty, setOpenMultipleProperty] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const handleOpenMultipleProperty = ()=>setOpenMultipleProperty(true);\n    const { profileData, propertyData } = (0,_ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__.useHeaderOwner)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SuperSidebar.useEffect\": ()=>{\n            const fetchFlags = {\n                \"SuperSidebar.useEffect.fetchFlags\": async ()=>{\n                    try {\n                        const response = await fetch(\"https://restcountries.com/v3.1/all\");\n                        const data = await response.json();\n                        const filteredFlags = data.filter({\n                            \"SuperSidebar.useEffect.fetchFlags.filteredFlags\": (country)=>{\n                                const commonName = country.name.common;\n                                return propertyData?.address?.country?.includes(commonName);\n                            }\n                        }[\"SuperSidebar.useEffect.fetchFlags.filteredFlags\"]).map({\n                            \"SuperSidebar.useEffect.fetchFlags.filteredFlags\": (country)=>({\n                                    id: country.cca3,\n                                    img: country.flags.svg,\n                                    name: country.name.common\n                                })\n                        }[\"SuperSidebar.useEffect.fetchFlags.filteredFlags\"]);\n                        setFlags(filteredFlags);\n                    } catch (error) {\n                        console.error(\"Error fetching flags:\", error);\n                    }\n                }\n            }[\"SuperSidebar.useEffect.fetchFlags\"];\n            if (propertyData?.address?.country) {\n                fetchFlags();\n            }\n        }\n    }[\"SuperSidebar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SuperSidebar.useEffect\": ()=>{\n            const setActiveItemFromPath = {\n                \"SuperSidebar.useEffect.setActiveItemFromPath\": (path)=>{\n                    const dashboardPath = path.split(\"/dashboard\")[1];\n                    if (!dashboardPath || dashboardPath === \"/\") {\n                        setActiveItem(\"Dashboard\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/user\")) {\n                        setActiveItem(\"User\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/booking\")) {\n                        setActiveItem(\"Booking\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/hostel\")) {\n                        setActiveItem(\"Hostel\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/communication\")) {\n                        setActiveItem(\"Communication\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/notification\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/announcement\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/create-notification\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/bulk-notification\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/feedback\")) {\n                        setActiveItem(\"Feedback\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/support\")) {\n                        setActiveItem(\"Support\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/profile\")) {\n                        setActiveItem(\"Profile\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    }\n                    // } else if (dashboardPath.includes(\"/payment\")) {\n                    //   setActiveItem(\"Payment\");\n                    //   setIsContentOpen(false);\n                    //   setIsLandingPageOpen(false);\n                    //   setActiveDropdownItem(\"\");\n                    // }\n                    // Handle Content submenu items\n                    if (dashboardPath.includes(\"/mobile-homepage\")) {\n                        setActiveItem(\"Content\", \"Mobile Home Page\");\n                        setActiveDropdownItem(\"Mobile Home Page\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/content-banner\")) {\n                        setActiveItem(\"Content\", \"Banner\");\n                        setActiveDropdownItem(\"Banner\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/explore-world\")) {\n                        setActiveItem(\"Content\", \"Explore the World\");\n                        setActiveDropdownItem(\"Explore the World\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/featured-hostel\")) {\n                        setActiveItem(\"Content\", \"Featured Hostel\");\n                        setActiveDropdownItem(\"Featured Hostel\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/travel-activity\")) {\n                        setActiveItem(\"Content\", \"Travel by Activity\");\n                        setActiveDropdownItem(\"Travel by Activity\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/split-bill\")) {\n                        setActiveItem(\"Content\", \"Split bill fairfare, Mix mate, Mix ride\");\n                        setActiveDropdownItem(\"Split bill fairfare, Mix mate, Mix ride\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/event\")) {\n                        setActiveItem(\"Content\", \"Event\");\n                        setActiveDropdownItem(\"Event\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/content-blog\")) {\n                        setActiveItem(\"Content\", \"Landing-Blog\");\n                        setActiveDropdownItem(\"Landing-Blog\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/about-us\")) {\n                        setActiveItem(\"Content\", \"About Us\");\n                        setActiveDropdownItem(\"About Us\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/awards\")) {\n                        setActiveItem(\"Content\", \"Awards\");\n                        setActiveDropdownItem(\"Awards\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/help\")) {\n                        setActiveItem(\"Content\", \"Help\");\n                        setActiveDropdownItem(\"Help\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/booking-guarantee\")) {\n                        setActiveItem(\"Content\", \"Booking Guarantee\");\n                        setActiveDropdownItem(\"Booking Guarantee\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/booking-refund-policy\")) {\n                        setActiveItem(\"Content\", \"Booking Refund Policy\");\n                        setActiveDropdownItem(\"Booking Refund Policy\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/privacy-policy\")) {\n                        setActiveItem(\"Content\", \"Privacy Policy\");\n                        setActiveDropdownItem(\"Privacy Policy\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/terms&conditions\")) {\n                        setActiveItem(\"Content\", \"Terms & Conditions\");\n                        setActiveDropdownItem(\"Terms & Conditions\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/faqs\")) {\n                        setActiveItem(\"Content\", \"Faqs\");\n                        setActiveDropdownItem(\"Faqs\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/offer-banner\")) {\n                        setActiveItem(\"Content\", \"Offer Banner\");\n                        setActiveDropdownItem(\"Offer Banner\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/blog\")) {\n                        setActiveItem(\"Content\", \"Blog\");\n                        setActiveDropdownItem(\"Blog\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/contact\")) {\n                        setActiveItem(\"Content\", \"Contact Us\");\n                        setActiveDropdownItem(\"Contact Us\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    }\n                    // Handle Management submenu items\n                    if (dashboardPath.includes(\"/management-admin\")) {\n                        setActiveItem(\"Management\", \"Admin\");\n                        setIsContentOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsManagementOpen(true);\n                        setActiveDropdownItem(\"Admin\");\n                    } else if (dashboardPath.includes(\"/management-audit\")) {\n                        setActiveItem(\"Management\", \"Audit Logs\");\n                        setIsContentOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsManagementOpen(true);\n                        setActiveDropdownItem(\"Audit Logs\");\n                    } else if (dashboardPath.includes(\"/management-status\")) {\n                        setActiveItem(\"Management\", \"Status\");\n                        setIsContentOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsManagementOpen(true);\n                        setActiveDropdownItem(\"Status\");\n                    }\n                    // Handle Management submenu items\n                    if (dashboardPath.includes(\"/payment-booking\")) {\n                        setActiveItem(\"Payment\", \"Bookings\");\n                        setIsPaymentOpen(true);\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setActiveDropdownItem(\"Bookings\");\n                    } else if (dashboardPath.includes(\"/payment-subscription\")) {\n                        setActiveItem(\"Payment\", \"Subscription\");\n                        setIsPaymentOpen(true);\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setActiveDropdownItem(\"Subscription\");\n                    } else if (dashboardPath.includes(\"/payment-event\")) {\n                        setActiveItem(\"Payment\", \"Event\");\n                        setIsPaymentOpen(true);\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setActiveDropdownItem(\"Event\");\n                    }\n                }\n            }[\"SuperSidebar.useEffect.setActiveItemFromPath\"];\n            setActiveItemFromPath(pathname);\n        }\n    }[\"SuperSidebar.useEffect\"], [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        ref: sidebarRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_11__.Box, {\n            role: \"presentation\",\n            sx: {\n                width: 327\n            },\n            className: `bg-white dark:bg-[#0D0D0D] w-[240px]  fixed z-10 transition-transform duration-300 h-[calc(100vh)] overflow-y-auto scrollbar-hide ${isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} sm:translate-x-0`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"fixed top-4 left-4 z-20 p-2 rounded-md sm:hidden\",\n                    onClick: ()=>setIsSidebarOpen(false),\n                    children: isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RxCross2_react_icons_rx__WEBPACK_IMPORTED_MODULE_12__.RxCross2, {\n                        className: \"bg-transparent text-black dark:text-[#B6B6B6]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                        lineNumber: 478,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-full gap-x-2 h-[70px] \",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            src: profileData?.profileImage?.objectURL ? profileData?.profileImage?.objectURL : `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/avatar.png`,\n                            alt: \"Profile Pic\",\n                            className: \"rounded-lg w-[40px] h-[40px]\",\n                            width: 40,\n                            height: 40,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/logo.png`,\n                            alt: \"Mixdorm\",\n                            width: 140,\n                            height: 50,\n                            title: \"Mixdorm\",\n                            className: \"object-contain w-fit h-fit max-w-20 max-h-11 flex items-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col justify-between h-[1000px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-full ${isCollapsed ? \"px-0\" : \"px-0\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"w-full text-sm  text-black-100 flex flex-col h-auto \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4  hover:border-[#50C2FF] ${activeItem === \"Dashboard\" ? \"border-l-4 border-[#50C2FF]\" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white transition duration-300 ease-in-out transform hover:scale-105 mt-0.5 hover:font-semibold ${activeItem === \"Dashboard\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Dashboard\");\n                                                router.push(\"/superadmin/dashboard\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Dashboard\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PiSquaresFourBold_react_icons_pi__WEBPACK_IMPORTED_MODULE_13__.PiSquaresFourBold, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Dashboard\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D]  hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"User\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white transition duration-300 ease-in-out transform hover:scale-105 mt-0.5 hover:font-semibold ${activeItem === \"User\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"User\");\n                                                router.push(\"/superadmin/dashboard/user\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/user\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"User\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegCircleUser_FaRegUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_14__.FaRegUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" User\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Content\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-0.5  transition duration-300 ease-in-out transform\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsContentOpen(!isContentOpen),\n                                                    className: `flex items-center gap-2 cursor-pointer font-poppins text-sm h-[48px] pl-8 pr-10 rounded-lg ${activeItem === \"Content\" || isContentOpen ? \"bg-[#50C2FF] text-white font-semibold\" : \"text-gray-700 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:text-white hover:font-semibold\"} transition duration-300 ease-in-out transform hover:scale-105`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GiKeyCard_GiNotebook_react_icons_gi__WEBPACK_IMPORTED_MODULE_15__.GiNotebook, {\n                                                            className: \"text-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" Content\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                            className: `text-2xl float-end transition-transform ${isContentOpen ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isContentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-2 space-y-4 ml-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/mobile-homepage\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Mobile Home Page\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Mobile Home Page\");\n                                                                    handleItemClick(\"Mobile Home Page\");\n                                                                },\n                                                                children: \"Mobile Home Page\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setIsLandingPageOpen(!isLandingPageOpen),\n                                                                    className: `flex items-center cursor-pointer text-sm font-poppins rounded-lg ${activeItem === \"Landing Page\" || isLandingPageOpen ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"} `,\n                                                                    children: [\n                                                                        \"Landing Page\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                                            className: `text-2xl float-end transition-transform ${isLandingPageOpen ? \"rotate-180\" : \"\"}`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                isLandingPageOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"mt-2 ml-4 space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/content-banner\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Banner\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Banner\");\n                                                                                    handleItemClick(\"Banner\");\n                                                                                },\n                                                                                children: \"Banner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/explore-world\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Explore the World\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Explore the World\");\n                                                                                    handleItemClick(\"Explore the World\");\n                                                                                },\n                                                                                children: \"Explore the World\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 628,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/featured-hostel\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Featured Hostel\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Featured Hostel\");\n                                                                                    handleItemClick(\"Featured Hostel\");\n                                                                                },\n                                                                                children: \"Featured Hostel\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/travel-activity\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Travel by Activity\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Travel by Activity\");\n                                                                                    handleItemClick(\"Travel by Activity\");\n                                                                                },\n                                                                                children: \"Travel by Activity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/split-bill\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Split bill fairfare, Mix mate, Mix ride\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Split bill fairfare, Mix mate, Mix ride\");\n                                                                                    handleItemClick(\"Split bill fairfare, Mix mate, Mix ride\");\n                                                                                },\n                                                                                children: \"Split bill fairfare, Mix mate, Mix ride\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/event\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Event\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Event\");\n                                                                                    handleItemClick(\"Event\");\n                                                                                },\n                                                                                children: \"Event\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 699,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/content-blog\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Landing-Blog\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Landing-Blog\");\n                                                                                    handleItemClick(\"Landing-Blog\");\n                                                                                },\n                                                                                children: \"Landing-Blog\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 715,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setIsOtherContentOpen(!isOtherContentOpen),\n                                                                    className: `flex items-centercursor-pointer text-sm font-poppins rounded-lg ${activeItem === \"Other Content\" || isOtherContentOpen ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"} `,\n                                                                    children: [\n                                                                        \"Other Content\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                                            className: `text-2xl float-end transition-transform ${isOtherContentOpen ? \"rotate-180\" : \"\"}`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                isOtherContentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"mt-2 ml-4 space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/about-us\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"About Us\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"About Us\");\n                                                                                    handleItemClick(\"About Us\");\n                                                                                },\n                                                                                children: \"About Us\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 754,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/awards\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Awards\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Awards\");\n                                                                                    handleItemClick(\"Awards\");\n                                                                                },\n                                                                                children: \"Awards\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/help\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Help\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Help\");\n                                                                                    handleItemClick(\"Help\");\n                                                                                },\n                                                                                children: \"Help\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/booking-guarantee\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Booking Guarantee\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Booking Guarantee\");\n                                                                                    handleItemClick(\"Booking Guarantee\");\n                                                                                },\n                                                                                children: \"Booking Guarantee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/booking-refund-policy\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Booking Refund Policy\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Booking Refund Policy\");\n                                                                                    handleItemClick(\"Booking Refund Policy\");\n                                                                                },\n                                                                                children: \"Booking Refund Policy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 820,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/privacy-policy\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Privacy Policy\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Privacy Policy\");\n                                                                                    handleItemClick(\"Privacy Policy\");\n                                                                                },\n                                                                                children: \"Privacy Policy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/terms&conditions\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Terms & Conditions\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Terms & Conditions\");\n                                                                                    handleItemClick(\"Terms & Conditions\");\n                                                                                },\n                                                                                children: \"Terms & Conditions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 857,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/faqs\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Faqs\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Faqs\");\n                                                                                    handleItemClick(\"Faqs\");\n                                                                                },\n                                                                                children: \"Faqs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/offer-banner\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Offer Banner\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Offer Banner\");\n                                                                    handleItemClick(\"Offer Banner\");\n                                                                },\n                                                                children: \"Offer Banner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/blog\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Blog\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Blog\");\n                                                                    handleItemClick(\"Blog\");\n                                                                },\n                                                                children: \"Blog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/contact\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Contact Us\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Contact Us\"), handleItemClick(\"Contact Us\");\n                                                                },\n                                                                children: \"Contact Us\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 926,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Booking\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Booking\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Booking\");\n                                                router.push(\"/superadmin/dashboard/booking\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/booking\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700  dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Booking\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__.LuCalendarDays, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Booking\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Hostel\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Hostel\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Hostel\");\n                                                router.push(\"/superadmin/dashboard/hostel\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/hostel\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Hostel\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GiKeyCard_GiNotebook_react_icons_gi__WEBPACK_IMPORTED_MODULE_15__.GiKeyCard, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Hostel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Communication\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Communication\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Communication\");\n                                                router.push(\"/superadmin/dashboard/communication\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/communication\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Communication\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbBrandWechat_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__.TbBrandWechat, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Communication\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Notification\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Notification\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Notification\");\n                                                router.push(\"/superadmin/dashboard/notifications\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/notifications\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Notification\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__.LuBellPlus, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Notification\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Feedback\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Feedback\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Feedback\");\n                                                router.push(\"/superadmin/dashboard/feedback\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/feedback\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Feedback\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineRateReview_react_icons_md__WEBPACK_IMPORTED_MODULE_19__.MdOutlineRateReview, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Feedback\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Support\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Support\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Support\");\n                                                router.push(\"/superadmin/dashboard/support\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/support\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Support\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHandHoldingUsd_FaHeadset_react_icons_fa__WEBPACK_IMPORTED_MODULE_20__.FaHeadset, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Support\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Profile\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Profile\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Profile\");\n                                                router.push(\"/superadmin/dashboard/profile\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/profile\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Profile\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegCircleUser_FaRegUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_14__.FaRegCircleUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1118,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Profile\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1099,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Management\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-0.5  transition duration-300 ease-in-out transform\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsManagementOpen(!isManagementOpen),\n                                                    className: `flex items-center gap-2 cursor-pointer text-sm font-poppins h-[48px] rounded-lg pr-1 pl-[32px] ${activeItem === \"Management\" || isManagementOpen ? \"bg-[#50C2FF] text-white font-semibold\" : \"text-gray-700 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:text-white hover:font-semibold\"} transition duration-300 ease-in-out transform hover:scale-105`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__.LuUserRoundCog, {\n                                                            className: \"text-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" Management\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                            className: `text-2xl mt-0.5 float-end transition-transform ${isManagementOpen ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1140,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isManagementOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-2 ml-14 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/management-admin\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Admin\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] dark:hover:text-[#50C2FF] hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Admin\");\n                                                                    handleItemClick(\"Admin\");\n                                                                },\n                                                                children: \"Admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1148,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/management-audit\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Audit Logs\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Audit Logs\");\n                                                                    handleItemClick(\"Audit Logs\");\n                                                                },\n                                                                children: \"Audit Logs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1165,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/management-status\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Status\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Status\");\n                                                                    handleItemClick(\"Status\");\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1182,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1147,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Payment\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-0.5 ml-1.5  transition duration-300 ease-in-out transform\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsPaymentOpen(!isPaymentOpen),\n                                                    className: `flex items-center  gap-1.5 cursor-pointer text-sm font-poppins h-[48px] rounded-lg pr-8  pl-[26px] ${activeItem === \"Payment\" || isPaymentOpen ? \"bg-[#50C2FF] text-white font-semibold\" : \"text-gray-700 hover:bg-[#50C2FF] hover:text-white hover:font-semibold dark:text-[#ffffff]\"} transition duration-300 ease-in-out transform hover:scale-105 `,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHandHoldingUsd_FaHeadset_react_icons_fa__WEBPACK_IMPORTED_MODULE_20__.FaHandHoldingUsd, {\n                                                            className: \"text-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1236,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" Payment\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                            className: `text-2xl float-end transition-transform ${isPaymentOpen ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1237,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isPaymentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-2 ml-14 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/payment-booking\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Bookings\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600  hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Bookings\");\n                                                                    handleItemClick(\"Bookings\");\n                                                                },\n                                                                children: \"Bookings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1246,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1245,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/payment-subscription\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Subscription\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Subscription\");\n                                                                    handleItemClick(\"Subscription\");\n                                                                },\n                                                                children: \"Subscription\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1263,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1262,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/payment-event\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Event\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Event\");\n                                                                    handleItemClick(\"Event\");\n                                                                },\n                                                                children: \"Event\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1279,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1278,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1222,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `mt-auto w-full ${isCollapsed ? \"px-0\" : \"px-0\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"border-1.5 p-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                    lineNumber: 1302,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"w-full text-sm font-poppins  flex flex-col h-[150px] mt-10 md:mt-5 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Theme\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: `flex items-center px-7 cursor-pointer first:mt-0 text-sm font-poppins h-[48px]  gap-x-2 text-gray-600 dark:text-[#ffffff]  rounded-lg hover:bg-[#50C2FF] hover:text-white hover:font-semibold  ${activeItem === \"Theme\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeSwitcher__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1304,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Settings\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: `flex items-center px-7 cursor-pointer first:mt-0  text-sm font-poppins rounded-lg h-[48px]  gap-x-2  text-gray-600 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:font-semibold hover:text-white  ${isCollapsed ? \"justify-center\" : \"justify-start\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiSettings, {\n                                                        className: ` ${isCollapsed ? \"\" : \"text-xl\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    isCollapsed ? \"\" : \"Settings \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1326,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1319,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-6 bg-white  dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Logout\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: `cursor-pointer text-sm font-poppins h-[48px]  px-7 flex items-center rounded-lg gap-x-2 text-gray-600 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:font-semibold hover:text-white  first:mt-0${isCollapsed ? \"justify-center\" : \"justify-start\"}`,\n                                                onClick: handleLogout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BiLogOutCircle_react_icons_bi__WEBPACK_IMPORTED_MODULE_22__.BiLogOutCircle, {\n                                                        className: `${isCollapsed ? \"\" : \"text-xl\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    isCollapsed ? \"\" : \"Logout \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1341,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1336,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                    lineNumber: 1303,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 1301,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n            lineNumber: 467,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n        lineNumber: 466,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SuperSidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/superadmin/SuperSidebar.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/superadmin/ThemeSwitcher.jsx":
/*!*************************************************!*\
  !*** ./components/superadmin/ThemeSwitcher.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_IoSunnyOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=IoSunnyOutline!=!react-icons/io5 */ \"(pages-dir-node)/__barrel_optimize__?names=IoSunnyOutline!=!./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsMoonStars_react_icons_bs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BsMoonStars!=!react-icons/bs */ \"(pages-dir-node)/__barrel_optimize__?names=BsMoonStars!=!./node_modules/react-icons/bs/index.mjs\");\n// \"use client\";\n// import { Moon, Sun } from \"lucide-react\";\n// import { useTheme } from \"next-themes\";\n// import { useEffect, useState } from \"react\";\n// import { BsMoonStars } from \"react-icons/bs\";\n// import { IoSunnyOutline } from \"react-icons/io5\";\n// const ThemeSwitcher = ({ isCollapsed }) => {\n//   const { theme, setTheme } = useTheme();\n//   const [mounted, setMounted] = useState(false);\n//   useEffect(() => {\n//     setMounted(true);\n//   }, []);\n//   if (!mounted) return null;\n//   return (\n//     <button\n//       className={`flex items-center justify-center text-sm ${\n//         isCollapsed ? \"\" : \"gap-x-2\"\n//       } `}\n//       onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n//     >\n//       {theme === \"light\" ? <IoSunnyOutline  size={18} /> : <BsMoonStars  size={18} />}\n//       {isCollapsed ? \"\" : \" Theme\"}\n//     </button>\n//   );\n// };\n// export default ThemeSwitcher;\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ThemeSwitcher() {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    // eslint-disable-next-line no-unused-vars\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeSwitcher.useEffect\": ()=>{\n            // Get theme from localStorage or default to light\n            const storedTheme = localStorage.getItem(\"theme\") || \"light\";\n            setTheme(storedTheme);\n            document.documentElement.classList.toggle(\"dark\", storedTheme === \"dark\");\n        }\n    }[\"ThemeSwitcher.useEffect\"], []);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        document.documentElement.classList.toggle(\"dark\", newTheme === \"dark\");\n        localStorage.setItem(\"theme\", newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${activeItem === \"Theme\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: toggleTheme,\n            className: `flex items-center justify-center text-sm font-poppins gap-x-2  cursor-pointer  ${activeItem === \"Theme\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n            children: [\n                theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoSunnyOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoSunnyOutline, {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 28\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsMoonStars_react_icons_bs__WEBPACK_IMPORTED_MODULE_3__.BsMoonStars, {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 58\n                }, this),\n                \"Theme\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/superadmin/ThemeSwitcher.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=BiLogOutCircle!=!./node_modules/react-icons/bi/index.mjs":
/*!******************************************************************************************!*\
  !*** __barrel_optimize__?names=BiLogOutCircle!=!./node_modules/react-icons/bi/index.mjs ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/bi/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/bi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Box!=!./node_modules/@mui/material/node/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=Box!=!./node_modules/@mui/material/node/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "(pages-dir-node)/./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=BsMoonStars!=!./node_modules/react-icons/bs/index.mjs":
/*!***************************************************************************************!*\
  !*** __barrel_optimize__?names=BsMoonStars!=!./node_modules/react-icons/bs/index.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bs_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/bs/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/bs/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bs_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bs_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!./node_modules/react-icons/fa/index.mjs":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!./node_modules/react-icons/fa/index.mjs ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaKey,FaUser!=!./node_modules/react-icons/fa6/index.mjs":
/*!*****************************************************************************************!*\
  !*** __barrel_optimize__?names=FaKey,FaUser!=!./node_modules/react-icons/fa6/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!./node_modules/react-icons/fa6/index.mjs":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!./node_modules/react-icons/fa6/index.mjs ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiSettings!=!./node_modules/react-icons/fi/index.mjs":
/*!**************************************************************************************!*\
  !*** __barrel_optimize__?names=FiSettings!=!./node_modules/react-icons/fi/index.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=GiKeyCard,GiNotebook!=!./node_modules/react-icons/gi/index.mjs":
/*!************************************************************************************************!*\
  !*** __barrel_optimize__?names=GiKeyCard,GiNotebook!=!./node_modules/react-icons/gi/index.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/gi/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/gi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=IoSunnyOutline!=!./node_modules/react-icons/io5/index.mjs":
/*!*******************************************************************************************!*\
  !*** __barrel_optimize__?names=IoSunnyOutline!=!./node_modules/react-icons/io5/index.mjs ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/io5/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/io5/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!./node_modules/react-icons/lu/index.mjs":
/*!********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!./node_modules/react-icons/lu/index.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_lu_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/lu/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/lu/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_lu_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_lu_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=MdManageAccounts!=!./node_modules/react-icons/md/index.mjs":
/*!********************************************************************************************!*\
  !*** __barrel_optimize__?names=MdManageAccounts!=!./node_modules/react-icons/md/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=MdOutlineRateReview!=!./node_modules/react-icons/md/index.mjs":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=MdOutlineRateReview!=!./node_modules/react-icons/md/index.mjs ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=PiSquaresFourBold!=!./node_modules/react-icons/pi/index.mjs":
/*!*********************************************************************************************!*\
  !*** __barrel_optimize__?names=PiSquaresFourBold!=!./node_modules/react-icons/pi/index.mjs ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_pi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/pi/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/pi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_pi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_pi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=RiArrowDropDownLine!=!./node_modules/react-icons/ri/index.mjs":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=RiArrowDropDownLine!=!./node_modules/react-icons/ri/index.mjs ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_ri_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/ri/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/ri/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_ri_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_ri_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=RxCross2!=!./node_modules/react-icons/rx/index.mjs":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=RxCross2!=!./node_modules/react-icons/rx/index.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_rx_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/rx/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/rx/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_rx_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_rx_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=TbBrandWechat!=!./node_modules/react-icons/tb/index.mjs":
/*!*****************************************************************************************!*\
  !*** __barrel_optimize__?names=TbBrandWechat!=!./node_modules/react-icons/tb/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_tb_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/tb/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/tb/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_tb_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_tb_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;