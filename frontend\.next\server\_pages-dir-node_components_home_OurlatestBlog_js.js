"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_home_OurlatestBlog_js";
exports.ids = ["_pages-dir-node_components_home_OurlatestBlog_js"];
exports.modules = {

/***/ "(pages-dir-node)/./components/home/<USER>":
/*!******************************************!*\
  !*** ./components/home/<USER>
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/browserSetting */ \"(pages-dir-node)/./utils/browserSetting.jsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _navbarContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./navbarContext */ \"(pages-dir-node)/./components/home/<USER>");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _barrel_optimize_names_FaHeart_react_icons_fa6__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FaHeart!=!react-icons/fa6 */ \"(pages-dir-node)/__barrel_optimize__?names=FaHeart!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! swiper/css/navigation */ \"(pages-dir-node)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_11__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, swiper_react__WEBPACK_IMPORTED_MODULE_9__, swiper_modules__WEBPACK_IMPORTED_MODULE_10__]);\n([_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_7__, swiper_react__WEBPACK_IMPORTED_MODULE_9__, swiper_modules__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unescaped-entities */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { motion } from \"framer-motion\";\nconst Loader = next_dynamic__WEBPACK_IMPORTED_MODULE_8___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/components/loader/loader */ \"(pages-dir-node)/./components/loader/loader.jsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\home\\\\OurlatestBlog.js -> \" + \"@/components/loader/loader\"\n        ]\n    },\n    ssr: false\n});\nconst OurlatestBlog = ()=>{\n    const [blogData, setBlogData] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    // eslint-disable-next-line no-unused-vars\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(1);\n    // eslint-disable-next-line no-unused-vars\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(1);\n    const limit = 4;\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const { token } = (0,_navbarContext__WEBPACK_IMPORTED_MODULE_6__.useNavbar)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [imageFailedMap, setImageFailedMap] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"OurlatestBlog.useEffect\": ()=>{\n            const fetchBlogData = {\n                \"OurlatestBlog.useEffect.fetchBlogData\": async ()=>{\n                    try {\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__.getBlogApi)(currentPage, limit);\n                        setBlogData(response?.data?.data?.blogs || []);\n                        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);\n                        setIsLoading(false);\n                    } catch (error) {\n                        console.error(\"Error fetching stay data:\", error);\n                    }\n                }\n            }[\"OurlatestBlog.useEffect.fetchBlogData\"];\n            if (!isFirstRender.current) {\n                fetchBlogData();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"OurlatestBlog.useEffect\"], [\n        currentPage,\n        token\n    ]);\n    const HandleLike = async (liked, id)=>{\n        if ((0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_2__.getItemLocalStorage)(\"token\")) {\n            setLoading(true);\n            try {\n                const payload = {\n                    isLike: !liked\n                };\n                const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_1__.likeUnlikePropertyApi)(id, payload);\n                console.log(\"Like/Unlike response:\", response);\n                // Update the UI directly with the response data\n                if (response?.data?.data || response?.data) {\n                    setBlogData((prevData)=>{\n                        return prevData.map((blog)=>{\n                            if (blog._id === id) {\n                                return {\n                                    ...blog,\n                                    ...response?.data?.data || response?.data,\n                                    liked: response?.data?.data?.liked || response?.data?.liked // Use the opposite of current liked state\n                                };\n                            }\n                            return blog;\n                        });\n                    });\n                }\n            } catch (error) {\n                console.error(\"Error updating like status:\", error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to update like status\");\n            } finally{\n                setLoading(false);\n            }\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Please login first!!\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Loader, {\n                open: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"w-full relative md:pb-16 pb-10 lg:px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-20 -left-8 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-4 w-40 h-40 bg-pink-300 rounded-full blur-2xl opacity-30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between w-full my-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \" text-black  font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl sm:mb-8 mb-3\",\n                                        children: [\n                                            \"The\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary-blue\",\n                                                children: \" Hostel \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \" Insider\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center sm:hidden block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750\",\n                                            prefetch: false,\n                                            children: \"See All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `gap-2 ${blogData?.length > 4 ? 'xl:flex' : 'xl:hidden'} ${blogData?.length > 3.5 ? 'lg:flex' : 'lg:hidden'} ${blogData?.length > 2.5 ? 'md:flex' : 'md:hidden'} ${blogData?.length > 2 ? 'sm:flex' : 'sm:hidden'} ${blogData?.length > 1.5 ? 'hidden' : 'hidden'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"slider-button-prev cursor-pointer custom-nav\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_12__.ArrowLeft, {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 77\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"slider-button-next cursor-pointer custom-nav\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_12__.ArrowRight, {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 77\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"hidden sm:block mt-2 text-base font-medium text-[#737373] font-manrope mb-3 w-[80%]\",\n                                children: [\n                                    \"Welcome to The \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/blog\",\n                                        className: \"text-primary-blue\",\n                                        children: \" Hostel Insider guide \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 27\n                                    }, undefined),\n                                    \" to the world of hostels, budget travel, and unforgettable backpacker adventures. From expert tips on finding the \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/\",\n                                        className: \"text-primary-blue\",\n                                        children: \" best hostel bookings \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 221\n                                    }, undefined),\n                                    \" to discovering hidden gems, local experiences, and solo travel hacks, we've got everything you need to travel smart, stay social, and save big. Whether you're a seasoned solo backpacker, planning your first dormitory stay, or just love affordable travel, this is where your journey begins.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_9__.Swiper, {\n                                    modules: [\n                                        swiper_modules__WEBPACK_IMPORTED_MODULE_10__.Autoplay,\n                                        swiper_modules__WEBPACK_IMPORTED_MODULE_10__.Navigation\n                                    ],\n                                    // autoplay={{ delay: 1500, pauseOnMouseEnter: true }}\n                                    slidesPerView: 18,\n                                    navigation: {\n                                        prevEl: '.slider-button-prev',\n                                        nextEl: '.slider-button-next'\n                                    },\n                                    loop: true,\n                                    speed: 1000,\n                                    spaceBetween: 14,\n                                    className: \"mySwiper myCustomSwiper py-4 discover-event-slider-home overflow-hidden\",\n                                    breakpoints: {\n                                        0: {\n                                            slidesPerView: 1.4\n                                        },\n                                        480: {\n                                            slidesPerView: 1.4\n                                        },\n                                        640: {\n                                            slidesPerView: 2\n                                        },\n                                        768: {\n                                            slidesPerView: 2.2\n                                        },\n                                        1024: {\n                                            slidesPerView: 3.5\n                                        },\n                                        1280: {\n                                            slidesPerView: 4\n                                        }\n                                    },\n                                    children: isLoading ? Array.from({\n                                        length: 4\n                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_9__.SwiperSlide, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-4 border-slate-100 overflow-hidden font-manrope w-full bg-white my-8 md:my-2 shadow-md animate-pulse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full rounded-t-[32px] bg-gray-200 h-[150px] xs:h-[212px] flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-white font-bold font-manrope\",\n                                                            children: \"MixDorm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3 bg-gray-300 rounded-full w-16 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 right-3 bg-gray-300 rounded-full w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"py-4 px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-x-3 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-300 rounded-full w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                                lineNumber: 192,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-300 rounded-md w-20 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                                lineNumber: 193,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-300 rounded-full w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                                lineNumber: 196,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-300 rounded-md w-12 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                                lineNumber: 197,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-300 h-4 rounded-md mb-2 w-[90%]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-300 h-4 rounded-md w-[70%] mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-300 rounded-full w-8 h-8\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                                lineNumber: 206,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-300 w-20 h-4 rounded-md\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-300 w-24 h-8 rounded-2xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                lineNumber: 181,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, `skeleton-${index}`, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, undefined)) : blogData?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_9__.SwiperSlide, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `border-4 border-slate-105 overflow-hidden font-manrope w-full  bg-white my-8 md:my-2 shadow-md  lg:hover:shadow-xl lg:hover:scale-105 lg:hover:-translate-y-1 lg:transition-all lg:duration-300`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full rounded-t-[32px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                // data-title={item?.title}\n                                                                href: `/blog-details?id=${item?._id}`,\n                                                                className: \" before:!top-[81px] before:!left-[140px] sm:before:!left-[150px] lg:before:!left-[125px]  xl:before:!left-[150px] 2xl:before:!left-[150px]\",\n                                                                prefetch: false,\n                                                                children: imageFailedMap[item._id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full xs:h-[212px] h-[150px] bg-slate-200 animate-pulse flex justify-center items-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                        className: \"text-white font-bold font-manrope\",\n                                                                        children: \"MixDorm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    src: item?.images?.[0],\n                                                                    alt: item?.title,\n                                                                    width: 418,\n                                                                    height: 259,\n                                                                    quality: 90,\n                                                                    sizes: \"(max-width: 480px) 100vw, (max-width: 768px) 100vw, 418px\",\n                                                                    className: \"w-full xs:h-[212px] h-[150px] object-cover bg-slate-200\",\n                                                                    onError: ()=>setImageFailedMap((prev)=>({\n                                                                                ...prev,\n                                                                                [item._id]: true\n                                                                            }))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute flex items-center justify-center px-3 py-1 text-xs font-medium text-black bg-white rounded-4xl xs:top-5 xs:left-5 top-3 left-3 font-manrope \",\n                                                                children: item?.categoryId?.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `absolute flex items-center justify-center p-1 rounded-full xs:w-7 xs:h-7 w-6 h-6 xs:top-5 xs:right-5 top-3 right-3 font-manrope ${item?.liked ? \"bg-white text-red-600\" : \"text-black bg-white\"} hover:bg-white hover:text-red-600`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_react_icons_fa6__WEBPACK_IMPORTED_MODULE_13__.FaHeart, {\n                                                                    size: 18,\n                                                                    onClick: ()=>HandleLike(item?.liked, item?._id)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: ` py-4 px-2`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \" before:!top-[80px] before:!left-[140px] sm:before:!left-[160px] md:before:!left-[150px] lg:before:!left-[125px] xl:before:!left-[145px] 2xl:before:!left-[150px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                    href: `/blog-details?id=${item?._id}`,\n                                                                    className: \"my-[11px] xs:text-base text-sm font-bold text-black line-clamp-2 hover:text-primary-blue font-manrope \",\n                                                                    prefetch: false,\n                                                                    children: item?.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center mt-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                    href: `/blog-details?id=${item?._id}`,\n                                                                    prefetch: false,\n                                                                    className: \"\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        className: \"md:inline-block   text-xxs 2xl:text-xs font-bold text-white bg-black font-manrope px-2 xs:px-3 2xl:px-5 xs:py-3 py-2 flex justify-center items-center rounded-9xl hover:bg-primary-blue hover:text-black\",\n                                                                        children: \"Keep Reading\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, item?._id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, item.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mt-10 hidden sm:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/blog\",\n                                    className: \"text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750\",\n                                    prefetch: false,\n                                    children: \"See All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\OurlatestBlog.js\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurlatestBlog);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/home/<USER>");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaHeart!=!./node_modules/react-icons/fa6/index.mjs":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=FaHeart!=!./node_modules/react-icons/fa6/index.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;