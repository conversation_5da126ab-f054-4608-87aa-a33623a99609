"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_popup_loginPopup_jsx-_022b1";
exports.ids = ["_pages-dir-node_components_popup_loginPopup_jsx-_022b1"];
exports.modules = {

/***/ "(pages-dir-node)/./components/popup/loginPopup.jsx":
/*!*****************************************!*\
  !*** ./components/popup/loginPopup.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\n/* harmony import */ var _barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Button!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=FaApple,FaFacebook!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-oauth/google */ \"@react-oauth/google\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-apple-login */ \"react-apple-login\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_apple_login__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-facebook */ \"react-facebook\");\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_facebook__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _signinPopup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./signinPopup */ \"(pages-dir-node)/./components/popup/signinPopup.jsx\");\n/* harmony import */ var _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../socialLogin/googleSocialLogin */ \"(pages-dir-node)/./components/socialLogin/googleSocialLogin.jsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/api */ \"(pages-dir-node)/./utils/api.js\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/browserSetting */ \"(pages-dir-node)/./utils/browserSetting.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/firebaseConfig */ \"(pages-dir-node)/./utils/firebaseConfig.jsx\");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/ownerflowServices */ \"(pages-dir-node)/./services/ownerflowServices.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../home/<USER>/ \"(pages-dir-node)/./components/home/<USER>");\n/* harmony import */ var _mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/icons-material/VisibilityOutlined */ \"(pages-dir-node)/./node_modules/@mui/icons-material/VisibilityOutlined.js\");\n/* harmony import */ var _mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/VisibilityOffOutlined */ \"(pages-dir-node)/./node_modules/@mui/icons-material/VisibilityOffOutlined.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_5__, _signinPopup__WEBPACK_IMPORTED_MODULE_8__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_5__, _signinPopup__WEBPACK_IMPORTED_MODULE_8__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst LoginPopup = ({ isOpen, onClose })=>{\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPopup.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginPopup.useEffect\"], []);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fullname: \"\",\n        phoneNumber: \"\",\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSignInOpen, setIsSignInOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(isOpen);\n    const [isSignUpOpen, setIsSignUpOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_15__.useNavbar)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSignup = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password || !formData.fullname || !formData.phoneNumber) {\n            setError(\"All fields are required.\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.registerApi)({\n                name: formData?.fullname,\n                email: formData?.email,\n                password: formData?.password,\n                contact: formData?.phoneNumber\n            });\n            if (response?.data?.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(response?.data?.message || \"Registration successful!\");\n                setFormData({\n                    fullname: \"\",\n                    phoneNumber: \"\",\n                    email: \"\",\n                    password: \"\"\n                });\n                router.push(\"/verifyotp\");\n                onClose(); // Close LoginPopup\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(response?.data?.message || \"Something went wrong, please try again.\");\n            }\n        } catch (error) {\n            setError(error.response?.data?.message || \"An error occurred, please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isDisabled = loading || !formData.email || !formData.password || !formData.fullname || !formData.phoneNumber;\n    const handleFacebookSuccess = async (response)=>{\n        try {\n            const { accessToken } = response.authResponse;\n            // Manually fetch user profile from Facebook using the access token\n            const profileRes = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(`https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`);\n            const { name, email } = profileRes.data;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_10__.BASE_URL}/auth/social/login`, {\n                token: accessToken,\n                role: \"user\",\n                email,\n                name\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"name\", name);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"email\", res?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"contact\", res?.data?.data?.user?.contact);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                onClose();\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleFacebookFailure = (error)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Facebook login failed: \" + error.message);\n    };\n    const handleAppleSuccess = async (response)=>{\n        try {\n            const { authorization, user } = response;\n            const { id_token } = authorization;\n            const name = user?.name ? `${user.name.firstName} ${user.name.lastName}` : null;\n            const email = user?.email || null;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_10__.BASE_URL}/auth/social/login`, {\n                token: id_token,\n                role: \"user\",\n                provider: \"apple\",\n                name: name,\n                email: email\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"name\", name || `${res.data.data.user.name.first} ${res.data.data.user.name.last}`);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"email\", res?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"contact\", res?.data?.data?.user?.contact);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n                router.push(\"/\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleAppleFailure = (response)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Apple login failed: \" + response.error);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPopup.useEffect\": ()=>{\n            setIsSignInOpen(isOpen);\n        }\n    }[\"LoginPopup.useEffect\"], [\n        isOpen\n    ]);\n    const openSignInForm = ()=>{\n        setIsSignInOpen(true);\n        setIsSignUpOpen(false);\n    };\n    const openSignUpForm = ()=>{\n        setIsSignInOpen(false);\n        setIsSignUpOpen(true);\n    };\n    const closePopup = ()=>{\n        setIsSignInOpen(false);\n        setIsSignUpOpen(false);\n        onClose(); // Ensure the parent is informed about closing\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            isSignInOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signinPopup__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isSignInOpen,\n                onClose: closePopup,\n                openSignUpForm: openSignUpForm\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined) : isSignUpOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl w-[95%] max-w-2xl p-5 font-manrope\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: closePopup,\n                                className: \"text-black hover:text-gray-600 transition duration-150\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 244,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col justify-center items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#40E0D0] flex text-2xl font-extrabold mb-2\",\n                                children: [\n                                    \"Mix\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-black text-2xl font-extrabold \",\n                                        children: \"Dorm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                lineNumber: 253,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 252,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSignup,\n                            className: \"mx-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 mb-4 text-center\",\n                                    children: \"\\uD83D\\uDC4B Sign Up your Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"fullname\",\n                                        name: \"fullname\",\n                                        value: formData.fullname,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Full Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"phoneNumber\",\n                                        name: \"phoneNumber\",\n                                        value: formData.phoneNumber,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Phone Number\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        name: \"email\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                            placeholder: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute right-4 top-[15px] text-gray-300 transform\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: `w-full font-semibold text-white py-4 rounded-full transition duration-200 ${isDisabled ? \"bg-gray-400 cursor-not-allowed\" : \"bg-[#40E0D0] hover:bg-[#38c7b7]\"}`,\n                                    disabled: isDisabled,\n                                    children: loading ? \"Signing up...\" : \"Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, undefined),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center text-red-600\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mx-8 space-x-4 my-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 border-t border-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Or\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 border-t border-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 358,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-x-4 justify-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_7__.FacebookProvider, {\n                                    appId: \"3141101952691007\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_7__.LoginButton, {\n                                        scope: \"public_profile,email\",\n                                        onSuccess: handleFacebookSuccess,\n                                        onError: handleFacebookFailure,\n                                        className: \"min-w-0 p-0 text-3xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_18__.FaFacebook, {\n                                            className: \"text-[#0866ff]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_oauth_google__WEBPACK_IMPORTED_MODULE_4__.GoogleOAuthProvider, {\n                                    clientId: \"179422288655-1cm80o7s30fk0p1esfh3an4s15duj2u9.apps.googleusercontent.com\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        role: \"user\",\n                                        closeLoginPopup: ()=>closePopup()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_apple_login__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                    clientId: \"com.mixdorm.app\",\n                                    redirectURI: \"https://mixdorm.com\",\n                                    responseType: \"code id_token\",\n                                    scope: \"name email\",\n                                    render: (renderProps)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                            className: \"min-w-0 p-0 text-3xl text-black mb-1\",\n                                            onClick: renderProps.onClick,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_18__.FaApple, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    onSuccess: handleAppleSuccess,\n                                    onError: handleAppleFailure\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 363,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-basec text-center text-sm text-gray-400\",\n                            children: [\n                                \"Already have an account?\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"text-primary-blue font-medium ml-1 cursor-pointer \",\n                                    onClick: openSignInForm,\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 403,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                    lineNumber: 243,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                lineNumber: 242,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginPopup);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/popup/loginPopup.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/popup/signinPopup.jsx":
/*!******************************************!*\
  !*** ./components/popup/signinPopup.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=FaApple,FaFacebook!=!react-icons/fa */ \"(pages-dir-node)/__barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/browserSetting */ \"(pages-dir-node)/./utils/browserSetting.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/firebaseConfig */ \"(pages-dir-node)/./utils/firebaseConfig.jsx\");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/ownerflowServices */ \"(pages-dir-node)/./services/ownerflowServices.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../home/<USER>/ \"(pages-dir-node)/./components/home/<USER>");\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-facebook */ \"react-facebook\");\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_facebook__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../socialLogin/googleSocialLogin */ \"(pages-dir-node)/./components/socialLogin/googleSocialLogin.jsx\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-apple-login */ \"react-apple-login\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react_apple_login__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @react-oauth/google */ \"@react-oauth/google\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/icons-material/VisibilityOutlined */ \"(pages-dir-node)/./node_modules/@mui/icons-material/VisibilityOutlined.js\");\n/* harmony import */ var _mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/VisibilityOffOutlined */ \"(pages-dir-node)/./node_modules/@mui/icons-material/VisibilityOffOutlined.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/api */ \"(pages-dir-node)/./utils/api.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__, axios__WEBPACK_IMPORTED_MODULE_14__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__, axios__WEBPACK_IMPORTED_MODULE_14__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unescaped-entities */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SignInPopup = ({ isOpen, onClose, openSignUpForm, closeLoginPopup })=>{\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value.trim()\n        });\n    };\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPopup.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SignInPopup.useEffect\"], []);\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_8__.useNavbar)();\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password) {\n            setError(\"All fields are required.\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.logInApi)({\n                email: formData.email,\n                password: formData.password,\n                role: \"user\"\n            });\n            console.log(\"response\", response);\n            if (response?.status === 403) {\n                try {\n                    const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.resendOtpApi)({\n                        email: formData?.email\n                    });\n                    console.log(\"response22\", response);\n                    if (response?.data?.status) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(`We’ve sent a verification code to ${formData?.email}. Please enter this code to continue.`);\n                        router.push(\"/verifyotp\");\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(response.data.message || \"Failed to resend verification code.\");\n                    }\n                    onClose();\n                } catch (error) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data?.message || \"An error occurred while resending OTP.\");\n                    console.error(\"Resend OTP error:\", error);\n                }\n            } else if (response?.data?.status) {\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", response?.data?.data?.user?.name.first);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", response?.data?.data?.user?._id);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", response?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"email\", response?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"contact\", response?.data?.data?.user?.contact);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(response?.data?.data?.token);\n                updateUserStatus(response?.data?.data?.token);\n                updateUserRole(response?.data?.data?.user?.role);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(response?.data?.message);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: response?.data?.data?.user?._id\n                    });\n                }\n                onClose(); // Close SignInPopup\n                closeLoginPopup(); // Close LoginPopup\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(response.data.message || \"An error occurred.\");\n            }\n        } catch (error) {\n            setError(error.response?.data?.message || \"An error occurred, please try again.\");\n            console.error(\"Login error:\", error.response);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isDisabled = loading || !formData.email || !formData.password;\n    const handleFacebookSuccess = async (response)=>{\n        try {\n            const { accessToken } = response.authResponse;\n            // Manually fetch user profile from Facebook using the access token\n            const profileRes = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].get(`https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`);\n            const { name, email } = profileRes.data;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_15__.BASE_URL}/auth/social/login`, {\n                token: accessToken,\n                role: \"user\",\n                email,\n                name\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", name);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                onClose();\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleFacebookFailure = (error)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Facebook login failed: \" + error.message);\n    };\n    const handleAppleSuccess = async (response)=>{\n        try {\n            const { authorization, user } = response;\n            const { id_token } = authorization;\n            const name = user?.name ? `${user.name.firstName} ${user.name.lastName}` : null;\n            const email = user?.email || null;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_15__.BASE_URL}/auth/social/login`, {\n                token: id_token,\n                role: \"user\",\n                provider: \"apple\",\n                name: name,\n                email: email\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", name || `${res.data.data.user.name.first} ${res.data.data.user.name.last}`);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n                router.push(\"/\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleAppleFailure = (response)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Apple login failed: \" + response.error);\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl w-[90%] max-w-md p-5 font-manrope\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-black hover:text-gray-600 transition duration-150\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col justify-center items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[#40E0D0] flex text-2xl font-extrabold sm:mb-2\",\n                            children: [\n                                \"Mix\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-black text-2xl font-extrabold \",\n                                    children: \"Dorm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 16\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSignIn,\n                        className: \"mx-1 sm:mx-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-[16px] sm:text-xl font-bold text-gray-800 sm:mb-6 mb-4 text-center\",\n                                children: \"\\uD83D\\uDC4B Login to your Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:mb-5 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email\",\n                                    name: \"email\",\n                                    value: formData.email,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative sm:mb-5 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showPassword ? \"text\" : \"password\",\n                                        id: \"password\",\n                                        name: \"password\",\n                                        value: formData.password,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"absolute right-4 top-[15px] text-gray-300 transform\",\n                                        onClick: ()=>setShowPassword(!showPassword),\n                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center sm:mb-6 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__.FormControlLabel, {\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__.Checkbox, {\n                                            className: \"text-[12px] sm:text-sm mt-[4px] block sm:mt-0\",\n                                            sx: {\n                                                color: \"rgba(0,0,0,0.4)\",\n                                                \"&.Mui-checked\": {\n                                                    color: \"#40E0D0\"\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \" text-[12px] sm:text-sm\",\n                                            children: \"Remember me\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/forgot-password\",\n                                        className: \"text-[#40E0D0] font-medium ml-1 cursor-pointer\",\n                                        prefetch: false,\n                                        onClick: onClose,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[12px] sm:text-sm\",\n                                            children: \" Forgot Passsword?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: `w-full sm:my-3 my-1 sm:text-base text-sm font-semibold text-white sm:py-4 py-3 rounded-4xl transition duration-200 ${isDisabled ? \"bg-gray-400 cursor-not-allowed\" : \"bg-[#40E0D0] hover:bg-teal-500\"}`,\n                                disabled: isDisabled,\n                                children: loading ? \"Signing in...\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 text-center text-red-600\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mx-8 space-x-4 sm:my-8 my-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 border-t border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 sm:text-base text-sm\",\n                                children: \"Or\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 border-t border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center sm:gap-x-4 gap-x-2 justify-center sm:mb-6 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_9__.FacebookProvider, {\n                                appId: \"3141101952691007\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_9__.LoginButton, {\n                                    scope: \"public_profile,email\",\n                                    onSuccess: handleFacebookSuccess,\n                                    onError: handleFacebookFailure,\n                                    className: \"min-w-0 p-0 sm:text-3xl text-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaFacebook, {\n                                        className: \"text-[#0866ff]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_oauth_google__WEBPACK_IMPORTED_MODULE_12__.GoogleOAuthProvider, {\n                                clientId: \"179422288655-1cm80o7s30fk0p1esfh3an4s15duj2u9.apps.googleusercontent.com\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    role: \"user\",\n                                    closeLoginPopup: onClose\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_apple_login__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                clientId: \"com.mixdorm.app\",\n                                redirectURI: \"https://mixdorm.com\",\n                                responseType: \"code id_token\",\n                                scope: \"name email\",\n                                render: (renderProps)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        className: \"min-w-0 p-0 text-3xl text-black mb-1\",\n                                        onClick: renderProps.onClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaApple, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, void 0),\n                                onSuccess: handleAppleSuccess,\n                                onError: handleAppleFailure\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-basec text-center text-sm text-gray-400\",\n                        children: [\n                            \"Don't have an account?\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                className: \"text-[#40E0D0] font-medium ml-1 cursor-pointer\",\n                                onClick: openSignUpForm,\n                                children: \"Sign Up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SignInPopup);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/popup/signinPopup.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/socialLogin/googleSocialLogin.jsx":
/*!******************************************************!*\
  !*** ./components/socialLogin/googleSocialLogin.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-oauth/google */ \"@react-oauth/google\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/browserSetting */ \"(pages-dir-node)/./utils/browserSetting.jsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"(pages-dir-node)/./utils/api.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_FcGoogle_react_icons_fc__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FcGoogle!=!react-icons/fc */ \"(pages-dir-node)/__barrel_optimize__?names=FcGoogle!=!./node_modules/react-icons/fc/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Button!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/firebaseConfig */ \"(pages-dir-node)/./utils/firebaseConfig.jsx\");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/ownerflowServices */ \"(pages-dir-node)/./services/ownerflowServices.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"(pages-dir-node)/./components/home/<USER>");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_6__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__]);\n([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_6__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst GoogleSocialLogin = ({ role, closeLoginPopup })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_10__.useNavbar)();\n    const fetchGoogleUserProfile = async (access_token)=>{\n        try {\n            const res = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://www.googleapis.com/oauth2/v3/userinfo', {\n                headers: {\n                    Authorization: `Bearer ${access_token}`\n                }\n            });\n            return res.data;\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to fetch user profile: \" + error.message);\n            throw error;\n        }\n    };\n    const handleGoogleSuccess = async (tokenResponse)=>{\n        const { access_token } = tokenResponse;\n        try {\n            const userProfile = await fetchGoogleUserProfile(access_token);\n            const { name, email } = userProfile;\n            const [firstName, lastName] = name.split(' ');\n            const res = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_5__.BASE_URL}/auth/social/login`, {\n                token: access_token,\n                role: role,\n                name: {\n                    first: firstName,\n                    last: lastName || ''\n                },\n                email: email\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", `${res?.data?.data?.user?.name?.first} ${res?.data?.data?.user?.name?.last}`);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"email\", res?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"contact\", res?.data?.data?.user?.contact);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n                if (role === \"hostel_owner\") {\n                    router.push(\"/owner/list\");\n                } else {\n                    closeLoginPopup();\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.response?.data.message || error.message);\n        }\n    };\n    const login = (0,_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__.useGoogleLogin)({\n        onSuccess: handleGoogleSuccess,\n        onError: {\n            \"GoogleSocialLogin.useGoogleLogin[login]\": (error)=>react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Google login failed: \" + error)\n        }[\"GoogleSocialLogin.useGoogleLogin[login]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_11__.Button, {\n        className: \"min-w-0 p-0 text-3xl\",\n        onClick: ()=>login(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FcGoogle_react_icons_fc__WEBPACK_IMPORTED_MODULE_12__.FcGoogle, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\socialLogin\\\\googleSocialLogin.jsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\socialLogin\\\\googleSocialLogin.jsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GoogleSocialLogin);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/socialLogin/googleSocialLogin.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./utils/firebaseConfig.jsx":
/*!**********************************!*\
  !*** ./utils/firebaseConfig.jsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requestForFCMToken: () => (/* binding */ requestForFCMToken)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_messaging__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/messaging */ \"firebase/messaging\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_messaging__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_messaging__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// firebaseClient.js\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDL2ms8HU3bt4gn_bpTN6Z5v9umH94iZ_4\",\n    authDomain: \"mixdorm-c5c35.firebaseapp.com\",\n    projectId: \"mixdorm-c5c35\",\n    storageBucket: \"mixdorm-c5c35.appspot.com\",\n    messagingSenderId: \"789621005244\",\n    appId: \"1:789621005244:web:cf662eaedbfd773330a9b9\",\n    measurementId: \"G-Z6LTN1ZD1X\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst requestForFCMToken = async ()=>{\n    if (false) {} else {\n        console.log(\"Firebase messaging is not supported or this is not running in the browser.\");\n        return null;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./utils/firebaseConfig.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!./node_modules/@mui/material/node/index.js":
/*!***************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!./node_modules/@mui/material/node/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "(pages-dir-node)/./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs":
/*!**********************************************************************************************!*\
  !*** __barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FcGoogle!=!./node_modules/react-icons/fc/index.mjs":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=FcGoogle!=!./node_modules/react-icons/fc/index.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fc_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fc/index.mjs */ "(pages-dir-node)/./node_modules/react-icons/fc/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fc_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fc_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;