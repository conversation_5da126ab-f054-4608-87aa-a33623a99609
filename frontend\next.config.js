/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "mixdrom.com",
      },
      {
        protocol: "https",
        hostname: "a.hwstatic.com",
      },
      {
        protocol: "https",
        hostname: "de2qijr9wfk4u.cloudfront.net",
      },
      {
        protocol: "https",
        hostname: "flagcdn.com",
      },
      {
        protocol: "https",
        hostname: "mixdorm-live.s3.ap-south-1.amazonaws.com",
      },
      {
        protocol: "https",
        hostname: "i.pinimg.com",
      },
      {
        protocol: "https",
        hostname: "learnanything.co.in",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
      },
      {
        protocol: "https",
        hostname: "img-s-msn-com.akamaized.net",
      },
      {
        protocol: "https",
        hostname: "undefined",
      },
      {
        protocol: "https",
        hostname: "maps.googleapis.com",
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: "/api/countries", // Local API path
        destination: "https://restcountries.com/v3.1/all", // External endpoint
      },
    ];
  },
  
};

module.exports = nextConfig;
