"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_superadmin_SuperLayout_jsx";
exports.ids = ["components_superadmin_SuperLayout_jsx"];
exports.modules = {

/***/ "./components/superadmin/SuperLayout.jsx":
/*!***********************************************!*\
  !*** ./components/superadmin/SuperLayout.jsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!lucide-react */ \"__barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_MdManageAccounts_react_icons_md__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MdManageAccounts!=!react-icons/md */ \"__barrel_optimize__?names=MdManageAccounts!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaKey_FaUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaKey,FaUser!=!react-icons/fa6 */ \"__barrel_optimize__?names=FaKey,FaUser!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _SuperSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SuperSidebar */ \"./components/superadmin/SuperSidebar.jsx\");\n/* harmony import */ var _fontsource_nunito_sans__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @fontsource/nunito-sans */ \"./node_modules/@fontsource/nunito-sans/index.css\");\n/* harmony import */ var _fontsource_nunito_sans__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_fontsource_nunito_sans__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_SuperSidebar__WEBPACK_IMPORTED_MODULE_3__]);\n_SuperSidebar__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst SuperLayout = ()=>{\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"English\"); // Default selected language\n    const [isProfileDropdownOpen, setIsProfileDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false); // Profile dropdown state\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false); // State for sidebar visibility on mobile\n    const toggleDropdown = ()=>{\n        setIsDropdownOpen(!isDropdownOpen);\n    };\n    const toggleProfileDropdown = ()=>{\n        setIsProfileDropdownOpen(!isProfileDropdownOpen);\n    };\n    const selectLanguage = (language)=>{\n        setSelectedLanguage(language);\n        setIsDropdownOpen(false); // Close dropdown after selection\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SuperLayout.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                    setIsProfileDropdownOpen(false);\n                }\n            }\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"SuperLayout.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"SuperLayout.useEffect\"];\n        }\n    }[\"SuperLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuperSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                setIsSidebarOpen: setIsSidebarOpen,\n                isSidebarOpen: isSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center shadow justify-between w-full h-auto px-2 md:px-5 lg:px-5 py-3 bg-white dark:bg-[#0D0D0D] text-black-100 lg:pl-[255px] md:pl-[255px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Menu, {\n                                    className: \"duration-200 ease-in-out cursor-pointer text-black-100 hover:text-black dark:text-[#B6B6B6]\",\n                                    size: 20,\n                                    onClick: ()=>setIsSidebarOpen(!isSidebarOpen)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full sm:w-60 lg:w-80 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"search\",\n                                        placeholder: \"Search for rooms and offers\",\n                                        className: \"pl-8 bg-gray-100 text-gray-200 rounded-full py-2 w-full outline-none border-2 border-gray-200 text-xs font-poppins dark:bg-[#0D0D0D] dark:border dark:text-[#616161]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Search, {\n                                        className: \"absolute  text-gray-500 dark:text-[#616161] top-1/2 left-2.5 transform -translate-y-1/2\",\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-x-3 md:gap-x-4 lg:gap-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"cursor-pointer text-gray-540 dark:text-[#B6B6B6]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.BellRing, {\n                                    size: 20,\n                                    className: \"font-light\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center text-gray-540 dark:text-[#B6B6B6] gap-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:flex md:flex items-center hidden gap-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Globe, {\n                                                size: 20,\n                                                className: \"font-light\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"cursor-pointer text-sm flex items-center gap-1\",\n                                                onClick: toggleDropdown,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: selectedLanguage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronDown, {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:hidden md:hidden\",\n                                        onClick: toggleDropdown,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Globe, {\n                                            size: 20,\n                                            className: \"font-light\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fixed inset-0 bg-black/30 backdrop-blur-sm z-10\",\n                                        onClick: toggleDropdown\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full right-0 mt-2 w-52 bg-white dark:bg-[#171616] rounded-xl shadow-lg z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"px-4 py-3 font-nunito text-sm lg:text-base  font-normal\",\n                                                children: \"Select Language\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    \"English\",\n                                                    \"French\",\n                                                    \"Spanish\"\n                                                ].map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"px-4 py-4 hover:bg-gray-100 dark:hover:bg-[#474646d0] cursor-pointer flex items-center justify-between\",\n                                                        onClick: ()=>selectLanguage(language),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 text-sm\",\n                                                                children: [\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                        src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/${language}.png`,\n                                                                        width: 44,\n                                                                        height: 30,\n                                                                        alt: language\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                        lineNumber: 162,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    language\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            selectedLanguage === language && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                                                size: 20,\n                                                                className: \"text-black dark:text-[#B6B6B6]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, language, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center gap-x-2\",\n                                ref: dropdownRef,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex lg:hidden items-center justify-center text-neutral-850 dark:text-[#B6B6B6]\",\n                                        onClick: toggleProfileDropdown,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaKey_FaUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_6__.FaUser, {\n                                            className: \"text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:flex items-center gap-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center text-white rounded-full w-11 h-11 bg-yellow-550\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-base font-bold\",\n                                                    onClick: toggleProfileDropdown,\n                                                    children: \"M\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-bold text-neutral-700 dark:text-[#B6B6B6]\",\n                                                        onClick: toggleProfileDropdown,\n                                                        children: \"Moni Roy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs font-semibold text-neutral-850 dark:text-[#B6B6B6]\",\n                                                        children: \"Admin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden lg:flex items-center justify-center w-5 h-5 duration-200 ease-in-out border rounded-full border-neutral-850 text-neutral-850 dark:text-[#B6B6B6]\",\n                                        onClick: toggleProfileDropdown,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellRing_Check_ChevronDown_Globe_Menu_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronDown, {\n                                            size: 16,\n                                            className: `${isProfileDropdownOpen ? \"rotate-180 mb-0.5\" : \"\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isProfileDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full right-0 mt-2 w-56 bg-white dark:bg-[#171616] rounded-lg shadow-lg z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"lg:hidden items-center flex px-3 pt-2 gap-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center text-white rounded-full w-11 h-11 bg-yellow-550\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-base font-bold\",\n                                                                children: \"M\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-bold text-neutral-700 dark:text-[#B6B6B6]\",\n                                                                    children: \"Moni Roy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-semibold text-neutral-850 dark:text-[#B6B6B6]\",\n                                                                    children: \"Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdManageAccounts_react_icons_md__WEBPACK_IMPORTED_MODULE_7__.MdManageAccounts, {\n                                                            size: 22,\n                                                            className: \"text-sky-blue-650\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Manage Profiles\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaKey_FaUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_6__.FaKey, {\n                                                            size: 16,\n                                                            className: \"text-[#8080FF]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Change Password\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/LogOut.png`,\n                                                            width: 14,\n                                                            height: 12,\n                                                            alt: \"LogOut\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Log out\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperLayout.jsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SuperLayout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3N1cGVyYWRtaW4vU3VwZXJMYXlvdXQuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXNCO0FBQ1M7QUFDUztBQUNVO0FBQ0Y7QUFDTjtBQUNBO0FBQ1Q7QUFJakMsTUFBTWUsY0FBYztJQUVsQixNQUFNQyxjQUFjSCw2Q0FBTUEsQ0FBQztJQUMzQixNQUFNLENBQUNJLGdCQUFnQkMsa0JBQWtCLEdBQUdWLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ1csa0JBQWtCQyxvQkFBb0IsR0FBR1osK0NBQVFBLENBQUMsWUFBWSw0QkFBNEI7SUFDakcsTUFBTSxDQUFDYSx1QkFBdUJDLHlCQUF5QixHQUFHZCwrQ0FBUUEsQ0FBQyxRQUFRLHlCQUF5QjtJQUNwRyxNQUFNLENBQUNlLGVBQWVDLGlCQUFpQixHQUFHaEIsK0NBQVFBLENBQUMsUUFBUSx5Q0FBeUM7SUFFcEcsTUFBTWlCLGlCQUFpQjtRQUNyQlAsa0JBQWtCLENBQUNEO0lBQ3JCO0lBRUEsTUFBTVMsd0JBQXdCO1FBQzVCSix5QkFBeUIsQ0FBQ0Q7SUFDNUI7SUFFQSxNQUFNTSxpQkFBaUIsQ0FBQ0M7UUFDdEJSLG9CQUFvQlE7UUFDcEJWLGtCQUFrQixRQUFRLGlDQUFpQztJQUM3RDtJQUNBSixnREFBU0E7aUNBQUM7WUFDUixTQUFTZSxtQkFBbUJDLEtBQUs7Z0JBQy9CLElBQUlkLFlBQVllLE9BQU8sSUFBSSxDQUFDZixZQUFZZSxPQUFPLENBQUNDLFFBQVEsQ0FBQ0YsTUFBTUcsTUFBTSxHQUFHO29CQUN0RVgseUJBQXlCO2dCQUMzQjtZQUNGO1lBQ0FZLFNBQVNDLGdCQUFnQixDQUFDLGFBQWFOO1lBQ3ZDO3lDQUFPO29CQUNMSyxTQUFTRSxtQkFBbUIsQ0FBQyxhQUFhUDtnQkFDNUM7O1FBQ0Y7Z0NBQUcsRUFBRTtJQUVMLHFCQUNFOzswQkFPRSw4REFBQ2pCLHFEQUFZQTtnQkFDWFksa0JBQWtCQTtnQkFDbEJELGVBQWVBOzs7Ozs7MEJBUWpCLDhEQUFDYztnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNsQyxrSEFBSUE7b0NBQ0hrQyxXQUFVO29DQUNWQyxNQUFNO29DQUNOQyxTQUFTLElBQU1oQixpQkFBaUIsQ0FBQ0Q7Ozs7Ozs7Ozs7OzBDQUlyQyw4REFBQ2M7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FDQ0MsTUFBSzt3Q0FDTEMsYUFBWTt3Q0FDWkwsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDakMsb0hBQU1BO3dDQUNMaUMsV0FBVTt3Q0FDVkMsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1aLDhEQUFDRjt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDdEMsc0hBQVFBO29DQUFDdUMsTUFBTTtvQ0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7MENBSWhDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ25DLG1IQUFLQTtnREFBQ29DLE1BQU07Z0RBQUlELFdBQVU7Ozs7OzswREFDM0IsOERBQUNEO2dEQUNDQyxXQUFVO2dEQUNWRSxTQUFTZjs7a0VBRVQsOERBQUNtQjtrRUFBTXpCOzs7Ozs7a0VBQ1AsOERBQUNsQix5SEFBV0E7d0RBQUNzQyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSXZCLDhEQUFDRjt3Q0FBSUMsV0FBVTt3Q0FBc0JFLFNBQVNmO2tEQUM1Qyw0RUFBQ3RCLG1IQUFLQTs0Q0FBQ29DLE1BQU07NENBQUlELFdBQVU7Ozs7Ozs7Ozs7O29DQUU1QnJCLGdDQUNDLDhEQUFDb0I7d0NBQ0NDLFdBQVU7d0NBQ1ZFLFNBQVNmOzs7Ozs7b0NBR1pSLGdDQUNDLDhEQUFDb0I7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTztnREFBR1AsV0FBVTswREFBMEQ7Ozs7OzswREFHeEUsOERBQUNROzs7OzswREF1QkQsOERBQUNDOzBEQUNFO29EQUFDO29EQUFXO29EQUFVO2lEQUFVLENBQUNDLEdBQUcsQ0FBQyxDQUFDcEIseUJBQ3JDLDhEQUFDcUI7d0RBRUNYLFdBQVU7d0RBQ1ZFLFNBQVMsSUFBTWIsZUFBZUM7OzBFQUU5Qiw4REFBQ1M7Z0VBQUlDLFdBQVU7O29FQUNaO2tGQUVELDhEQUFDaEMsbURBQUtBO3dFQUNKNEMsS0FBSyxHQUFHQyxrREFBaUMsQ0FBQyxjQUFjLEVBQUV2QixTQUFTLElBQUksQ0FBQzt3RUFDeEUwQixPQUFPO3dFQUNQQyxRQUFRO3dFQUNSQyxLQUFLNUI7Ozs7OztvRUFFTkE7Ozs7Ozs7NERBRUZULHFCQUFxQlMsMEJBQ3BCLDhEQUFDMUIsbUhBQUtBO2dFQUFDcUMsTUFBTTtnRUFBSUQsV0FBVTs7Ozs7Ozt1REFoQnhCVjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0EwQmpCLDhEQUFDUztnQ0FBSUMsV0FBVTtnQ0FBcUNtQixLQUFLekM7O2tEQUV2RCw4REFBQ3FCO3dDQUNDQyxXQUFVO3dDQUNWRSxTQUFTZDtrREFFVCw0RUFBQ2YsdUZBQU1BOzRDQUFDMkIsV0FBVTs7Ozs7Ozs7Ozs7a0RBR3BCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDTztvREFBR1AsV0FBVTtvREFBc0JFLFNBQVNkOzhEQUF1Qjs7Ozs7Ozs7Ozs7MERBRXRFLDhEQUFDVzs7a0VBQ0MsOERBQUNxQjt3REFBR3BCLFdBQVU7d0RBQXlERSxTQUFTZDtrRUFBdUI7Ozs7OztrRUFDdkcsOERBQUNpQzt3REFBRXJCLFdBQVU7a0VBQTZEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTlFLDhEQUFDRDt3Q0FDQ0MsV0FBVTt3Q0FDVkUsU0FBU2Q7a0RBRVQsNEVBQUN6Qix5SEFBV0E7NENBQUNzQyxNQUFNOzRDQUFLRCxXQUFXLEdBQ3pCakIsd0JBQXdCLHNCQUFzQixJQUM5Qzs7Ozs7Ozs7Ozs7b0NBRVhBLHVDQUNDLDhEQUFDZ0I7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNTOzs4REFDQyw4REFBQ0U7b0RBQUdYLFdBQVU7O3NFQUNaLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ087Z0VBQUdQLFdBQVU7MEVBQXNCOzs7Ozs7Ozs7OztzRUFFdEMsOERBQUNEOzs4RUFDQyw4REFBQ3FCO29FQUFHcEIsV0FBVTs4RUFBeUQ7Ozs7Ozs4RUFHdkUsOERBQUNxQjtvRUFBRXJCLFdBQVU7OEVBQTZEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzlFLDhEQUFDVztvREFBR1gsV0FBVTs7c0VBQ1osOERBQUM3QixvR0FBZ0JBOzREQUFDOEIsTUFBTTs0REFBSUQsV0FBVTs7Ozs7O3dEQUFzQjs7Ozs7Ozs4REFHOUQsOERBQUNROzs7Ozs4REFDRCw4REFBQ0c7b0RBQUdYLFdBQVU7O3NFQUNaLDhEQUFDNUIsc0ZBQUtBOzREQUFDNkIsTUFBTTs0REFBSUQsV0FBVTs7Ozs7O3dEQUFtQjs7Ozs7Ozs4REFHaEQsOERBQUNROzs7Ozs4REFDRCw4REFBQ0c7b0RBQUdYLFdBQVU7O3NFQUNaLDhEQUFDaEMsbURBQUtBOzREQUNKNEMsS0FBSyxHQUFHQyxrREFBaUMsQ0FBQyx3QkFBd0IsQ0FBQzs0REFDbkVHLE9BQU87NERBQ1BDLFFBQVE7NERBQ1JDLEtBQUk7Ozs7Ozt3REFDSjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXdEI7QUFFQSxpRUFBZXpDLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUE9PSkFcXE1peGRvcm1cXE1peGRvcm0tV2ViLTIuMFxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHN1cGVyYWRtaW5cXFN1cGVyTGF5b3V0LmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IHtcclxuICBCZWxsUmluZyxcclxuICBDaGV2cm9uRG93bixcclxuICBDaGVjayxcclxuICBHbG9iZSxcclxuICBNZW51LFxyXG4gIFNlYXJjaCxcclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgTWRNYW5hZ2VBY2NvdW50cyB9IGZyb20gXCJyZWFjdC1pY29ucy9tZFwiO1xyXG5pbXBvcnQgeyBGYUtleSwgRmFVc2VyIH0gZnJvbSBcInJlYWN0LWljb25zL2ZhNlwiO1xyXG5pbXBvcnQgU3VwZXJTaWRlYmFyIGZyb20gXCIuL1N1cGVyU2lkZWJhclwiO1xyXG5pbXBvcnQgeyB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgXCJAZm9udHNvdXJjZS9udW5pdG8tc2Fuc1wiO1xyXG5cclxuXHJcblxyXG5jb25zdCBTdXBlckxheW91dCA9ICgpID0+IHtcclxuICBcclxuICBjb25zdCBkcm9wZG93blJlZiA9IHVzZVJlZihudWxsKTtcclxuICBjb25zdCBbaXNEcm9wZG93bk9wZW4sIHNldElzRHJvcGRvd25PcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2VsZWN0ZWRMYW5ndWFnZSwgc2V0U2VsZWN0ZWRMYW5ndWFnZV0gPSB1c2VTdGF0ZShcIkVuZ2xpc2hcIik7IC8vIERlZmF1bHQgc2VsZWN0ZWQgbGFuZ3VhZ2VcclxuICBjb25zdCBbaXNQcm9maWxlRHJvcGRvd25PcGVuLCBzZXRJc1Byb2ZpbGVEcm9wZG93bk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpOyAvLyBQcm9maWxlIGRyb3Bkb3duIHN0YXRlXHJcbiAgY29uc3QgW2lzU2lkZWJhck9wZW4sIHNldElzU2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpOyAvLyBTdGF0ZSBmb3Igc2lkZWJhciB2aXNpYmlsaXR5IG9uIG1vYmlsZVxyXG5cclxuICBjb25zdCB0b2dnbGVEcm9wZG93biA9ICgpID0+IHtcclxuICAgIHNldElzRHJvcGRvd25PcGVuKCFpc0Ryb3Bkb3duT3Blbik7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdG9nZ2xlUHJvZmlsZURyb3Bkb3duID0gKCkgPT4ge1xyXG4gICAgc2V0SXNQcm9maWxlRHJvcGRvd25PcGVuKCFpc1Byb2ZpbGVEcm9wZG93bk9wZW4pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHNlbGVjdExhbmd1YWdlID0gKGxhbmd1YWdlKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZExhbmd1YWdlKGxhbmd1YWdlKTtcclxuICAgIHNldElzRHJvcGRvd25PcGVuKGZhbHNlKTsgLy8gQ2xvc2UgZHJvcGRvd24gYWZ0ZXIgc2VsZWN0aW9uXHJcbiAgfTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZnVuY3Rpb24gaGFuZGxlQ2xpY2tPdXRzaWRlKGV2ZW50KSB7XHJcbiAgICAgIGlmIChkcm9wZG93blJlZi5jdXJyZW50ICYmICFkcm9wZG93blJlZi5jdXJyZW50LmNvbnRhaW5zKGV2ZW50LnRhcmdldCkpIHtcclxuICAgICAgICBzZXRJc1Byb2ZpbGVEcm9wZG93bk9wZW4oZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwibW91c2Vkb3duXCIsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwibW91c2Vkb3duXCIsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIHsvKiB7aXNTaWRlYmFyT3BlbiAmJiAoXHJcbiAgICAgICAgPFJ4Q3Jvc3MyXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJiZy10cmFuc3BhcmVudCBcIlxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNTaWRlYmFyT3BlbihmYWxzZSl9XHJcbiAgICAgICAgLz5cclxuICAgICAgKX0gKi99XHJcbiAgICAgIDxTdXBlclNpZGViYXJcclxuICAgICAgICBzZXRJc1NpZGViYXJPcGVuPXtzZXRJc1NpZGViYXJPcGVufVxyXG4gICAgICAgIGlzU2lkZWJhck9wZW49e2lzU2lkZWJhck9wZW59XHJcbiAgICAgIC8+XHJcbiAgICAgIHsvKiA8TWVudVxyXG4gICAgICAgIGNsYXNzTmFtZT0nZHVyYXRpb24tMjAwIGVhc2UtaW4tb3V0IGN1cnNvci1wb2ludGVyIHRleHQtYmxhY2stMTAwIGhvdmVyOnRleHQtYmxhY2snXHJcbiAgICAgICAgc2l6ZT17MjB9XHJcbiAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNTaWRlYmFyT3BlbighaXNTaWRlYmFyT3Blbil9XHJcbiAgICAgIC8+ICovfVxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzaGFkb3cganVzdGlmeS1iZXR3ZWVuIHctZnVsbCBoLWF1dG8gcHgtMiBtZDpweC01IGxnOnB4LTUgcHktMyBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSB0ZXh0LWJsYWNrLTEwMCBsZzpwbC1bMjU1cHhdIG1kOnBsLVsyNTVweF1cIj5cclxuICAgICAgICB7LyogTGVmdCBTZWN0aW9uICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLXgtM1wiPlxyXG4gICAgICAgICAgey8qIE1lbnUgSWNvbiAtIFZpc2libGUgb25seSBvbiBtb2JpbGUgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiBtZDpoaWRkZW5cIj5cclxuICAgICAgICAgICAgPE1lbnVcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkdXJhdGlvbi0yMDAgZWFzZS1pbi1vdXQgY3Vyc29yLXBvaW50ZXIgdGV4dC1ibGFjay0xMDAgaG92ZXI6dGV4dC1ibGFjayBkYXJrOnRleHQtWyNCNkI2QjZdXCJcclxuICAgICAgICAgICAgICBzaXplPXsyMH1cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1NpZGViYXJPcGVuKCFpc1NpZGViYXJPcGVuKX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgey8qIFNlYXJjaCBJbnB1dCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgdy1mdWxsIHNtOnctNjAgbGc6dy04MCBwci00XCI+XHJcbiAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJzZWFyY2hcIlxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGZvciByb29tcyBhbmQgb2ZmZXJzXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC04IGJnLWdyYXktMTAwIHRleHQtZ3JheS0yMDAgcm91bmRlZC1mdWxsIHB5LTIgdy1mdWxsIG91dGxpbmUtbm9uZSBib3JkZXItMiBib3JkZXItZ3JheS0yMDAgdGV4dC14cyBmb250LXBvcHBpbnMgZGFyazpiZy1bIzBEMEQwRF0gZGFyazpib3JkZXIgZGFyazp0ZXh0LVsjNjE2MTYxXVwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxTZWFyY2hcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSAgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtWyM2MTYxNjFdIHRvcC0xLzIgbGVmdC0yLjUgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzJcIlxyXG4gICAgICAgICAgICAgIHNpemU9ezE2fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBSaWdodCBTZWN0aW9uICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLXgtMyBtZDpnYXAteC00IGxnOmdhcC14LTRcIj5cclxuICAgICAgICAgIHsvKiBOb3RpZmljYXRpb24gSWNvbiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgdGV4dC1ncmF5LTU0MCBkYXJrOnRleHQtWyNCNkI2QjZdXCI+XHJcbiAgICAgICAgICAgIDxCZWxsUmluZyBzaXplPXsyMH0gY2xhc3NOYW1lPVwiZm9udC1saWdodFwiIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogTGFuZ3VhZ2UgRHJvcGRvd24gKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggaXRlbXMtY2VudGVyIHRleHQtZ3JheS01NDAgZGFyazp0ZXh0LVsjQjZCNkI2XSBnYXAteC0xXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6ZmxleCBtZDpmbGV4IGl0ZW1zLWNlbnRlciBoaWRkZW4gZ2FwLXgtMVwiPlxyXG4gICAgICAgICAgICAgIDxHbG9iZSBzaXplPXsyMH0gY2xhc3NOYW1lPVwiZm9udC1saWdodFwiIC8+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVEcm9wZG93bn1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8c3Bhbj57c2VsZWN0ZWRMYW5ndWFnZX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gc2l6ZT17MTZ9IC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICB7LyogSWNvbiBvbmx5IGZvciBtb2JpbGUgKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIG1kOmhpZGRlblwiIG9uQ2xpY2s9e3RvZ2dsZURyb3Bkb3dufT5cclxuICAgICAgICAgICAgICA8R2xvYmUgc2l6ZT17MjB9IGNsYXNzTmFtZT1cImZvbnQtbGlnaHRcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAge2lzRHJvcGRvd25PcGVuICYmIChcclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzMwIGJhY2tkcm9wLWJsdXItc20gei0xMFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVEcm9wZG93bn0gLy8gQ2xvc2UgZHJvcGRvd24gd2hlbiBjbGlja2luZyBvbiB0aGUgb3ZlcmxheVxyXG4gICAgICAgICAgICAgID48L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAge2lzRHJvcGRvd25PcGVuICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC1mdWxsIHJpZ2h0LTAgbXQtMiB3LTUyIGJnLXdoaXRlIGRhcms6YmctWyMxNzE2MTZdIHJvdW5kZWQteGwgc2hhZG93LWxnIHotMTBcIj5cclxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJweC00IHB5LTMgZm9udC1udW5pdG8gdGV4dC1zbSBsZzp0ZXh0LWJhc2UgIGZvbnQtbm9ybWFsXCI+XHJcbiAgICAgICAgICAgICAgICAgIFNlbGVjdCBMYW5ndWFnZVxyXG4gICAgICAgICAgICAgICAgPC9oMT5cclxuICAgICAgICAgICAgICAgIDxociAvPlxyXG4gICAgICAgICAgICAgICAgey8qIDx1bD5cclxuICAgICAgICAgICAgICAgICAge1tcIkVuZ2xpc2hcIiwgXCJGcmVuY2hcIiwgXCJTcGFuaXNoXCJdLm1hcCgobGFuZ3VhZ2UpID0+IChcclxuICAgICAgICAgICAgICAgICAgICA8bGlcclxuICAgICAgICAgICAgICAgICAgICAgIGtleT17bGFuZ3VhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTQgaG92ZXI6YmctZ3JheS0xMDAgY3Vyc29yLXBvaW50ZXIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNlbGVjdExhbmd1YWdlKGxhbmd1YWdlKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17YCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUzNfVVJMX0ZFfS9mcm9udC1pbWFnZXMvJHtsYW5ndWFnZX0ucG5nYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXszMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2xhbmd1YWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7bGFuZ3VhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZExhbmd1YWdlID09PSBsYW5ndWFnZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBzaXplPXsyMH0gY2xhc3NOYW1lPVwidGV4dC1ibGFja1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgPC91bD4gKi99XHJcbiAgICAgICAgICAgICAgICA8dWw+XHJcbiAgICAgICAgICAgICAgICAgIHtbXCJFbmdsaXNoXCIsIFwiRnJlbmNoXCIsIFwiU3BhbmlzaFwiXS5tYXAoKGxhbmd1YWdlKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2xhbmd1YWdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS00IGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctWyM0NzQ2NDZkMF0gY3Vyc29yLXBvaW50ZXIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNlbGVjdExhbmd1YWdlKGxhbmd1YWdlKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TM19VUkxfRkV9L2Zyb250LWltYWdlcy8ke2xhbmd1YWdlfS5wbmdgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPXs0NH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17bGFuZ3VhZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtsYW5ndWFnZX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkTGFuZ3VhZ2UgPT09IGxhbmd1YWdlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrIHNpemU9ezIwfSBjbGFzc05hbWU9XCJ0ZXh0LWJsYWNrIGRhcms6dGV4dC1bI0I2QjZCNl1cIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogUHJvZmlsZSBTZWN0aW9uICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAteC0yXCIgcmVmPXtkcm9wZG93blJlZn0+XHJcbiAgICAgICAgICAgIHsvKiBJY29uIGZvciBtb2JpbGUgKi99XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGxnOmhpZGRlbiBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1uZXV0cmFsLTg1MCBkYXJrOnRleHQtWyNCNkI2QjZdXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVQcm9maWxlRHJvcGRvd259XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8RmFVc2VyIGNsYXNzTmFtZT1cInRleHQtbGdcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgey8qIEZ1bGwgUHJvZmlsZSBJbmZvIGZvciBsYXJnZXIgc2NyZWVucyAqL31cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIgZ2FwLXgtM1wiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgdy0xMSBoLTExIGJnLXllbGxvdy01NTBcIj5cclxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1ib2xkXCIgb25DbGljaz17dG9nZ2xlUHJvZmlsZURyb3Bkb3dufT5NPC9oMT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtbmV1dHJhbC03MDAgZGFyazp0ZXh0LVsjQjZCNkI2XVwiIG9uQ2xpY2s9e3RvZ2dsZVByb2ZpbGVEcm9wZG93bn0+TW9uaSBSb3k8L2gzPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtbmV1dHJhbC04NTAgZGFyazp0ZXh0LVsjQjZCNkI2XVwiPkFkbWluPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgey8qIERyb3Bkb3duIFRyaWdnZXIgKi99XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy01IGgtNSBkdXJhdGlvbi0yMDAgZWFzZS1pbi1vdXQgYm9yZGVyIHJvdW5kZWQtZnVsbCBib3JkZXItbmV1dHJhbC04NTAgdGV4dC1uZXV0cmFsLTg1MCBkYXJrOnRleHQtWyNCNkI2QjZdXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVQcm9maWxlRHJvcGRvd259XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gc2l6ZT17MTZ9ICBjbGFzc05hbWU9e2Ake1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1Byb2ZpbGVEcm9wZG93bk9wZW4gPyBcInJvdGF0ZS0xODAgbWItMC41XCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB9YH0vPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAge2lzUHJvZmlsZURyb3Bkb3duT3BlbiAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCByaWdodC0wIG10LTIgdy01NiBiZy13aGl0ZSBkYXJrOmJnLVsjMTcxNjE2XSByb3VuZGVkLWxnIHNoYWRvdy1sZyB6LTEwXCI+XHJcbiAgICAgICAgICAgICAgICA8dWw+XHJcbiAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJsZzpoaWRkZW4gaXRlbXMtY2VudGVyIGZsZXggcHgtMyBwdC0yIGdhcC14LTNcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGUgcm91bmRlZC1mdWxsIHctMTEgaC0xMSBiZy15ZWxsb3ctNTUwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtYm9sZFwiPk08L2gxPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LWJvbGQgdGV4dC1uZXV0cmFsLTcwMCBkYXJrOnRleHQtWyNCNkI2QjZdXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIE1vbmkgUm95XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtbmV1dHJhbC04NTAgZGFyazp0ZXh0LVsjQjZCNkI2XVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBBZG1pblxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwicHgtNCBweS00IGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOmJnLWdyYXktMTAwIGN1cnNvci1wb2ludGVyIHRleHQtYmFzZSBkYXJrOnRleHQtWyNCNkI2QjZdIGRhcms6aG92ZXI6YmctWyM0NzQ2NDZkMF1cIj5cclxuICAgICAgICAgICAgICAgICAgICA8TWRNYW5hZ2VBY2NvdW50cyBzaXplPXsyMn0gY2xhc3NOYW1lPVwidGV4dC1za3ktYmx1ZS02NTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIE1hbmFnZSBQcm9maWxlc1xyXG4gICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8aHIgLz5cclxuICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cInB4LTQgcHktNCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBob3ZlcjpiZy1ncmF5LTEwMCBjdXJzb3ItcG9pbnRlciB0ZXh0LWJhc2UgZGFyazp0ZXh0LVsjQjZCNkI2XSBkYXJrOmhvdmVyOmJnLVsjNDc0NjQ2ZDBdXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEZhS2V5IHNpemU9ezE2fSBjbGFzc05hbWU9XCJ0ZXh0LVsjODA4MEZGXVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgQ2hhbmdlIFBhc3N3b3JkXHJcbiAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgIDxociAvPlxyXG4gICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwicHgtNCBweS00IGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOmJnLWdyYXktMTAwIGN1cnNvci1wb2ludGVyIHRleHQtYmFzZSBkYXJrOnRleHQtWyNCNkI2QjZdIGRhcms6aG92ZXI6YmctWyM0NzQ2NDZkMF1cIj5cclxuICAgICAgICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgICAgICAgIHNyYz17YCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUzNfVVJMX0ZFfS9mcm9udC1pbWFnZXMvTG9nT3V0LnBuZ2B9XHJcbiAgICAgICAgICAgICAgICAgICAgICB3aWR0aD17MTR9XHJcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezEyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgYWx0PVwiTG9nT3V0XCJcclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgIExvZyBvdXRcclxuICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8Lz5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgU3VwZXJMYXlvdXQ7XHJcbiJdLCJuYW1lcyI6WyJCZWxsUmluZyIsIkNoZXZyb25Eb3duIiwiQ2hlY2siLCJHbG9iZSIsIk1lbnUiLCJTZWFyY2giLCJJbWFnZSIsIlJlYWN0IiwidXNlU3RhdGUiLCJNZE1hbmFnZUFjY291bnRzIiwiRmFLZXkiLCJGYVVzZXIiLCJTdXBlclNpZGViYXIiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJTdXBlckxheW91dCIsImRyb3Bkb3duUmVmIiwiaXNEcm9wZG93bk9wZW4iLCJzZXRJc0Ryb3Bkb3duT3BlbiIsInNlbGVjdGVkTGFuZ3VhZ2UiLCJzZXRTZWxlY3RlZExhbmd1YWdlIiwiaXNQcm9maWxlRHJvcGRvd25PcGVuIiwic2V0SXNQcm9maWxlRHJvcGRvd25PcGVuIiwiaXNTaWRlYmFyT3BlbiIsInNldElzU2lkZWJhck9wZW4iLCJ0b2dnbGVEcm9wZG93biIsInRvZ2dsZVByb2ZpbGVEcm9wZG93biIsInNlbGVjdExhbmd1YWdlIiwibGFuZ3VhZ2UiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwib25DbGljayIsImlucHV0IiwidHlwZSIsInBsYWNlaG9sZGVyIiwic3BhbiIsImgxIiwiaHIiLCJ1bCIsIm1hcCIsImxpIiwic3JjIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1MzX1VSTF9GRSIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwicmVmIiwiaDMiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/superadmin/SuperLayout.jsx\n");

/***/ }),

/***/ "./components/superadmin/SuperSidebar.jsx":
/*!************************************************!*\
  !*** ./components/superadmin/SuperSidebar.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"__barrel_optimize__?names=Box!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/ownerflowServices */ \"./services/ownerflowServices.jsx\");\n/* harmony import */ var _ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ownerFlow/headerContex */ \"./components/ownerFlow/headerContex.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\n/* harmony import */ var _barrel_optimize_names_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=FiSettings!=!react-icons/fi */ \"__barrel_optimize__?names=FiSettings!=!./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_PiSquaresFourBold_react_icons_pi__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=PiSquaresFourBold!=!react-icons/pi */ \"__barrel_optimize__?names=PiSquaresFourBold!=!./node_modules/react-icons/pi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RxCross2_react_icons_rx__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=RxCross2!=!react-icons/rx */ \"__barrel_optimize__?names=RxCross2!=!./node_modules/react-icons/rx/index.mjs\");\n/* harmony import */ var _ThemeSwitcher__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ThemeSwitcher */ \"./components/superadmin/ThemeSwitcher.jsx\");\n/* harmony import */ var _barrel_optimize_names_BiLogOutCircle_react_icons_bi__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BiLogOutCircle!=!react-icons/bi */ \"__barrel_optimize__?names=BiLogOutCircle!=!./node_modules/react-icons/bi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaRegCircleUser_FaRegUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!react-icons/fa6 */ \"__barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_GiKeyCard_GiNotebook_react_icons_gi__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=GiKeyCard,GiNotebook!=!react-icons/gi */ \"__barrel_optimize__?names=GiKeyCard,GiNotebook!=!./node_modules/react-icons/gi/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=RiArrowDropDownLine!=!react-icons/ri */ \"__barrel_optimize__?names=RiArrowDropDownLine!=!./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!react-icons/lu */ \"__barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!./node_modules/react-icons/lu/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbBrandWechat_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=TbBrandWechat!=!react-icons/tb */ \"__barrel_optimize__?names=TbBrandWechat!=!./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_MdOutlineRateReview_react_icons_md__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=MdOutlineRateReview!=!react-icons/md */ \"__barrel_optimize__?names=MdOutlineRateReview!=!./node_modules/react-icons/md/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaHandHoldingUsd_FaHeadset_react_icons_fa__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!react-icons/fa */ \"__barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _fontsource_poppins__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fontsource/poppins */ \"./node_modules/@fontsource/poppins/index.css\");\n/* harmony import */ var _fontsource_poppins__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_fontsource_poppins__WEBPACK_IMPORTED_MODULE_10__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__, _ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__]);\n([_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__, _ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SuperSidebar = ({ isCollapsed, isSidebarOpen, setIsSidebarOpen })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\"); // State for active menu item\n    // eslint-disable-next-line no-unused-vars\n    const [flags, setFlags] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [isContentOpen, setIsContentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isManagementOpen, setIsManagementOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isPaymentOpen, setIsPaymentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isLandingPageOpen, setIsLandingPageOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isOtherContentOpen, setIsOtherContentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { updateUserStatus, updateUserRole, updateHopId } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_8__.useNavbar)();\n    // const [activeItem, setActiveItem] = useState({ name: \"\", url: \"\" });\n    const [activeDropdownItem, setActiveDropdownItem] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\"); // Tracks the active dropdown item\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SuperSidebar.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {\n                    setIsSidebarOpen(false);\n                }\n            }\n            if (isSidebarOpen) {\n                document.addEventListener(\"mousedown\", handleClickOutside);\n            } else {\n                document.removeEventListener(\"mousedown\", handleClickOutside);\n            }\n            return ({\n                \"SuperSidebar.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"SuperSidebar.useEffect\"];\n        }\n    }[\"SuperSidebar.useEffect\"], [\n        isSidebarOpen\n    ]);\n    const handleDropdownItemClick = (dropdownItem)=>{\n        setActiveDropdownItem(dropdownItem);\n        setActiveItem(\"Content\"); // Keep the parent \"Content\" option highlighted\n    };\n    // const handleItemClick = (item) => {\n    //   setActiveItem(item);\n    //   setActiveDropdownItem(\"\"); // Clear dropdown selection if another main item is selected\n    // };\n    const handleItemClick = (name)=>{\n        setActiveItem(name);\n        // setActiveItem({ name: itemName, url: itemUrl })// Set the active menu item\n        if (name === \"Multiple Property\") {\n            handleOpenMultipleProperty();\n            setActiveDropdownItem(\"\");\n        }\n        if (window.innerWidth < 640) {\n            setIsSidebarOpen(false); // Close sidebar on mobile after selecting\n        }\n    };\n    const handleLogout = async ()=>{\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"token\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"name\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"id\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"role\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"hopid\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"email\");\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"contact\");\n        updateUserStatus(\"\");\n        updateUserRole(\"\");\n        updateHopId(\"\");\n        const payload = {\n            token: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.getItemLocalStorage)(\"FCT\"),\n            userId: (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.getItemLocalStorage)(\"uid\")\n        };\n        try {\n            await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_6__.removeFirebaseToken)(payload);\n            console.log(\"FCM token removed successfully.\");\n            (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"FCT\");\n        } catch (error) {\n            console.error(\"Error removing FCM token:\", error);\n        }\n        (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.removeItemLocalStorage)(\"uid\");\n        router.push(\"/owner/login\");\n    };\n    // eslint-disable-next-line no-unused-vars\n    const [openMultipleProperty, setOpenMultipleProperty] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const handleOpenMultipleProperty = ()=>setOpenMultipleProperty(true);\n    const { profileData, propertyData } = (0,_ownerFlow_headerContex__WEBPACK_IMPORTED_MODULE_7__.useHeaderOwner)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SuperSidebar.useEffect\": ()=>{\n            const fetchFlags = {\n                \"SuperSidebar.useEffect.fetchFlags\": async ()=>{\n                    try {\n                        const response = await fetch(\"https://restcountries.com/v3.1/all\");\n                        const data = await response.json();\n                        const filteredFlags = data.filter({\n                            \"SuperSidebar.useEffect.fetchFlags.filteredFlags\": (country)=>{\n                                const commonName = country.name.common;\n                                return propertyData?.address?.country?.includes(commonName);\n                            }\n                        }[\"SuperSidebar.useEffect.fetchFlags.filteredFlags\"]).map({\n                            \"SuperSidebar.useEffect.fetchFlags.filteredFlags\": (country)=>({\n                                    id: country.cca3,\n                                    img: country.flags.svg,\n                                    name: country.name.common\n                                })\n                        }[\"SuperSidebar.useEffect.fetchFlags.filteredFlags\"]);\n                        setFlags(filteredFlags);\n                    } catch (error) {\n                        console.error(\"Error fetching flags:\", error);\n                    }\n                }\n            }[\"SuperSidebar.useEffect.fetchFlags\"];\n            if (propertyData?.address?.country) {\n                fetchFlags();\n            }\n        }\n    }[\"SuperSidebar.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"SuperSidebar.useEffect\": ()=>{\n            const setActiveItemFromPath = {\n                \"SuperSidebar.useEffect.setActiveItemFromPath\": (path)=>{\n                    const dashboardPath = path.split(\"/dashboard\")[1];\n                    if (!dashboardPath || dashboardPath === \"/\") {\n                        setActiveItem(\"Dashboard\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/user\")) {\n                        setActiveItem(\"User\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/booking\")) {\n                        setActiveItem(\"Booking\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/hostel\")) {\n                        setActiveItem(\"Hostel\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/communication\")) {\n                        setActiveItem(\"Communication\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/notification\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/announcement\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/create-notification\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/bulk-notification\")) {\n                        setActiveItem(\"Notification\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/feedback\")) {\n                        setActiveItem(\"Feedback\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/support\")) {\n                        setActiveItem(\"Support\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    } else if (dashboardPath.includes(\"/profile\")) {\n                        setActiveItem(\"Profile\");\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                        setActiveDropdownItem(\"\");\n                    }\n                    // } else if (dashboardPath.includes(\"/payment\")) {\n                    //   setActiveItem(\"Payment\");\n                    //   setIsContentOpen(false);\n                    //   setIsLandingPageOpen(false);\n                    //   setActiveDropdownItem(\"\");\n                    // }\n                    // Handle Content submenu items\n                    if (dashboardPath.includes(\"/mobile-homepage\")) {\n                        setActiveItem(\"Content\", \"Mobile Home Page\");\n                        setActiveDropdownItem(\"Mobile Home Page\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/content-banner\")) {\n                        setActiveItem(\"Content\", \"Banner\");\n                        setActiveDropdownItem(\"Banner\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/explore-world\")) {\n                        setActiveItem(\"Content\", \"Explore the World\");\n                        setActiveDropdownItem(\"Explore the World\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/featured-hostel\")) {\n                        setActiveItem(\"Content\", \"Featured Hostel\");\n                        setActiveDropdownItem(\"Featured Hostel\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/travel-activity\")) {\n                        setActiveItem(\"Content\", \"Travel by Activity\");\n                        setActiveDropdownItem(\"Travel by Activity\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/split-bill\")) {\n                        setActiveItem(\"Content\", \"Split bill fairfare, Mix mate, Mix ride\");\n                        setActiveDropdownItem(\"Split bill fairfare, Mix mate, Mix ride\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/event\")) {\n                        setActiveItem(\"Content\", \"Event\");\n                        setActiveDropdownItem(\"Event\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/content-blog\")) {\n                        setActiveItem(\"Content\", \"Landing-Blog\");\n                        setActiveDropdownItem(\"Landing-Blog\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(true);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/about-us\")) {\n                        setActiveItem(\"Content\", \"About Us\");\n                        setActiveDropdownItem(\"About Us\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/awards\")) {\n                        setActiveItem(\"Content\", \"Awards\");\n                        setActiveDropdownItem(\"Awards\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/help\")) {\n                        setActiveItem(\"Content\", \"Help\");\n                        setActiveDropdownItem(\"Help\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/booking-guarantee\")) {\n                        setActiveItem(\"Content\", \"Booking Guarantee\");\n                        setActiveDropdownItem(\"Booking Guarantee\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/booking-refund-policy\")) {\n                        setActiveItem(\"Content\", \"Booking Refund Policy\");\n                        setActiveDropdownItem(\"Booking Refund Policy\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/privacy-policy\")) {\n                        setActiveItem(\"Content\", \"Privacy Policy\");\n                        setActiveDropdownItem(\"Privacy Policy\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/terms&conditions\")) {\n                        setActiveItem(\"Content\", \"Terms & Conditions\");\n                        setActiveDropdownItem(\"Terms & Conditions\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/faqs\")) {\n                        setActiveItem(\"Content\", \"Faqs\");\n                        setActiveDropdownItem(\"Faqs\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(true);\n                    } else if (dashboardPath.includes(\"/offer-banner\")) {\n                        setActiveItem(\"Content\", \"Offer Banner\");\n                        setActiveDropdownItem(\"Offer Banner\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/blog\")) {\n                        setActiveItem(\"Content\", \"Blog\");\n                        setActiveDropdownItem(\"Blog\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    } else if (dashboardPath.includes(\"/contact\")) {\n                        setActiveItem(\"Content\", \"Contact Us\");\n                        setActiveDropdownItem(\"Contact Us\");\n                        setIsContentOpen(true);\n                        setIsManagementOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsLandingPageOpen(false);\n                        setIsOtherContentOpen(false);\n                    }\n                    // Handle Management submenu items\n                    if (dashboardPath.includes(\"/management-admin\")) {\n                        setActiveItem(\"Management\", \"Admin\");\n                        setIsContentOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsManagementOpen(true);\n                        setActiveDropdownItem(\"Admin\");\n                    } else if (dashboardPath.includes(\"/management-audit\")) {\n                        setActiveItem(\"Management\", \"Audit Logs\");\n                        setIsContentOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsManagementOpen(true);\n                        setActiveDropdownItem(\"Audit Logs\");\n                    } else if (dashboardPath.includes(\"/management-status\")) {\n                        setActiveItem(\"Management\", \"Status\");\n                        setIsContentOpen(false);\n                        setIsPaymentOpen(false);\n                        setIsManagementOpen(true);\n                        setActiveDropdownItem(\"Status\");\n                    }\n                    // Handle Management submenu items\n                    if (dashboardPath.includes(\"/payment-booking\")) {\n                        setActiveItem(\"Payment\", \"Bookings\");\n                        setIsPaymentOpen(true);\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setActiveDropdownItem(\"Bookings\");\n                    } else if (dashboardPath.includes(\"/payment-subscription\")) {\n                        setActiveItem(\"Payment\", \"Subscription\");\n                        setIsPaymentOpen(true);\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setActiveDropdownItem(\"Subscription\");\n                    } else if (dashboardPath.includes(\"/payment-event\")) {\n                        setActiveItem(\"Payment\", \"Event\");\n                        setIsPaymentOpen(true);\n                        setIsContentOpen(false);\n                        setIsManagementOpen(false);\n                        setActiveDropdownItem(\"Event\");\n                    }\n                }\n            }[\"SuperSidebar.useEffect.setActiveItemFromPath\"];\n            setActiveItemFromPath(pathname);\n        }\n    }[\"SuperSidebar.useEffect\"], [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        ref: sidebarRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_11__.Box, {\n            role: \"presentation\",\n            sx: {\n                width: 327\n            },\n            className: `bg-white dark:bg-[#0D0D0D] w-[240px]  fixed z-10 transition-transform duration-300 h-[calc(100vh)] overflow-y-auto scrollbar-hide ${isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} sm:translate-x-0`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"fixed top-4 left-4 z-20 p-2 rounded-md sm:hidden\",\n                    onClick: ()=>setIsSidebarOpen(false),\n                    children: isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RxCross2_react_icons_rx__WEBPACK_IMPORTED_MODULE_12__.RxCross2, {\n                        className: \"bg-transparent text-black dark:text-[#B6B6B6]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                        lineNumber: 478,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-full gap-x-2 h-[70px] \",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            src: profileData?.profileImage?.objectURL ? profileData?.profileImage?.objectURL : `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/avatar.png`,\n                            alt: \"Profile Pic\",\n                            className: \"rounded-lg w-[40px] h-[40px]\",\n                            width: 40,\n                            height: 40,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/logo.png`,\n                            alt: \"Mixdorm\",\n                            width: 140,\n                            height: 50,\n                            title: \"Mixdorm\",\n                            className: \"object-contain w-fit h-fit max-w-20 max-h-11 flex items-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col justify-between h-[1000px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-full ${isCollapsed ? \"px-0\" : \"px-0\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"w-full text-sm  text-black-100 flex flex-col h-auto \",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4  hover:border-[#50C2FF] ${activeItem === \"Dashboard\" ? \"border-l-4 border-[#50C2FF]\" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white transition duration-300 ease-in-out transform hover:scale-105 mt-0.5 hover:font-semibold ${activeItem === \"Dashboard\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Dashboard\");\n                                                router.push(\"/superadmin/dashboard\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Dashboard\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PiSquaresFourBold_react_icons_pi__WEBPACK_IMPORTED_MODULE_13__.PiSquaresFourBold, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Dashboard\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D]  hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"User\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white transition duration-300 ease-in-out transform hover:scale-105 mt-0.5 hover:font-semibold ${activeItem === \"User\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"User\");\n                                                router.push(\"/superadmin/dashboard/user\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/user\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"User\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegCircleUser_FaRegUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_14__.FaRegUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" User\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Content\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-0.5  transition duration-300 ease-in-out transform\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsContentOpen(!isContentOpen),\n                                                    className: `flex items-center gap-2 cursor-pointer font-poppins text-sm h-[48px] pl-8 pr-10 rounded-lg ${activeItem === \"Content\" || isContentOpen ? \"bg-[#50C2FF] text-white font-semibold\" : \"text-gray-700 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:text-white hover:font-semibold\"} transition duration-300 ease-in-out transform hover:scale-105`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GiKeyCard_GiNotebook_react_icons_gi__WEBPACK_IMPORTED_MODULE_15__.GiNotebook, {\n                                                            className: \"text-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" Content\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                            className: `text-2xl float-end transition-transform ${isContentOpen ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isContentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-2 space-y-4 ml-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/mobile-homepage\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Mobile Home Page\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Mobile Home Page\");\n                                                                    handleItemClick(\"Mobile Home Page\");\n                                                                },\n                                                                children: \"Mobile Home Page\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setIsLandingPageOpen(!isLandingPageOpen),\n                                                                    className: `flex items-center cursor-pointer text-sm font-poppins rounded-lg ${activeItem === \"Landing Page\" || isLandingPageOpen ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"} `,\n                                                                    children: [\n                                                                        \"Landing Page\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                                            className: `text-2xl float-end transition-transform ${isLandingPageOpen ? \"rotate-180\" : \"\"}`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                isLandingPageOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"mt-2 ml-4 space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/content-banner\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Banner\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Banner\");\n                                                                                    handleItemClick(\"Banner\");\n                                                                                },\n                                                                                children: \"Banner\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/explore-world\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Explore the World\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Explore the World\");\n                                                                                    handleItemClick(\"Explore the World\");\n                                                                                },\n                                                                                children: \"Explore the World\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 629,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 628,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/featured-hostel\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Featured Hostel\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Featured Hostel\");\n                                                                                    handleItemClick(\"Featured Hostel\");\n                                                                                },\n                                                                                children: \"Featured Hostel\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/travel-activity\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Travel by Activity\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Travel by Activity\");\n                                                                                    handleItemClick(\"Travel by Activity\");\n                                                                                },\n                                                                                children: \"Travel by Activity\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 661,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 660,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/split-bill\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Split bill fairfare, Mix mate, Mix ride\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Split bill fairfare, Mix mate, Mix ride\");\n                                                                                    handleItemClick(\"Split bill fairfare, Mix mate, Mix ride\");\n                                                                                },\n                                                                                children: \"Split bill fairfare, Mix mate, Mix ride\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/event\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Event\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Event\");\n                                                                                    handleItemClick(\"Event\");\n                                                                                },\n                                                                                children: \"Event\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 699,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/content-blog\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Landing-Blog\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Landing-Blog\");\n                                                                                    handleItemClick(\"Landing-Blog\");\n                                                                                },\n                                                                                children: \"Landing-Blog\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 715,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setIsOtherContentOpen(!isOtherContentOpen),\n                                                                    className: `flex items-centercursor-pointer text-sm font-poppins rounded-lg ${activeItem === \"Other Content\" || isOtherContentOpen ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"} `,\n                                                                    children: [\n                                                                        \"Other Content\",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                                            className: `text-2xl float-end transition-transform ${isOtherContentOpen ? \"rotate-180\" : \"\"}`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                isOtherContentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"mt-2 ml-4 space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/about-us\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"About Us\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"About Us\");\n                                                                                    handleItemClick(\"About Us\");\n                                                                                },\n                                                                                children: \"About Us\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 754,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/awards\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Awards\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Awards\");\n                                                                                    handleItemClick(\"Awards\");\n                                                                                },\n                                                                                children: \"Awards\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/help\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Help\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Help\");\n                                                                                    handleItemClick(\"Help\");\n                                                                                },\n                                                                                children: \"Help\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 786,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 785,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/booking-guarantee\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Booking Guarantee\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Booking Guarantee\");\n                                                                                    handleItemClick(\"Booking Guarantee\");\n                                                                                },\n                                                                                children: \"Booking Guarantee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/booking-refund-policy\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Booking Refund Policy\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Booking Refund Policy\");\n                                                                                    handleItemClick(\"Booking Refund Policy\");\n                                                                                },\n                                                                                children: \"Booking Refund Policy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 820,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/privacy-policy\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Privacy Policy\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Privacy Policy\");\n                                                                                    handleItemClick(\"Privacy Policy\");\n                                                                                },\n                                                                                children: \"Privacy Policy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/terms&conditions\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Terms & Conditions\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Terms & Conditions\");\n                                                                                    handleItemClick(\"Terms & Conditions\");\n                                                                                },\n                                                                                children: \"Terms & Conditions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 857,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/superadmin/dashboard/faqs\",\n                                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Faqs\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                                onClick: ()=>{\n                                                                                    handleDropdownItemClick(\"Faqs\");\n                                                                                    handleItemClick(\"Faqs\");\n                                                                                },\n                                                                                children: \"Faqs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/offer-banner\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Offer Banner\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Offer Banner\");\n                                                                    handleItemClick(\"Offer Banner\");\n                                                                },\n                                                                children: \"Offer Banner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/blog\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Blog\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Blog\");\n                                                                    handleItemClick(\"Blog\");\n                                                                },\n                                                                children: \"Blog\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/contact\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Contact Us\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Contact Us\"), handleItemClick(\"Contact Us\");\n                                                                },\n                                                                children: \"Contact Us\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 926,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Booking\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Booking\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Booking\");\n                                                router.push(\"/superadmin/dashboard/booking\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/booking\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700  dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Booking\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__.LuCalendarDays, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Booking\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Hostel\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Hostel\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Hostel\");\n                                                router.push(\"/superadmin/dashboard/hostel\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/hostel\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Hostel\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GiKeyCard_GiNotebook_react_icons_gi__WEBPACK_IMPORTED_MODULE_15__.GiKeyCard, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Hostel\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 982,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Communication\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Communication\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Communication\");\n                                                router.push(\"/superadmin/dashboard/communication\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/communication\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Communication\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TbBrandWechat_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__.TbBrandWechat, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Communication\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Notification\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Notification\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Notification\");\n                                                router.push(\"/superadmin/dashboard/notifications\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/notifications\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Notification\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__.LuBellPlus, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Notification\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1027,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Feedback\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Feedback\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Feedback\");\n                                                router.push(\"/superadmin/dashboard/feedback\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/feedback\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Feedback\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MdOutlineRateReview_react_icons_md__WEBPACK_IMPORTED_MODULE_19__.MdOutlineRateReview, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Feedback\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Support\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Support\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Support\");\n                                                router.push(\"/superadmin/dashboard/support\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/support\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Support\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHandHoldingUsd_FaHeadset_react_icons_fa__WEBPACK_IMPORTED_MODULE_20__.FaHeadset, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Support\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1085,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Profile\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: `cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${activeItem === \"Profile\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                            onClick: ()=>{\n                                                handleItemClick(\"Profile\");\n                                                router.push(\"/superadmin/dashboard/profile\");\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/superadmin/dashboard/profile\",\n                                                className: `flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${activeItem === \"Profile\" ? \" text-white font-semibold\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaRegCircleUser_FaRegUser_react_icons_fa6__WEBPACK_IMPORTED_MODULE_14__.FaRegCircleUser, {\n                                                        className: \"text-xl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1118,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \" Profile\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1099,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Management\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-0.5  transition duration-300 ease-in-out transform\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsManagementOpen(!isManagementOpen),\n                                                    className: `flex items-center gap-2 cursor-pointer text-sm font-poppins h-[48px] rounded-lg pr-1 pl-[32px] ${activeItem === \"Management\" || isManagementOpen ? \"bg-[#50C2FF] text-white font-semibold\" : \"text-gray-700 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:text-white hover:font-semibold\"} transition duration-300 ease-in-out transform hover:scale-105`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LuBellPlus_LuCalendarDays_LuUserRoundCog_react_icons_lu__WEBPACK_IMPORTED_MODULE_17__.LuUserRoundCog, {\n                                                            className: \"text-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" Management\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                            className: `text-2xl mt-0.5 float-end transition-transform ${isManagementOpen ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1140,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isManagementOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-2 ml-14 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/management-admin\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Admin\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 dark:text-[#ffffff] dark:hover:text-[#50C2FF] hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Admin\");\n                                                                    handleItemClick(\"Admin\");\n                                                                },\n                                                                children: \"Admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1148,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/management-audit\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Audit Logs\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Audit Logs\");\n                                                                    handleItemClick(\"Audit Logs\");\n                                                                },\n                                                                children: \"Audit Logs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1166,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1165,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/management-status\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Status\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Status\");\n                                                                    handleItemClick(\"Status\");\n                                                                },\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1182,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1147,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Payment\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"mt-0.5 ml-1.5  transition duration-300 ease-in-out transform\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsPaymentOpen(!isPaymentOpen),\n                                                    className: `flex items-center  gap-1.5 cursor-pointer text-sm font-poppins h-[48px] rounded-lg pr-8  pl-[26px] ${activeItem === \"Payment\" || isPaymentOpen ? \"bg-[#50C2FF] text-white font-semibold\" : \"text-gray-700 hover:bg-[#50C2FF] hover:text-white hover:font-semibold dark:text-[#ffffff]\"} transition duration-300 ease-in-out transform hover:scale-105 `,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHandHoldingUsd_FaHeadset_react_icons_fa__WEBPACK_IMPORTED_MODULE_20__.FaHandHoldingUsd, {\n                                                            className: \"text-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1236,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" Payment\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_RiArrowDropDownLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_16__.RiArrowDropDownLine, {\n                                                            className: `text-2xl float-end transition-transform ${isPaymentOpen ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1237,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isPaymentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"mt-2 ml-14 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/payment-booking\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Bookings\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600  hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Bookings\");\n                                                                    handleItemClick(\"Bookings\");\n                                                                },\n                                                                children: \"Bookings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1246,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1245,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/payment-subscription\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Subscription\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Subscription\");\n                                                                    handleItemClick(\"Subscription\");\n                                                                },\n                                                                children: \"Subscription\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1263,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1262,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/superadmin/dashboard/payment-event\",\n                                                                className: `text-sm font-poppins ${activeDropdownItem === \"Event\" ? \"text-[#50C2FF] font-semibold\" : \"text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]\"}`,\n                                                                onClick: ()=>{\n                                                                    handleDropdownItemClick(\"Event\");\n                                                                    handleItemClick(\"Event\");\n                                                                },\n                                                                children: \"Event\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                                lineNumber: 1279,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                            lineNumber: 1278,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                        lineNumber: 1222,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 505,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `mt-auto w-full ${isCollapsed ? \"px-0\" : \"px-0\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                    className: \"border-1.5 p-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                    lineNumber: 1302,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"w-full text-sm font-poppins  flex flex-col h-[150px] mt-10 md:mt-5 \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Theme\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: `flex items-center px-7 cursor-pointer first:mt-0 text-sm font-poppins h-[48px]  gap-x-2 text-gray-600 dark:text-[#ffffff]  rounded-lg hover:bg-[#50C2FF] hover:text-white hover:font-semibold  ${activeItem === \"Theme\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeSwitcher__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1309,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1304,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Settings\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: `flex items-center px-7 cursor-pointer first:mt-0  text-sm font-poppins rounded-lg h-[48px]  gap-x-2  text-gray-600 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:font-semibold hover:text-white  ${isCollapsed ? \"justify-center\" : \"justify-start\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_21__.FiSettings, {\n                                                        className: ` ${isCollapsed ? \"\" : \"text-xl\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    isCollapsed ? \"\" : \"Settings \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1326,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1319,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-6 bg-white  dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${activeItem === \"Logout\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: `cursor-pointer text-sm font-poppins h-[48px]  px-7 flex items-center rounded-lg gap-x-2 text-gray-600 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:font-semibold hover:text-white  first:mt-0${isCollapsed ? \"justify-center\" : \"justify-start\"}`,\n                                                onClick: handleLogout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BiLogOutCircle_react_icons_bi__WEBPACK_IMPORTED_MODULE_22__.BiLogOutCircle, {\n                                                        className: `${isCollapsed ? \"\" : \"text-xl\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                        lineNumber: 1347,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    isCollapsed ? \"\" : \"Logout \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                                lineNumber: 1341,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                            lineNumber: 1336,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                                    lineNumber: 1303,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                            lineNumber: 1301,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n                    lineNumber: 504,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n            lineNumber: 467,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\SuperSidebar.jsx\",\n        lineNumber: 466,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SuperSidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3N1cGVyYWRtaW4vU3VwZXJTaWRlYmFyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSThDO0FBQ2pCO0FBQ087QUFDTDtBQUlDO0FBQ29CO0FBQ2U7QUFDUjtBQUNUO0FBQ0w7QUFDTztBQUNWO0FBQ0U7QUFDSTtBQUNhO0FBQ0w7QUFDSDtBQUN1QjtBQUM3QjtBQUNNO0FBQ1E7QUFDaEM7QUFDZTtBQUU1QyxNQUFNOEIsZUFBZSxDQUFDLEVBQUVDLFdBQVcsRUFBRUMsYUFBYSxFQUFFQyxnQkFBZ0IsRUFBRTtJQUNwRSxNQUFNQyxTQUFTTCwwREFBU0E7SUFDeEIsTUFBTSxDQUFDTSxZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBQyxLQUFLLDZCQUE2QjtJQUMvRSwwQ0FBMEM7SUFDMUMsTUFBTSxDQUFDNkIsT0FBT0MsU0FBUyxHQUFHOUIsK0NBQVFBLENBQUMsRUFBRTtJQUNyQyxNQUFNLENBQUMrQixlQUFlQyxpQkFBaUIsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lDLGtCQUFrQkMsb0JBQW9CLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNtQyxlQUFlQyxpQkFBaUIsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3FDLG1CQUFtQkMscUJBQXFCLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUN1QyxvQkFBb0JDLHNCQUFzQixHQUFHeEMsK0NBQVFBLENBQUM7SUFDN0QsTUFBTXlDLFdBQVdqRCw0REFBV0E7SUFFNUIsTUFBTSxFQUFFa0QsZ0JBQWdCLEVBQUVDLGNBQWMsRUFBRUMsV0FBVyxFQUFFLEdBQUd6Qyw4REFBU0E7SUFDbkUsdUVBQXVFO0lBQ3ZFLE1BQU0sQ0FBQzBDLG9CQUFvQkMsc0JBQXNCLEdBQUc5QywrQ0FBUUEsQ0FBQyxLQUFLLGtDQUFrQztJQUVwRyxNQUFNK0MsYUFBYWhELDZDQUFNQSxDQUFDO0lBRTFCRCxnREFBU0E7a0NBQUM7WUFDUixTQUFTa0QsbUJBQW1CQyxLQUFLO2dCQUMvQixJQUFJRixXQUFXRyxPQUFPLElBQUksQ0FBQ0gsV0FBV0csT0FBTyxDQUFDQyxRQUFRLENBQUNGLE1BQU1HLE1BQU0sR0FBRztvQkFDcEUzQixpQkFBaUI7Z0JBQ25CO1lBQ0Y7WUFDQSxJQUFJRCxlQUFlO2dCQUNqQjZCLFNBQVNDLGdCQUFnQixDQUFDLGFBQWFOO1lBQ3pDLE9BQU87Z0JBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO1lBQzVDO1lBQ0E7MENBQU87b0JBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO2dCQUM1Qzs7UUFDRjtpQ0FBRztRQUFDeEI7S0FBYztJQUdsQixNQUFNZ0MsMEJBQTBCLENBQUNDO1FBQy9CWCxzQkFBc0JXO1FBQ3RCN0IsY0FBYyxZQUFZLCtDQUErQztJQUMzRTtJQUVBLHNDQUFzQztJQUN0Qyx5QkFBeUI7SUFDekIsNEZBQTRGO0lBQzVGLEtBQUs7SUFFTCxNQUFNOEIsa0JBQWtCLENBQUNDO1FBQ3ZCL0IsY0FBYytCO1FBQ2QsNkVBQTZFO1FBQzdFLElBQUlBLFNBQVMscUJBQXFCO1lBQ2hDQztZQUNBZCxzQkFBc0I7UUFDeEI7UUFDQSxJQUFJZSxPQUFPQyxVQUFVLEdBQUcsS0FBSztZQUMzQnJDLGlCQUFpQixRQUFRLDBDQUEwQztRQUNyRTtJQUNGO0lBRUEsTUFBTXNDLGVBQWU7UUFDbkJsRSw2RUFBc0JBLENBQUM7UUFDdkJBLDZFQUFzQkEsQ0FBQztRQUN2QkEsNkVBQXNCQSxDQUFDO1FBQ3ZCQSw2RUFBc0JBLENBQUM7UUFDdkJBLDZFQUFzQkEsQ0FBQztRQUN2QkEsNkVBQXNCQSxDQUFDO1FBQ3ZCQSw2RUFBc0JBLENBQUM7UUFDdkI2QyxpQkFBaUI7UUFDakJDLGVBQWU7UUFDZkMsWUFBWTtRQUVaLE1BQU1vQixVQUFVO1lBQ2RDLE9BQU9yRSwwRUFBbUJBLENBQUM7WUFDM0JzRSxRQUFRdEUsMEVBQW1CQSxDQUFDO1FBQzlCO1FBRUEsSUFBSTtZQUNGLE1BQU1LLGdGQUFtQkEsQ0FBQytEO1lBQzFCRyxRQUFRQyxHQUFHLENBQUM7WUFDWnZFLDZFQUFzQkEsQ0FBQztRQUN6QixFQUFFLE9BQU93RSxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzdDO1FBQ0F4RSw2RUFBc0JBLENBQUM7UUFDdkI2QixPQUFPNEMsSUFBSSxDQUFDO0lBQ2Q7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTSxDQUFDQyxzQkFBc0JDLHdCQUF3QixHQUFHeEUsK0NBQVFBLENBQUM7SUFDakUsTUFBTTRELDZCQUE2QixJQUFNWSx3QkFBd0I7SUFFakUsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFlBQVksRUFBRSxHQUFHeEUsdUVBQWNBO0lBRXBESixnREFBU0E7a0NBQUM7WUFDUixNQUFNNkU7cURBQWE7b0JBQ2pCLElBQUk7d0JBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNO3dCQUM3QixNQUFNQyxPQUFPLE1BQU1GLFNBQVNHLElBQUk7d0JBRWhDLE1BQU1DLGdCQUFnQkYsS0FDbkJHLE1BQU07K0VBQUMsQ0FBQ0M7Z0NBQ1AsTUFBTUMsYUFBYUQsUUFBUXZCLElBQUksQ0FBQ3lCLE1BQU07Z0NBQ3RDLE9BQU9WLGNBQWNXLFNBQVNILFNBQVNJLFNBQVNIOzRCQUNsRDs4RUFDQ0ksR0FBRzsrRUFBQyxDQUFDTCxVQUFhO29DQUNqQk0sSUFBSU4sUUFBUU8sSUFBSTtvQ0FDaEJDLEtBQUtSLFFBQVFyRCxLQUFLLENBQUM4RCxHQUFHO29DQUN0QmhDLE1BQU11QixRQUFRdkIsSUFBSSxDQUFDeUIsTUFBTTtnQ0FDM0I7O3dCQUVGdEQsU0FBU2tEO29CQUNYLEVBQUUsT0FBT1gsT0FBTzt3QkFDZEYsUUFBUUUsS0FBSyxDQUFDLHlCQUF5QkE7b0JBQ3pDO2dCQUNGOztZQUVBLElBQUlLLGNBQWNXLFNBQVNILFNBQVM7Z0JBQ2xDUDtZQUNGO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMN0UsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTThGO2dFQUF3QixDQUFDQztvQkFDN0IsTUFBTUMsZ0JBQWdCRCxLQUFLRSxLQUFLLENBQUMsYUFBYSxDQUFDLEVBQUU7b0JBRWpELElBQUksQ0FBQ0QsaUJBQWlCQSxrQkFBa0IsS0FBSzt3QkFDM0NsRSxjQUFjO3dCQUNkSSxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7d0JBQ3RCTSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSWdELGNBQWNSLFFBQVEsQ0FBQyxVQUFVO3dCQUMxQzFELGNBQWM7d0JBQ2RJLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjt3QkFDdEJNLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJZ0QsY0FBY1IsUUFBUSxDQUFDLGFBQWE7d0JBQzdDMUQsY0FBYzt3QkFDZEksaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO3dCQUN0Qk0sc0JBQXNCO29CQUN4QixPQUFPLElBQUlnRCxjQUFjUixRQUFRLENBQUMsWUFBWTt3QkFDNUMxRCxjQUFjO3dCQUNkSSxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7d0JBQ3RCTSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSWdELGNBQWNSLFFBQVEsQ0FBQyxtQkFBbUI7d0JBQ25EMUQsY0FBYzt3QkFDZEksaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO3dCQUN0Qk0sc0JBQXNCO29CQUN4QixPQUFPLElBQUlnRCxjQUFjUixRQUFRLENBQUMsa0JBQWtCO3dCQUNsRDFELGNBQWM7d0JBQ2RJLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjt3QkFDdEJNLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJZ0QsY0FBY1IsUUFBUSxDQUFDLGtCQUFrQjt3QkFDbEQxRCxjQUFjO3dCQUNkSSxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7d0JBQ3RCTSxzQkFBc0I7b0JBQ3hCLE9BQ0ssSUFBSWdELGNBQWNSLFFBQVEsQ0FBQyx5QkFBeUI7d0JBQ3ZEMUQsY0FBYzt3QkFDZEksaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO3dCQUN0Qk0sc0JBQXNCO29CQUN4QixPQUNLLElBQUlnRCxjQUFjUixRQUFRLENBQUMsdUJBQXVCO3dCQUNyRDFELGNBQWM7d0JBQ2RJLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjt3QkFDdEJNLHNCQUFzQjtvQkFDeEIsT0FDSyxJQUFJZ0QsY0FBY1IsUUFBUSxDQUFDLGNBQWM7d0JBQzVDMUQsY0FBYzt3QkFDZEksaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO3dCQUN0Qk0sc0JBQXNCO29CQUN4QixPQUFPLElBQUlnRCxjQUFjUixRQUFRLENBQUMsYUFBYTt3QkFDN0MxRCxjQUFjO3dCQUNkSSxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7d0JBQ3RCTSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSWdELGNBQWNSLFFBQVEsQ0FBQyxhQUFhO3dCQUM3QzFELGNBQWM7d0JBQ2RJLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjt3QkFDdEJNLHNCQUFzQjtvQkFDeEI7b0JBQ0EsbURBQW1EO29CQUNuRCw4QkFBOEI7b0JBQzlCLDZCQUE2QjtvQkFDN0IsaUNBQWlDO29CQUNqQywrQkFBK0I7b0JBQy9CLElBQUk7b0JBRUosK0JBQStCO29CQUMvQixJQUFJZ0QsY0FBY1IsUUFBUSxDQUFDLHFCQUFxQjt3QkFDOUMxRCxjQUFjLFdBQVc7d0JBQ3pCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUFPLElBQUlzRCxjQUFjUixRQUFRLENBQUMsb0JBQW9CO3dCQUNwRDFELGNBQWMsV0FBVzt3QkFDekJrQixzQkFBc0I7d0JBQ3RCZCxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSXNELGNBQWNSLFFBQVEsQ0FBQyxtQkFBbUI7d0JBQ25EMUQsY0FBYyxXQUFZO3dCQUMxQmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLHFCQUFxQjt3QkFDckQxRCxjQUFjLFdBQVc7d0JBQ3pCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUFPLElBQUlzRCxjQUFjUixRQUFRLENBQUMscUJBQXFCO3dCQUNyRDFELGNBQWMsV0FBVTt3QkFDeEJrQixzQkFBc0I7d0JBQ3RCZCxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSXNELGNBQWNSLFFBQVEsQ0FBQyxnQkFBZ0I7d0JBQ2hEMUQsY0FBYyxXQUFZO3dCQUMxQmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLFdBQVc7d0JBQzNDMUQsY0FBYyxXQUFZO3dCQUMxQmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLGtCQUFrQjt3QkFDbEQxRCxjQUFjLFdBQVc7d0JBQ3pCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUFPLElBQUlzRCxjQUFjUixRQUFRLENBQUMsY0FBYzt3QkFDOUMxRCxjQUFjLFdBQVc7d0JBQ3pCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUFPLElBQUlzRCxjQUFjUixRQUFRLENBQUMsWUFBWTt3QkFDNUMxRCxjQUFjLFdBQVk7d0JBQzFCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUNLLElBQUlzRCxjQUFjUixRQUFRLENBQUMsVUFBVTt3QkFDeEMxRCxjQUFjLFdBQVc7d0JBQ3pCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUFPLElBQUlzRCxjQUFjUixRQUFRLENBQUMsdUJBQXVCO3dCQUN2RDFELGNBQWMsV0FBVzt3QkFDekJrQixzQkFBc0I7d0JBQ3RCZCxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7b0JBQ3hCLE9BQU0sSUFBSXNELGNBQWNSLFFBQVEsQ0FBQywyQkFBMkI7d0JBQzFEMUQsY0FBYyxXQUFXO3dCQUN6QmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEIsT0FBTSxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLG9CQUFvQjt3QkFDbkQxRCxjQUFjLFdBQVc7d0JBQ3pCa0Isc0JBQXNCO3dCQUN0QmQsaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQkUsaUJBQWlCO3dCQUNqQkUscUJBQXFCO3dCQUNyQkUsc0JBQXNCO29CQUN4QixPQUFNLElBQUlzRCxjQUFjUixRQUFRLENBQUMsc0JBQXNCO3dCQUNyRDFELGNBQWMsV0FBVzt3QkFDekJrQixzQkFBc0I7d0JBQ3RCZCxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7b0JBQ3hCLE9BQU0sSUFBSXNELGNBQWNSLFFBQVEsQ0FBQyxVQUFVO3dCQUN6QzFELGNBQWMsV0FBVzt3QkFDekJrQixzQkFBc0I7d0JBQ3RCZCxpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCRSxpQkFBaUI7d0JBQ2pCRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7b0JBQ3hCLE9BQ0ssSUFBSXNELGNBQWNSLFFBQVEsQ0FBQyxrQkFBa0I7d0JBQ2hEMUQsY0FBYyxXQUFZO3dCQUMxQmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLFVBQVU7d0JBQzFDMUQsY0FBYyxXQUFXO3dCQUN6QmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEIsT0FBTyxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLGFBQWE7d0JBQzdDMUQsY0FBYyxXQUFXO3dCQUN6QmtCLHNCQUFzQjt3QkFDdEJkLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJFLGlCQUFpQjt3QkFDakJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjtvQkFDeEI7b0JBRUEsa0NBQWtDO29CQUNsQyxJQUFJc0QsY0FBY1IsUUFBUSxDQUFDLHNCQUFzQjt3QkFDL0MxRCxjQUFjLGNBQWU7d0JBQzdCSSxpQkFBaUI7d0JBQ2pCSSxpQkFBaUI7d0JBQ2pCRixvQkFBb0I7d0JBQ3BCWSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSWdELGNBQWNSLFFBQVEsQ0FBQyxzQkFBc0I7d0JBQ3REMUQsY0FBYyxjQUFlO3dCQUM3QkksaUJBQWlCO3dCQUNqQkksaUJBQWlCO3dCQUNqQkYsb0JBQW9CO3dCQUNwQlksc0JBQXNCO29CQUN4QixPQUFPLElBQUlnRCxjQUFjUixRQUFRLENBQUMsdUJBQXVCO3dCQUN2RDFELGNBQWMsY0FBYzt3QkFDNUJJLGlCQUFpQjt3QkFDakJJLGlCQUFpQjt3QkFDakJGLG9CQUFvQjt3QkFDcEJZLHNCQUFzQjtvQkFDeEI7b0JBRUEsa0NBQWtDO29CQUNsQyxJQUFJZ0QsY0FBY1IsUUFBUSxDQUFDLHFCQUFxQjt3QkFDOUMxRCxjQUFjLFdBQVc7d0JBQ3pCUSxpQkFBaUI7d0JBQ2pCSixpQkFBaUI7d0JBQ2pCRSxvQkFBb0I7d0JBQ3BCWSxzQkFBc0I7b0JBQ3hCLE9BQU8sSUFBSWdELGNBQWNSLFFBQVEsQ0FBQywwQkFBMEI7d0JBQzFEMUQsY0FBYyxXQUFXO3dCQUN6QlEsaUJBQWlCO3dCQUNqQkosaUJBQWlCO3dCQUNqQkUsb0JBQW9CO3dCQUNwQlksc0JBQXNCO29CQUN4QixPQUFPLElBQUlnRCxjQUFjUixRQUFRLENBQUMsbUJBQW1CO3dCQUNuRDFELGNBQWMsV0FBVzt3QkFDekJRLGlCQUFpQjt3QkFDakJKLGlCQUFpQjt3QkFDakJFLG9CQUFvQjt3QkFDcEJZLHNCQUFzQjtvQkFDeEI7Z0JBQ0Y7O1lBRUE4QyxzQkFBc0JuRDtRQUN4QjtpQ0FBRztRQUFDQTtLQUFTO0lBRWIscUJBQ0UsOERBQUN1RDtRQUFJQyxXQUFVO1FBQU9DLEtBQUtuRDtrQkFDekIsNEVBQUNyRCx5RUFBR0E7WUFDRnlHLE1BQUs7WUFDTEMsSUFBSTtnQkFBRUMsT0FBTztZQUFJO1lBQ2pCSixXQUFXLENBQUMsa0lBQWtJLEVBQzVJekUsZ0JBQWdCLGtCQUFrQixvQkFDbkMsaUJBQWlCLENBQUM7OzhCQUVuQiw4REFBQzhFO29CQUNDTCxXQUFVO29CQUNWTSxTQUFTLElBQU05RSxpQkFBaUI7OEJBRS9CRCwrQkFBaUIsOERBQUNsQixxRkFBUUE7d0JBQUMyRixXQUFVOzs7Ozs7Ozs7Ozs4QkFFeEMsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3RHLG1EQUFLQTs0QkFDSjZHLEtBQ0UvQixhQUFhZ0MsY0FBY0MsWUFDdkJqQyxhQUFhZ0MsY0FBY0MsWUFDM0IsR0FBR0Msa0RBQWlDLENBQUMsd0JBQXdCLENBQUM7NEJBRXBFRyxLQUFJOzRCQUNKYixXQUFVOzRCQUNWSSxPQUFPOzRCQUNQVSxRQUFROzRCQUNSQyxTQUFROzs7Ozs7c0NBR1YsOERBQUNySCxtREFBS0E7NEJBQ0o2RyxLQUFLLEdBQUdHLGtEQUFpQyxDQUFDLHNCQUFzQixDQUFDOzRCQUNqRUcsS0FBSTs0QkFDSlQsT0FBTzs0QkFDUFUsUUFBUTs0QkFDUkUsT0FBTTs0QkFDTmhCLFdBQVU7Ozs7Ozs7Ozs7Ozs4QkFJZCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVyxDQUFDLE9BQU8sRUFBRTFFLGNBQWMsU0FBUyxRQUFRO3NDQUN2RCw0RUFBQzJGO2dDQUFHakIsV0FBVTs7a0RBQ1osOERBQUNEO3dDQUNDQyxXQUFXLENBQUMseUVBQXlFLEVBQ25GdEUsZUFBZSxjQUNYLGdDQUNBLElBQ0o7a0RBRUYsNEVBQUN3Rjs0Q0FDQ2xCLFdBQVcsQ0FBQywwTUFBME0sRUFDcE50RSxlQUFlLGNBQWMsNEJBQTRCLElBQ3pEOzRDQUNGNEUsU0FBUztnREFBTzdDLGdCQUFnQjtnREFBY2hDLE9BQU80QyxJQUFJLENBQUM7NENBQXdCO3NEQUVsRiw0RUFBQzdFLGtEQUFJQTtnREFDSDJILE1BQUs7Z0RBQ0xuQixXQUFXLENBQUMsaUdBQWlHLEVBQzNHdEUsZUFBZSxjQUNYLDhCQUNBLElBQ0o7O2tFQUVGLDhEQUFDdEIsdUdBQWlCQTt3REFBQzRGLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSS9DLDhEQUFDRDt3Q0FDQ0MsV0FBVyxDQUFDLGlGQUFpRixFQUMzRnRFLGVBQWUsU0FBUyxpQ0FBaUMsSUFDekQ7a0RBRUYsNEVBQUN3Rjs0Q0FDQ2xCLFdBQVcsQ0FBQywwTUFBME0sRUFDcE50RSxlQUFlLFNBQVMsNEJBQTRCLElBQ3BEOzRDQUNGNEUsU0FBUztnREFBTzdDLGdCQUFnQjtnREFBU2hDLE9BQU80QyxJQUFJLENBQUM7NENBQTZCO3NEQUVsRiw0RUFBQzdFLGtEQUFJQTtnREFDSDJILE1BQUs7Z0RBQ0xuQixXQUFXLENBQUMsaUdBQWlHLEVBQzNHdEUsZUFBZSxTQUFTLDhCQUE4QixJQUN0RDs7a0VBRUYsOERBQUNqQix3R0FBU0E7d0RBQUN1RixXQUFVOzs7Ozs7b0RBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUl2Qyw4REFBQ0Q7d0NBQ0NDLFdBQVcsQ0FBQyxnRkFBZ0YsRUFDMUZ0RSxlQUFlLFlBQVksaUNBQWlDLElBQzVEO2tEQUVGLDRFQUFDd0Y7NENBQUdsQixXQUFVOzs4REFDWiw4REFBQ0s7b0RBQ0NDLFNBQVMsSUFBTXZFLGlCQUFpQixDQUFDRDtvREFDakNrRSxXQUFXLENBQUMsMkZBQTJGLEVBQ3JHdEUsZUFBZSxhQUFhSSxnQkFDeEIsMENBQ0EsNEZBQ0wsOERBQThELENBQUM7O3NFQUVoRSw4REFBQ25CLG1HQUFVQTs0REFBQ3FGLFdBQVU7Ozs7Ozt3REFBWTtzRUFDbEMsOERBQUNwRiwyR0FBbUJBOzREQUNsQm9GLFdBQVcsQ0FBQyx3Q0FBd0MsRUFDbERsRSxnQkFBZ0IsZUFBZSxJQUMvQjs7Ozs7Ozs7Ozs7O2dEQUdMQSwrQkFDQyw4REFBQ21GO29EQUFHakIsV0FBVTs7c0VBQ1osOERBQUNrQjtzRUFDQyw0RUFBQzFILGtEQUFJQTtnRUFDSDJILE1BQUs7Z0VBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLHFCQUNuQixpQ0FDQSxvRkFDSjtnRUFDRjBELFNBQVM7b0VBQ1AvQyx3QkFBd0I7b0VBQ3hCRSxnQkFBZ0I7Z0VBQ2xCOzBFQUNEOzs7Ozs7Ozs7OztzRUFJSCw4REFBQ3lEOzs4RUFDQyw4REFBQ2I7b0VBQ0NDLFNBQVMsSUFDUGpFLHFCQUFxQixDQUFDRDtvRUFFeEI0RCxXQUFXLENBQUMsaUVBQWlFLEVBQzNFdEUsZUFBZSxrQkFBa0JVLG9CQUM3QixpQ0FDQSxtRkFDTCxDQUFDLENBQUM7O3dFQUNKO3NGQUVDLDhEQUFDeEIsMkdBQW1CQTs0RUFDbEJvRixXQUFXLENBQUMsd0NBQXdDLEVBQ2xENUQsb0JBQW9CLGVBQWUsSUFDbkM7Ozs7Ozs7Ozs7OztnRUFHTEEsbUNBQ0MsOERBQUM2RTtvRUFBR2pCLFdBQVU7O3NGQUNaLDhEQUFDa0I7c0ZBQ0MsNEVBQUMxSCxrREFBSUE7Z0ZBQ0gySCxNQUFLO2dGQUNMbkIsV0FBVyxDQUFDLHFCQUFxQixFQUMvQnBELHVCQUF1QixXQUNuQixpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFBd0I7b0ZBQ3hCRSxnQkFBZ0I7Z0ZBQ2xCOzBGQUNEOzs7Ozs7Ozs7OztzRkFJSCw4REFBQ3lEO3NGQUNDLDRFQUFDMUgsa0RBQUlBO2dGQUNIMkgsTUFBSztnRkFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsc0JBQ25CLGlDQUNBLG9GQUNKO2dGQUNGMEQsU0FBUztvRkFDUC9DLHdCQUF3QjtvRkFDeEJFLGdCQUFnQjtnRkFDbEI7MEZBQ0Q7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDeUQ7c0ZBQ0MsNEVBQUMxSCxrREFBSUE7Z0ZBQ0gySCxNQUFLO2dGQUNMbkIsV0FBVyxDQUFDLHFCQUFxQixFQUMvQnBELHVCQUF1QixvQkFDbkIsaUNBQ0Esb0ZBQ0o7Z0ZBQ0YwRCxTQUFTO29GQUNQL0Msd0JBQXdCO29GQUN4QkUsZ0JBQWdCO2dGQUNsQjswRkFDRDs7Ozs7Ozs7Ozs7c0ZBSUgsOERBQUN5RDtzRkFDQyw0RUFBQzFILGtEQUFJQTtnRkFDSDJILE1BQUs7Z0ZBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLHVCQUNuQixpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFBd0I7b0ZBQ3hCRSxnQkFBZ0I7Z0ZBQ2xCOzBGQUNEOzs7Ozs7Ozs7OztzRkFLSCw4REFBQ3lEO3NGQUNDLDRFQUFDMUgsa0RBQUlBO2dGQUNIMkgsTUFBSztnRkFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFDQSw0Q0FDSSxpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFDRTtvRkFFRkUsZ0JBQ0U7Z0ZBRUo7MEZBQ0Q7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDeUQ7c0ZBQ0MsNEVBQUMxSCxrREFBSUE7Z0ZBQ0gySCxNQUFLO2dGQUNMbkIsV0FBVyxDQUFDLHFCQUFxQixFQUMvQnBELHVCQUF1QixVQUNuQixpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFBd0I7b0ZBQ3hCRSxnQkFBZ0I7Z0ZBQ2xCOzBGQUNEOzs7Ozs7Ozs7OztzRkFJSCw4REFBQ3lEO3NGQUNDLDRFQUFDMUgsa0RBQUlBO2dGQUNIMkgsTUFBSztnRkFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsaUJBQ25CLGlDQUNBLG9GQUNKO2dGQUNGMEQsU0FBUztvRkFDUC9DLHdCQUF3QjtvRkFDeEJFLGdCQUFnQjtnRkFDbEI7MEZBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQU9ULDhEQUFDeUQ7OzhFQUNDLDhEQUFDYjtvRUFDQ0MsU0FBUyxJQUNQL0Qsc0JBQXNCLENBQUNEO29FQUV6QjBELFdBQVcsQ0FBQyxnRUFBZ0UsRUFDMUV0RSxlQUFlLG1CQUFtQlkscUJBQzlCLGlDQUNBLG1GQUNMLENBQUMsQ0FBQzs7d0VBQ0o7c0ZBRUMsOERBQUMxQiwyR0FBbUJBOzRFQUNsQm9GLFdBQVcsQ0FBQyx3Q0FBd0MsRUFDbEQxRCxxQkFBcUIsZUFBZSxJQUNwQzs7Ozs7Ozs7Ozs7O2dFQUdMQSxvQ0FDQyw4REFBQzJFO29FQUFHakIsV0FBVTs7c0ZBQ1osOERBQUNrQjtzRkFDQyw0RUFBQzFILGtEQUFJQTtnRkFDSDJILE1BQUs7Z0ZBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLGFBQ25CLGlDQUNBLG9GQUNKO2dGQUNGMEQsU0FBUztvRkFDUC9DLHdCQUF3QjtvRkFDeEJFLGdCQUFnQjtnRkFDbEI7MEZBQ0Q7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDeUQ7c0ZBQ0MsNEVBQUMxSCxrREFBSUE7Z0ZBQ0gySCxNQUFLO2dGQUNMbkIsV0FBVyxDQUFDLHFCQUFxQixFQUMvQnBELHVCQUF1QixXQUNuQixpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFBd0I7b0ZBQ3hCRSxnQkFBZ0I7Z0ZBQ2xCOzBGQUNEOzs7Ozs7Ozs7OztzRkFJSCw4REFBQ3lEO3NGQUNDLDRFQUFDMUgsa0RBQUlBO2dGQUNIMkgsTUFBSztnRkFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsU0FDbkIsaUNBQ0Esb0ZBQ0o7Z0ZBQ0YwRCxTQUFTO29GQUNQL0Msd0JBQXdCO29GQUN4QkUsZ0JBQWdCO2dGQUNsQjswRkFDRDs7Ozs7Ozs7Ozs7c0ZBSUgsOERBQUN5RDtzRkFDQyw0RUFBQzFILGtEQUFJQTtnRkFDSDJILE1BQUs7Z0ZBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLHNCQUNuQixpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFBd0I7b0ZBQ3hCRSxnQkFBZ0I7Z0ZBQ2xCOzBGQUNEOzs7Ozs7Ozs7OztzRkFNSCw4REFBQ3lEO3NGQUNDLDRFQUFDMUgsa0RBQUlBO2dGQUNIMkgsTUFBSztnRkFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFDQSwwQkFDSSxpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFDRTtvRkFFRkUsZ0JBQ0U7Z0ZBRUo7MEZBQ0Q7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDeUQ7c0ZBQ0MsNEVBQUMxSCxrREFBSUE7Z0ZBQ0gySCxNQUFLO2dGQUNMbkIsV0FBVyxDQUFDLHFCQUFxQixFQUMvQnBELHVCQUF1QixtQkFDbkIsaUNBQ0Esb0ZBQ0o7Z0ZBQ0YwRCxTQUFTO29GQUNQL0Msd0JBQXdCO29GQUN4QkUsZ0JBQWdCO2dGQUNsQjswRkFDRDs7Ozs7Ozs7Ozs7c0ZBSUgsOERBQUN5RDtzRkFDQyw0RUFBQzFILGtEQUFJQTtnRkFDSDJILE1BQUs7Z0ZBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLHVCQUNuQixpQ0FDQSxvRkFDSjtnRkFDRjBELFNBQVM7b0ZBQ1AvQyx3QkFBd0I7b0ZBQ3hCRSxnQkFBZ0I7Z0ZBQ2xCOzBGQUNEOzs7Ozs7Ozs7OztzRkFLSCw4REFBQ3lEO3NGQUNDLDRFQUFDMUgsa0RBQUlBO2dGQUNIMkgsTUFBSztnRkFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsU0FDbkIsaUNBQ0Esb0ZBQ0o7Z0ZBQ0YwRCxTQUFTO29GQUNQL0Msd0JBQXdCO29GQUN4QkUsZ0JBQWdCO2dGQUNsQjswRkFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBUVQsOERBQUN5RDtzRUFDQyw0RUFBQzFILGtEQUFJQTtnRUFDSDJILE1BQUs7Z0VBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLGlCQUNuQixpQ0FDQSxvRkFDSjtnRUFDRjBELFNBQVM7b0VBQ1AvQyx3QkFBd0I7b0VBQ3hCRSxnQkFBZ0I7Z0VBQ2xCOzBFQUNEOzs7Ozs7Ozs7OztzRUFJSCw4REFBQ3lEO3NFQUNDLDRFQUFDMUgsa0RBQUlBO2dFQUNIMkgsTUFBSztnRUFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsU0FDbkIsaUNBQ0Esb0ZBQ0o7Z0VBQ0YwRCxTQUFTO29FQUNQL0Msd0JBQXdCO29FQUN4QkUsZ0JBQWdCO2dFQUNsQjswRUFDRDs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUN5RDtzRUFDQyw0RUFBQzFILGtEQUFJQTtnRUFDSDJILE1BQUs7Z0VBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLGVBQ25CLGlDQUNBLG9GQUNKO2dFQUNGMEQsU0FBUztvRUFDUC9DLHdCQUF3QixlQUN0QkUsZ0JBQWdCO2dFQUNwQjswRUFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFXWCw4REFBQ3NDO3dDQUNDQyxXQUFXLENBQUMsZ0ZBQWdGLEVBQzFGdEUsZUFBZSxZQUFZLGlDQUFpQyxJQUM1RDtrREFFRiw0RUFBQ3dGOzRDQUNDbEIsV0FBVyxDQUFDLDBNQUEwTSxFQUNwTnRFLGVBQWUsWUFBWSw0QkFBNEIsSUFDdkQ7NENBQ0Y0RSxTQUFTO2dEQUFPN0MsZ0JBQWdCO2dEQUFXaEMsT0FBTzRDLElBQUksQ0FBQzs0Q0FBZ0M7c0RBRXZGLDRFQUFDN0Usa0RBQUlBO2dEQUNIMkgsTUFBSztnREFDTG5CLFdBQVcsQ0FBQyxrR0FBa0csRUFDNUd0RSxlQUFlLFlBQ1gsOEJBQ0EsSUFDSjs7a0VBRUYsOERBQUNaLDJIQUFjQTt3REFBQ2tGLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTVDLDhEQUFDRDt3Q0FDQ0MsV0FBVyxDQUFDLGdGQUFnRixFQUMxRnRFLGVBQWUsV0FBVyxpQ0FBaUMsSUFDM0Q7a0RBRUYsNEVBQUN3Rjs0Q0FDQ2xCLFdBQVcsQ0FBQywwTUFBME0sRUFDcE50RSxlQUFlLFdBQVcsNEJBQTRCLElBQ3REOzRDQUNGNEUsU0FBUztnREFBTzdDLGdCQUFnQjtnREFBV2hDLE9BQU80QyxJQUFJLENBQUM7NENBQStCO3NEQUV0Riw0RUFBQzdFLGtEQUFJQTtnREFDSDJILE1BQUs7Z0RBQ0xuQixXQUFXLENBQUMsaUdBQWlHLEVBQzNHdEUsZUFBZSxXQUFXLDhCQUE4QixJQUN4RDs7a0VBRUYsOERBQUNoQixrR0FBU0E7d0RBQUNzRixXQUFVOzs7Ozs7b0RBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUt2Qyw4REFBQ0Q7d0NBQ0NDLFdBQVcsQ0FBQyxnRkFBZ0YsRUFDMUZ0RSxlQUFlLGtCQUNYLGlDQUNBLElBQ0o7a0RBRUYsNEVBQUN3Rjs0Q0FDQ2xCLFdBQVcsQ0FBQywwTUFBME0sRUFDcE50RSxlQUFlLGtCQUNYLDRCQUNBLElBQ0o7NENBQ0Y0RSxTQUFTO2dEQUFPN0MsZ0JBQWdCO2dEQUFrQmhDLE9BQU80QyxJQUFJLENBQUM7NENBQXNDO3NEQUVwRyw0RUFBQzdFLGtEQUFJQTtnREFDSDJILE1BQUs7Z0RBQ0xuQixXQUFXLENBQUMsaUdBQWlHLEVBQzNHdEUsZUFBZSxrQkFDWCw4QkFDQSxJQUNKOztrRUFFRiw4REFBQ1YsK0ZBQWFBO3dEQUFDZ0YsV0FBVTs7Ozs7O29EQUFZOzs7Ozs7Ozs7Ozs7Ozs7OztrREFJM0MsOERBQUNEO3dDQUNDQyxXQUFXLENBQUMsZ0ZBQWdGLEVBQzFGdEUsZUFBZSxpQkFDWCxpQ0FDQSxJQUNKO2tEQUVGLDRFQUFDd0Y7NENBQ0NsQixXQUFXLENBQUMsME1BQTBNLEVBQ3BOdEUsZUFBZSxpQkFDWCw0QkFDQSxJQUNKOzRDQUNGNEUsU0FBUztnREFBTzdDLGdCQUFnQjtnREFBaUJoQyxPQUFPNEMsSUFBSSxDQUFDOzRDQUFzQztzREFFbkcsNEVBQUM3RSxrREFBSUE7Z0RBQ0gySCxNQUFLO2dEQUNMbkIsV0FBVyxDQUFDLGlHQUFpRyxFQUMzR3RFLGVBQWUsaUJBQ1gsOEJBQ0EsSUFDSjs7a0VBRUYsOERBQUNiLHVIQUFVQTt3REFBQ21GLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3hDLDhEQUFDRDt3Q0FDQ0MsV0FBVyxDQUFDLGdGQUFnRixFQUMxRnRFLGVBQWUsYUFDWCxpQ0FDQSxJQUNKO2tEQUVGLDRFQUFDd0Y7NENBQ0NsQixXQUFXLENBQUMsME1BQTBNLEVBQ3BOdEUsZUFBZSxhQUFhLDRCQUE0QixJQUN4RDs0Q0FDRjRFLFNBQVM7Z0RBQU83QyxnQkFBZ0I7Z0RBQWFoQyxPQUFPNEMsSUFBSSxDQUFDOzRDQUFpQztzREFFMUYsNEVBQUM3RSxrREFBSUE7Z0RBQ0gySCxNQUFLO2dEQUNMbkIsV0FBVyxDQUFDLGlHQUFpRyxFQUMzR3RFLGVBQWUsYUFDWCw4QkFDQSxJQUNKOztrRUFFRiw4REFBQ1QsMkdBQW1CQTt3REFBQytFLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS2pELDhEQUFDRDt3Q0FDQ0MsV0FBVyxDQUFDLGdGQUFnRixFQUMxRnRFLGVBQWUsWUFBWSxpQ0FBaUMsSUFDNUQ7a0RBRUYsNEVBQUN3Rjs0Q0FDQ2xCLFdBQVcsQ0FBQywwTUFBME0sRUFDcE50RSxlQUFlLFlBQVksNEJBQTRCLElBQ3ZEOzRDQUNGNEUsU0FBUztnREFBTzdDLGdCQUFnQjtnREFBWWhDLE9BQU80QyxJQUFJLENBQUM7NENBQWdDO3NEQUV4Riw0RUFBQzdFLGtEQUFJQTtnREFDSDJILE1BQUs7Z0RBQ0xuQixXQUFXLENBQUMsaUdBQWlHLEVBQzNHdEUsZUFBZSxZQUNYLDhCQUNBLElBQ0o7O2tFQUVGLDhEQUFDUCx3R0FBU0E7d0RBQUM2RSxXQUFVOzs7Ozs7b0RBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU12Qyw4REFBQ0Q7d0NBQ0NDLFdBQVcsQ0FBQyxnRkFBZ0YsRUFDMUZ0RSxlQUFlLFlBQVksaUNBQWlDLElBQzVEO2tEQUVGLDRFQUFDd0Y7NENBQ0NsQixXQUFXLENBQUMsME1BQTBNLEVBQ3BOdEUsZUFBZSxZQUFZLDRCQUE0QixJQUN2RDs0Q0FDRjRFLFNBQVM7Z0RBQU83QyxnQkFBZ0I7Z0RBQVloQyxPQUFPNEMsSUFBSSxDQUFDOzRDQUFnQztzREFFeEYsNEVBQUM3RSxrREFBSUE7Z0RBQ0gySCxNQUFLO2dEQUNMbkIsV0FBVyxDQUFDLGlHQUFpRyxFQUMzR3RFLGVBQWUsWUFDWCw4QkFDQSxJQUNKOztrRUFFRiw4REFBQ2xCLDhHQUFlQTt3REFBQ3dGLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzdDLDhEQUFDRDt3Q0FDQ0MsV0FBVyxDQUFDLGdGQUFnRixFQUMxRnRFLGVBQWUsZUFDWCxpQ0FDQSxJQUNKO2tEQUVGLDRFQUFDd0Y7NENBQUdsQixXQUFVOzs4REFDWiw4REFBQ0s7b0RBQ0NDLFNBQVMsSUFBTXJFLG9CQUFvQixDQUFDRDtvREFDcENnRSxXQUFXLENBQUMsK0ZBQStGLEVBQ3pHdEUsZUFBZSxnQkFBZ0JNLG1CQUMzQiwwQ0FDQSw0RkFDTCw4REFBOEQsQ0FBQzs7c0VBRWhFLDhEQUFDakIsMkhBQWNBOzREQUFDaUYsV0FBVTs7Ozs7O3dEQUFZO3NFQUN0Qyw4REFBQ3BGLDJHQUFtQkE7NERBQ2xCb0YsV0FBVyxDQUFDLCtDQUErQyxFQUN6RGhFLG1CQUFtQixlQUFlLElBQ2xDOzs7Ozs7Ozs7Ozs7Z0RBR0xBLGtDQUNDLDhEQUFDaUY7b0RBQUdqQixXQUFVOztzRUFDWiw4REFBQ2tCO3NFQUNDLDRFQUFDMUgsa0RBQUlBO2dFQUNIMkgsTUFBSztnRUFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsVUFDbkIsaUNBQ0Esb0ZBQ0o7Z0VBQ0YwRCxTQUFTO29FQUNQL0Msd0JBQXdCO29FQUN4QkUsZ0JBQWdCO2dFQUNsQjswRUFDRDs7Ozs7Ozs7Ozs7c0VBS0gsOERBQUN5RDtzRUFDQyw0RUFBQzFILGtEQUFJQTtnRUFDSDJILE1BQUs7Z0VBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLGVBQ25CLGlDQUNBLG9GQUNKO2dFQUNGMEQsU0FBUztvRUFDUC9DLHdCQUF3QjtvRUFDeEJFLGdCQUFnQjtnRUFDbEI7MEVBQ0Q7Ozs7Ozs7Ozs7O3NFQUlILDhEQUFDeUQ7c0VBQ0MsNEVBQUMxSCxrREFBSUE7Z0VBQ0gySCxNQUFLO2dFQUNMbkIsV0FBVyxDQUFDLHFCQUFxQixFQUMvQnBELHVCQUF1QixXQUNuQixpQ0FDQSxvRkFDSjtnRUFDRjBELFNBQVM7b0VBQ1AvQyx3QkFBd0I7b0VBQ3hCRSxnQkFBZ0I7Z0VBQ2xCOzBFQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQTZCWCw4REFBQ3NDO3dDQUNDQyxXQUFXLENBQUMsZ0ZBQWdGLEVBQzFGdEUsZUFBZSxZQUFZLGlDQUFpQyxJQUM1RDtrREFFRiw0RUFBQ3dGOzRDQUFHbEIsV0FBVTs7OERBQ1osOERBQUNLO29EQUNDQyxTQUFTLElBQU1uRSxpQkFBaUIsQ0FBQ0Q7b0RBQ2pDOEQsV0FBVyxDQUFDLG1HQUFtRyxFQUM3R3RFLGVBQWUsYUFBYVEsZ0JBQ3hCLDBDQUNBLDRGQUNMLCtEQUErRCxDQUFDOztzRUFFakUsOERBQUNoQiwrR0FBZ0JBOzREQUFDOEUsV0FBVTs7Ozs7O3dEQUFZO3NFQUN4Qyw4REFBQ3BGLDJHQUFtQkE7NERBQ2xCb0YsV0FBVyxDQUFDLHdDQUF3QyxFQUNsRDlELGdCQUFnQixlQUFlLElBQy9COzs7Ozs7Ozs7Ozs7Z0RBR0xBLCtCQUNDLDhEQUFDK0U7b0RBQUdqQixXQUFVOztzRUFDWiw4REFBQ2tCO3NFQUNDLDRFQUFDMUgsa0RBQUlBO2dFQUNIMkgsTUFBSztnRUFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsYUFDbkIsaUNBQ0EscUZBQ0o7Z0VBQ0YwRCxTQUFTO29FQUNQL0Msd0JBQXdCO29FQUN4QkUsZ0JBQWdCO2dFQUNsQjswRUFDRDs7Ozs7Ozs7Ozs7c0VBS0gsOERBQUN5RDtzRUFDQyw0RUFBQzFILGtEQUFJQTtnRUFDSDJILE1BQUs7Z0VBQ0xuQixXQUFXLENBQUMscUJBQXFCLEVBQy9CcEQsdUJBQXVCLGlCQUNuQixpQ0FDQSxvRkFDSjtnRUFDRjBELFNBQVM7b0VBQ1AvQyx3QkFBd0I7b0VBQ3hCRSxnQkFBZ0I7Z0VBQ2xCOzBFQUNEOzs7Ozs7Ozs7OztzRUFJSCw4REFBQ3lEO3NFQUNDLDRFQUFDMUgsa0RBQUlBO2dFQUNIMkgsTUFBSztnRUFDTG5CLFdBQVcsQ0FBQyxxQkFBcUIsRUFDL0JwRCx1QkFBdUIsVUFDbkIsaUNBQ0Esb0ZBQ0o7Z0VBQ0YwRCxTQUFTO29FQUNQL0Msd0JBQXdCO29FQUN4QkUsZ0JBQWdCO2dFQUNsQjswRUFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVdmLDhEQUFDc0M7NEJBQUlDLFdBQVcsQ0FBQyxlQUFlLEVBQUUxRSxjQUFjLFNBQVMsUUFBUTs7OENBQy9ELDhEQUFDOEY7b0NBQUdwQixXQUFVOzs7Ozs7OENBQ2QsOERBQUNpQjtvQ0FBR2pCLFdBQVU7O3NEQUNaLDhEQUFDRDs0Q0FDQ0MsV0FBVyxDQUFDLGdGQUFnRixFQUMxRnRFLGVBQWUsVUFBVSxpQ0FBaUMsSUFDMUQ7c0RBRUYsNEVBQUN3RjtnREFDQ2xCLFdBQVcsQ0FBQywrTEFBK0wsRUFDek10RSxlQUFlLFVBQVUsNEJBQTRCLElBQ3JEOzBEQUdGLDRFQUFDcEIsc0RBQWFBOzs7Ozs7Ozs7Ozs7Ozs7c0RBSWxCLDhEQUFDeUY7NENBQ0NDLFdBQVcsQ0FBQyxnRkFBZ0YsRUFDMUZ0RSxlQUFlLGFBQ1gsaUNBQ0EsSUFDSjtzREFFRiw0RUFBQ3dGO2dEQUNDbEIsV0FBVyxDQUFDLGdNQUFnTSxFQUMxTTFFLGNBQWMsbUJBQW1CLGlCQUNqQzs7a0VBRUYsOERBQUNuQix5RkFBVUE7d0RBQUM2RixXQUFXLENBQUMsQ0FBQyxFQUFFMUUsY0FBYyxLQUFLLFdBQVc7Ozs7OztvREFBSztvREFDN0RBLGNBQWMsS0FBSzs7Ozs7Ozs7Ozs7O3NEQUl4Qiw4REFBQ3lFOzRDQUNDQyxXQUFXLENBQUMsaUZBQWlGLEVBQzNGdEUsZUFBZSxXQUFXLGlDQUFpQyxJQUMzRDtzREFFRiw0RUFBQ3dGO2dEQUNDbEIsV0FBVyxDQUFDLDZMQUE2TCxFQUN2TTFFLGNBQWMsbUJBQW1CLGlCQUNqQztnREFDRmdGLFNBQVN4Qzs7a0VBRVQsOERBQUN2RCxpR0FBY0E7d0RBQ2J5RixXQUFXLEdBQUcxRSxjQUFjLEtBQUssV0FBVzs7Ozs7O29EQUU3Q0EsY0FBYyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVN0QztBQUVBLGlFQUFlRCxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBPT0pBXFxNaXhkb3JtXFxNaXhkb3JtLVdlYi0yLjBcXGZyb250ZW5kXFxjb21wb25lbnRzXFxzdXBlcmFkbWluXFxTdXBlclNpZGViYXIuanN4Il0sInNvdXJjZXNDb250ZW50IjpbIiBcclxuIFxyXG5cInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcbmltcG9ydCB7IEJveCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xyXG5pbXBvcnQge1xyXG4gIGdldEl0ZW1Mb2NhbFN0b3JhZ2UsXHJcbiAgcmVtb3ZlSXRlbUxvY2FsU3RvcmFnZSxcclxufSBmcm9tIFwiQC91dGlscy9icm93c2VyU2V0dGluZ1wiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgcmVtb3ZlRmlyZWJhc2VUb2tlbiB9IGZyb20gXCJAL3NlcnZpY2VzL293bmVyZmxvd1NlcnZpY2VzXCI7XHJcbmltcG9ydCB7IHVzZUhlYWRlck93bmVyIH0gZnJvbSBcIi4uL293bmVyRmxvdy9oZWFkZXJDb250ZXhcIjtcclxuaW1wb3J0IHsgdXNlTmF2YmFyIH0gZnJvbSBcIi4uL2hvbWUvbmF2YmFyQ29udGV4dFwiO1xyXG5pbXBvcnQgeyAgRmlTZXR0aW5ncyB9IGZyb20gXCJyZWFjdC1pY29ucy9maVwiO1xyXG5pbXBvcnQgeyAgUGlTcXVhcmVzRm91ckJvbGQgfSBmcm9tIFwicmVhY3QtaWNvbnMvcGlcIjtcclxuaW1wb3J0IHsgUnhDcm9zczIgfSBmcm9tIFwicmVhY3QtaWNvbnMvcnhcIjtcclxuaW1wb3J0IFRoZW1lU3dpdGNoZXIgZnJvbSBcIi4vVGhlbWVTd2l0Y2hlclwiO1xyXG5pbXBvcnQgeyBCaUxvZ091dENpcmNsZSB9IGZyb20gXCJyZWFjdC1pY29ucy9iaVwiO1xyXG5pbXBvcnQgeyBGYVJlZ0NpcmNsZVVzZXIsIEZhUmVnVXNlciB9IGZyb20gXCJyZWFjdC1pY29ucy9mYTZcIjtcclxuaW1wb3J0IHsgIEdpS2V5Q2FyZCwgR2lOb3RlYm9vayB9IGZyb20gXCJyZWFjdC1pY29ucy9naVwiO1xyXG5pbXBvcnQgeyBSaUFycm93RHJvcERvd25MaW5lIH0gZnJvbSBcInJlYWN0LWljb25zL3JpXCI7XHJcbmltcG9ydCB7IEx1QmVsbFBsdXMsIEx1Q2FsZW5kYXJEYXlzLCBMdVVzZXJSb3VuZENvZyB9IGZyb20gXCJyZWFjdC1pY29ucy9sdVwiO1xyXG5pbXBvcnQgeyBUYkJyYW5kV2VjaGF0IH0gZnJvbSBcInJlYWN0LWljb25zL3RiXCI7XHJcbmltcG9ydCB7IE1kT3V0bGluZVJhdGVSZXZpZXcgfSBmcm9tIFwicmVhY3QtaWNvbnMvbWRcIjtcclxuaW1wb3J0IHsgRmFIYW5kSG9sZGluZ1VzZCwgRmFIZWFkc2V0IH0gZnJvbSBcInJlYWN0LWljb25zL2ZhXCI7XHJcbmltcG9ydCBcIkBmb250c291cmNlL3BvcHBpbnNcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiOyBcclxuXHJcbmNvbnN0IFN1cGVyU2lkZWJhciA9ICh7IGlzQ29sbGFwc2VkLCBpc1NpZGViYXJPcGVuLCBzZXRJc1NpZGViYXJPcGVuIH0pID0+IHtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBbYWN0aXZlSXRlbSwgc2V0QWN0aXZlSXRlbV0gPSB1c2VTdGF0ZShcIlwiKTsgLy8gU3RhdGUgZm9yIGFjdGl2ZSBtZW51IGl0ZW1cclxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcclxuICBjb25zdCBbZmxhZ3MsIHNldEZsYWdzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbaXNDb250ZW50T3Blbiwgc2V0SXNDb250ZW50T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzTWFuYWdlbWVudE9wZW4sIHNldElzTWFuYWdlbWVudE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc1BheW1lbnRPcGVuLCBzZXRJc1BheW1lbnRPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNMYW5kaW5nUGFnZU9wZW4sIHNldElzTGFuZGluZ1BhZ2VPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNPdGhlckNvbnRlbnRPcGVuLCBzZXRJc090aGVyQ29udGVudE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcclxuICBcclxuICBjb25zdCB7IHVwZGF0ZVVzZXJTdGF0dXMsIHVwZGF0ZVVzZXJSb2xlLCB1cGRhdGVIb3BJZCB9ID0gdXNlTmF2YmFyKCk7XHJcbiAgLy8gY29uc3QgW2FjdGl2ZUl0ZW0sIHNldEFjdGl2ZUl0ZW1dID0gdXNlU3RhdGUoeyBuYW1lOiBcIlwiLCB1cmw6IFwiXCIgfSk7XHJcbiAgY29uc3QgW2FjdGl2ZURyb3Bkb3duSXRlbSwgc2V0QWN0aXZlRHJvcGRvd25JdGVtXSA9IHVzZVN0YXRlKFwiXCIpOyAvLyBUcmFja3MgdGhlIGFjdGl2ZSBkcm9wZG93biBpdGVtXHJcblxyXG4gIGNvbnN0IHNpZGViYXJSZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZ1bmN0aW9uIGhhbmRsZUNsaWNrT3V0c2lkZShldmVudCkge1xyXG4gICAgICBpZiAoc2lkZWJhclJlZi5jdXJyZW50ICYmICFzaWRlYmFyUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0KSkge1xyXG4gICAgICAgIHNldElzU2lkZWJhck9wZW4oZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBpZiAoaXNTaWRlYmFyT3Blbikge1xyXG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwibW91c2Vkb3duXCIsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwibW91c2Vkb3duXCIsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwibW91c2Vkb3duXCIsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgICB9O1xyXG4gIH0sIFtpc1NpZGViYXJPcGVuXSk7XHJcblxyXG5cclxuICBjb25zdCBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayA9IChkcm9wZG93bkl0ZW0pID0+IHtcclxuICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShkcm9wZG93bkl0ZW0pO1xyXG4gICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIik7IC8vIEtlZXAgdGhlIHBhcmVudCBcIkNvbnRlbnRcIiBvcHRpb24gaGlnaGxpZ2h0ZWRcclxuICB9O1xyXG5cclxuICAvLyBjb25zdCBoYW5kbGVJdGVtQ2xpY2sgPSAoaXRlbSkgPT4ge1xyXG4gIC8vICAgc2V0QWN0aXZlSXRlbShpdGVtKTtcclxuICAvLyAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIlwiKTsgLy8gQ2xlYXIgZHJvcGRvd24gc2VsZWN0aW9uIGlmIGFub3RoZXIgbWFpbiBpdGVtIGlzIHNlbGVjdGVkXHJcbiAgLy8gfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSXRlbUNsaWNrID0gKG5hbWUpID0+IHtcclxuICAgIHNldEFjdGl2ZUl0ZW0obmFtZSk7XHJcbiAgICAvLyBzZXRBY3RpdmVJdGVtKHsgbmFtZTogaXRlbU5hbWUsIHVybDogaXRlbVVybCB9KS8vIFNldCB0aGUgYWN0aXZlIG1lbnUgaXRlbVxyXG4gICAgaWYgKG5hbWUgPT09IFwiTXVsdGlwbGUgUHJvcGVydHlcIikge1xyXG4gICAgICBoYW5kbGVPcGVuTXVsdGlwbGVQcm9wZXJ0eSgpO1xyXG4gICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJcIik7XHJcbiAgICB9XHJcbiAgICBpZiAod2luZG93LmlubmVyV2lkdGggPCA2NDApIHtcclxuICAgICAgc2V0SXNTaWRlYmFyT3BlbihmYWxzZSk7IC8vIENsb3NlIHNpZGViYXIgb24gbW9iaWxlIGFmdGVyIHNlbGVjdGluZ1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9IGFzeW5jICgpID0+IHtcclxuICAgIHJlbW92ZUl0ZW1Mb2NhbFN0b3JhZ2UoXCJ0b2tlblwiKTtcclxuICAgIHJlbW92ZUl0ZW1Mb2NhbFN0b3JhZ2UoXCJuYW1lXCIpO1xyXG4gICAgcmVtb3ZlSXRlbUxvY2FsU3RvcmFnZShcImlkXCIpO1xyXG4gICAgcmVtb3ZlSXRlbUxvY2FsU3RvcmFnZShcInJvbGVcIik7XHJcbiAgICByZW1vdmVJdGVtTG9jYWxTdG9yYWdlKFwiaG9waWRcIik7XHJcbiAgICByZW1vdmVJdGVtTG9jYWxTdG9yYWdlKFwiZW1haWxcIik7XHJcbiAgICByZW1vdmVJdGVtTG9jYWxTdG9yYWdlKFwiY29udGFjdFwiKTtcclxuICAgIHVwZGF0ZVVzZXJTdGF0dXMoXCJcIik7XHJcbiAgICB1cGRhdGVVc2VyUm9sZShcIlwiKTtcclxuICAgIHVwZGF0ZUhvcElkKFwiXCIpO1xyXG5cclxuICAgIGNvbnN0IHBheWxvYWQgPSB7XHJcbiAgICAgIHRva2VuOiBnZXRJdGVtTG9jYWxTdG9yYWdlKFwiRkNUXCIpLFxyXG4gICAgICB1c2VySWQ6IGdldEl0ZW1Mb2NhbFN0b3JhZ2UoXCJ1aWRcIiksXHJcbiAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IHJlbW92ZUZpcmViYXNlVG9rZW4ocGF5bG9hZCk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiRkNNIHRva2VuIHJlbW92ZWQgc3VjY2Vzc2Z1bGx5LlwiKTtcclxuICAgICAgcmVtb3ZlSXRlbUxvY2FsU3RvcmFnZShcIkZDVFwiKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciByZW1vdmluZyBGQ00gdG9rZW46XCIsIGVycm9yKTtcclxuICAgIH1cclxuICAgIHJlbW92ZUl0ZW1Mb2NhbFN0b3JhZ2UoXCJ1aWRcIik7XHJcbiAgICByb3V0ZXIucHVzaChcIi9vd25lci9sb2dpblwiKTtcclxuICB9O1xyXG5cclxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tdW51c2VkLXZhcnNcclxuICBjb25zdCBbb3Blbk11bHRpcGxlUHJvcGVydHksIHNldE9wZW5NdWx0aXBsZVByb3BlcnR5XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBoYW5kbGVPcGVuTXVsdGlwbGVQcm9wZXJ0eSA9ICgpID0+IHNldE9wZW5NdWx0aXBsZVByb3BlcnR5KHRydWUpO1xyXG5cclxuICBjb25zdCB7IHByb2ZpbGVEYXRhLCBwcm9wZXJ0eURhdGEgfSA9IHVzZUhlYWRlck93bmVyKCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaEZsYWdzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goXCJodHRwczovL3Jlc3Rjb3VudHJpZXMuY29tL3YzLjEvYWxsXCIpO1xyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcblxyXG4gICAgICAgIGNvbnN0IGZpbHRlcmVkRmxhZ3MgPSBkYXRhXHJcbiAgICAgICAgICAuZmlsdGVyKChjb3VudHJ5KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNvbW1vbk5hbWUgPSBjb3VudHJ5Lm5hbWUuY29tbW9uO1xyXG4gICAgICAgICAgICByZXR1cm4gcHJvcGVydHlEYXRhPy5hZGRyZXNzPy5jb3VudHJ5Py5pbmNsdWRlcyhjb21tb25OYW1lKTtcclxuICAgICAgICAgIH0pXHJcbiAgICAgICAgICAubWFwKChjb3VudHJ5KSA9PiAoe1xyXG4gICAgICAgICAgICBpZDogY291bnRyeS5jY2EzLFxyXG4gICAgICAgICAgICBpbWc6IGNvdW50cnkuZmxhZ3Muc3ZnLFxyXG4gICAgICAgICAgICBuYW1lOiBjb3VudHJ5Lm5hbWUuY29tbW9uLFxyXG4gICAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICBzZXRGbGFncyhmaWx0ZXJlZEZsYWdzKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgZmxhZ3M6XCIsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBpZiAocHJvcGVydHlEYXRhPy5hZGRyZXNzPy5jb3VudHJ5KSB7XHJcbiAgICAgIGZldGNoRmxhZ3MoKTtcclxuICAgIH1cclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBzZXRBY3RpdmVJdGVtRnJvbVBhdGggPSAocGF0aCkgPT4ge1xyXG4gICAgICBjb25zdCBkYXNoYm9hcmRQYXRoID0gcGF0aC5zcGxpdChcIi9kYXNoYm9hcmRcIilbMV07XHJcblxyXG4gICAgICBpZiAoIWRhc2hib2FyZFBhdGggfHwgZGFzaGJvYXJkUGF0aCA9PT0gXCIvXCIpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiRGFzaGJvYXJkXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIlwiKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3VzZXJcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiVXNlclwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJcIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9ib29raW5nXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkJvb2tpbmdcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiXCIpO1xyXG4gICAgICB9IGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvaG9zdGVsXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkhvc3RlbFwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJcIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9jb21tdW5pY2F0aW9uXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbW11bmljYXRpb25cIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiXCIpO1xyXG4gICAgICB9IGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvbm90aWZpY2F0aW9uXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIk5vdGlmaWNhdGlvblwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJcIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9hbm5vdW5jZW1lbnRcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiTm90aWZpY2F0aW9uXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIlwiKTtcclxuICAgICAgfSBcclxuICAgICAgZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9jcmVhdGUtbm90aWZpY2F0aW9uXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIk5vdGlmaWNhdGlvblwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJcIik7XHJcbiAgICAgIH1cclxuICAgICAgZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9idWxrLW5vdGlmaWNhdGlvblwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJOb3RpZmljYXRpb25cIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiXCIpO1xyXG4gICAgICB9XHJcbiAgICAgIGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvZmVlZGJhY2tcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiRmVlZGJhY2tcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiXCIpO1xyXG4gICAgICB9IGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvc3VwcG9ydFwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJTdXBwb3J0XCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIlwiKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3Byb2ZpbGVcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiUHJvZmlsZVwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJcIik7XHJcbiAgICAgIH1cclxuICAgICAgLy8gfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3BheW1lbnRcIikpIHtcclxuICAgICAgLy8gICBzZXRBY3RpdmVJdGVtKFwiUGF5bWVudFwiKTtcclxuICAgICAgLy8gICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgLy8gICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgIC8vICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiXCIpO1xyXG4gICAgICAvLyB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgQ29udGVudCBzdWJtZW51IGl0ZW1zXHJcbiAgICAgIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL21vYmlsZS1ob21lcGFnZVwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsIFwiTW9iaWxlIEhvbWUgUGFnZVwiKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJNb2JpbGUgSG9tZSBQYWdlXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9jb250ZW50LWJhbm5lclwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsIFwiQmFubmVyXCIpO1xyXG4gICAgICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIkJhbm5lclwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9leHBsb3JlLXdvcmxkXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIiAsIFwiRXhwbG9yZSB0aGUgV29ybGRcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiRXhwbG9yZSB0aGUgV29ybGRcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICB9IGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvZmVhdHVyZWQtaG9zdGVsXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIiwgXCJGZWF0dXJlZCBIb3N0ZWxcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiRmVhdHVyZWQgSG9zdGVsXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3RyYXZlbC1hY3Rpdml0eVwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsXCJUcmF2ZWwgYnkgQWN0aXZpdHlcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiVHJhdmVsIGJ5IEFjdGl2aXR5XCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3NwbGl0LWJpbGxcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiQ29udGVudFwiICwgXCJTcGxpdCBiaWxsIGZhaXJmYXJlLCBNaXggbWF0ZSwgTWl4IHJpZGVcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiU3BsaXQgYmlsbCBmYWlyZmFyZSwgTWl4IG1hdGUsIE1peCByaWRlXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL2V2ZW50XCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIiAsIFwiRXZlbnRcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiRXZlbnRcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICB9IGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvY29udGVudC1ibG9nXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIiwgXCJMYW5kaW5nLUJsb2dcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiTGFuZGluZy1CbG9nXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL2Fib3V0LXVzXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIiwgXCJBYm91dCBVc1wiKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJBYm91dCBVc1wiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9hd2FyZHNcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiQ29udGVudFwiICwgXCJBd2FyZHNcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiQXdhcmRzXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgfVxyXG4gICAgICBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL2hlbHBcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiQ29udGVudFwiLCBcIkhlbHBcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiSGVscFwiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9ib29raW5nLWd1YXJhbnRlZVwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsIFwiQm9va2luZyBHdWFyYW50ZWVcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiQm9va2luZyBHdWFyYW50ZWVcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICB9ZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9ib29raW5nLXJlZnVuZC1wb2xpY3lcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiQ29udGVudFwiLCBcIkJvb2tpbmcgUmVmdW5kIFBvbGljeVwiKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJCb29raW5nIFJlZnVuZCBQb2xpY3lcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICB9ZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9wcml2YWN5LXBvbGljeVwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsIFwiUHJpdmFjeSBQb2xpY3lcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiUHJpdmFjeSBQb2xpY3lcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICB9ZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi90ZXJtcyZjb25kaXRpb25zXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIkNvbnRlbnRcIiwgXCJUZXJtcyAmIENvbmRpdGlvbnNcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiVGVybXMgJiBDb25kaXRpb25zXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgfWVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvZmFxc1wiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsIFwiRmFxc1wiKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJGYXFzXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgfVxyXG4gICAgICBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL29mZmVyLWJhbm5lclwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIgLCBcIk9mZmVyIEJhbm5lclwiKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJPZmZlciBCYW5uZXJcIik7XHJcbiAgICAgICAgc2V0SXNDb250ZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNPdGhlckNvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL2Jsb2dcIikpIHtcclxuICAgICAgICBzZXRBY3RpdmVJdGVtKFwiQ29udGVudFwiLCBcIkJsb2dcIik7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiQmxvZ1wiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTGFuZGluZ1BhZ2VPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICB9IGVsc2UgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvY29udGFjdFwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJDb250ZW50XCIsIFwiQ29udGFjdCBVc1wiKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJDb250YWN0IFVzXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0SXNNYW5hZ2VtZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3BlbihmYWxzZSk7XHJcbiAgICAgICAgc2V0SXNMYW5kaW5nUGFnZU9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzT3RoZXJDb250ZW50T3BlbihmYWxzZSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEhhbmRsZSBNYW5hZ2VtZW50IHN1Ym1lbnUgaXRlbXNcclxuICAgICAgaWYgKGRhc2hib2FyZFBhdGguaW5jbHVkZXMoXCIvbWFuYWdlbWVudC1hZG1pblwiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJNYW5hZ2VtZW50XCIgLCBcIkFkbWluXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiQWRtaW5cIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9tYW5hZ2VtZW50LWF1ZGl0XCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIk1hbmFnZW1lbnRcIiAsIFwiQXVkaXQgTG9nc1wiKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIkF1ZGl0IExvZ3NcIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9tYW5hZ2VtZW50LXN0YXR1c1wiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJNYW5hZ2VtZW50XCIsIFwiU3RhdHVzXCIpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzUGF5bWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4odHJ1ZSk7XHJcbiAgICAgICAgc2V0QWN0aXZlRHJvcGRvd25JdGVtKFwiU3RhdHVzXCIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBIYW5kbGUgTWFuYWdlbWVudCBzdWJtZW51IGl0ZW1zXHJcbiAgICAgIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3BheW1lbnQtYm9va2luZ1wiKSkge1xyXG4gICAgICAgIHNldEFjdGl2ZUl0ZW0oXCJQYXltZW50XCIsIFwiQm9va2luZ3NcIik7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJCb29raW5nc1wiKTtcclxuICAgICAgfSBlbHNlIGlmIChkYXNoYm9hcmRQYXRoLmluY2x1ZGVzKFwiL3BheW1lbnQtc3Vic2NyaXB0aW9uXCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIlBheW1lbnRcIiwgXCJTdWJzY3JpcHRpb25cIik7XHJcbiAgICAgICAgc2V0SXNQYXltZW50T3Blbih0cnVlKTtcclxuICAgICAgICBzZXRJc0NvbnRlbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRJc01hbmFnZW1lbnRPcGVuKGZhbHNlKTtcclxuICAgICAgICBzZXRBY3RpdmVEcm9wZG93bkl0ZW0oXCJTdWJzY3JpcHRpb25cIik7XHJcbiAgICAgIH0gZWxzZSBpZiAoZGFzaGJvYXJkUGF0aC5pbmNsdWRlcyhcIi9wYXltZW50LWV2ZW50XCIpKSB7XHJcbiAgICAgICAgc2V0QWN0aXZlSXRlbShcIlBheW1lbnRcIiwgXCJFdmVudFwiKTtcclxuICAgICAgICBzZXRJc1BheW1lbnRPcGVuKHRydWUpO1xyXG4gICAgICAgIHNldElzQ29udGVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldElzTWFuYWdlbWVudE9wZW4oZmFsc2UpO1xyXG4gICAgICAgIHNldEFjdGl2ZURyb3Bkb3duSXRlbShcIkV2ZW50XCIpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHNldEFjdGl2ZUl0ZW1Gcm9tUGF0aChwYXRobmFtZSk7XHJcbiAgfSwgW3BhdGhuYW1lXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhcIiByZWY9e3NpZGViYXJSZWZ9PlxyXG4gICAgICA8Qm94XHJcbiAgICAgICAgcm9sZT1cInByZXNlbnRhdGlvblwiXHJcbiAgICAgICAgc3g9e3sgd2lkdGg6IDMyNyB9fVxyXG4gICAgICAgIGNsYXNzTmFtZT17YGJnLXdoaXRlIGRhcms6YmctWyMwRDBEMERdIHctWzI0MHB4XSAgZml4ZWQgei0xMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgaC1bY2FsYygxMDB2aCldIG92ZXJmbG93LXktYXV0byBzY3JvbGxiYXItaGlkZSAke1xyXG4gICAgICAgICAgaXNTaWRlYmFyT3BlbiA/IFwidHJhbnNsYXRlLXgtMFwiIDogXCItdHJhbnNsYXRlLXgtZnVsbFwiXHJcbiAgICAgICAgfSBzbTp0cmFuc2xhdGUteC0wYH1cclxuICAgICAgPlxyXG4gICAgICAgIDxidXR0b25cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIHRvcC00IGxlZnQtNCB6LTIwIHAtMiByb3VuZGVkLW1kIHNtOmhpZGRlblwiXHJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1NpZGViYXJPcGVuKGZhbHNlKX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7aXNTaWRlYmFyT3BlbiAmJiA8UnhDcm9zczIgY2xhc3NOYW1lPVwiYmctdHJhbnNwYXJlbnQgdGV4dC1ibGFjayBkYXJrOnRleHQtWyNCNkI2QjZdXCIgLz59XHJcbiAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3LWZ1bGwgZ2FwLXgtMiBoLVs3MHB4XSBcIj5cclxuICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICBzcmM9e1xyXG4gICAgICAgICAgICAgIHByb2ZpbGVEYXRhPy5wcm9maWxlSW1hZ2U/Lm9iamVjdFVSTFxyXG4gICAgICAgICAgICAgICAgPyBwcm9maWxlRGF0YT8ucHJvZmlsZUltYWdlPy5vYmplY3RVUkxcclxuICAgICAgICAgICAgICAgIDogYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUzNfVVJMX0ZFfS9mcm9udC1pbWFnZXMvYXZhdGFyLnBuZ2BcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBhbHQ9XCJQcm9maWxlIFBpY1wiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtbGcgdy1bNDBweF0gaC1bNDBweF1cIlxyXG4gICAgICAgICAgICB3aWR0aD17NDB9XHJcbiAgICAgICAgICAgIGhlaWdodD17NDB9XHJcbiAgICAgICAgICAgIGxvYWRpbmc9XCJsYXp5XCJcclxuICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgIHNyYz17YCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfUzNfVVJMX0ZFfS9mcm9udC1pbWFnZXMvbG9nby5wbmdgfVxyXG4gICAgICAgICAgICBhbHQ9XCJNaXhkb3JtXCJcclxuICAgICAgICAgICAgd2lkdGg9ezE0MH1cclxuICAgICAgICAgICAgaGVpZ2h0PXs1MH1cclxuICAgICAgICAgICAgdGl0bGU9XCJNaXhkb3JtXCJcclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwib2JqZWN0LWNvbnRhaW4gdy1maXQgaC1maXQgbWF4LXctMjAgbWF4LWgtMTEgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGp1c3RpZnktYmV0d2VlbiBoLVsxMDAwcHhdXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctZnVsbCAke2lzQ29sbGFwc2VkID8gXCJweC0wXCIgOiBcInB4LTBcIn1gfT5cclxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXNtICB0ZXh0LWJsYWNrLTEwMCBmbGV4IGZsZXgtY29sIGgtYXV0byBcIj5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IGJnLXdoaXRlIGRhcms6YmctWyMwRDBEMERdIGhvdmVyOmJvcmRlci1sLTQgIGhvdmVyOmJvcmRlci1bIzUwQzJGRl0gJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJEYXNoYm9hcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCJib3JkZXItbC00IGJvcmRlci1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGN1cnNvci1wb2ludGVyIHRleHQtc20gZm9udC1wb3BwaW5zIGgtWzQ4cHhdIGZsZXggdGV4dC1ibGFjayByb3VuZGVkLWxnIHB4LTUgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSBtdC0wLjUgaG92ZXI6Zm9udC1zZW1pYm9sZCAke1xyXG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiRGFzaGJvYXJkXCIgPyBcImJnLVsjNTBDMkZGXSB0ZXh0LXdoaXRlXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7aGFuZGxlSXRlbUNsaWNrKFwiRGFzaGJvYXJkXCIpOyByb3V0ZXIucHVzaChcIi9zdXBlcmFkbWluL2Rhc2hib2FyZFwiKX19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gcHgtMyBweS0yIHJvdW5kZWQtbWQgICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIkRhc2hib2FyZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8UGlTcXVhcmVzRm91ckJvbGQgY2xhc3NOYW1lPVwidGV4dC14bFwiIC8+IERhc2hib2FyZFxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTYgYmctd2hpdGUgZGFyazpiZy1bIzBEMEQwRF0gIGhvdmVyOmJvcmRlci1sLTQgbXQtMC41ICBob3Zlcjpib3JkZXItWyM1MEMyRkZdICR7XHJcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiVXNlclwiID8gXCJib3JkZXItbC00IGJvcmRlci1bIzUwQzJGRl0gXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8bGlcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmb250LXBvcHBpbnMgaC1bNDhweF0gZmxleCB0ZXh0LWJsYWNrIHJvdW5kZWQtbGcgcHgtNSBob3ZlcjpiZy1bIzUwQzJGRl0gaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1IG10LTAuNSBob3Zlcjpmb250LXNlbWlib2xkICR7XHJcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJVc2VyXCIgPyBcImJnLVsjNTBDMkZGXSB0ZXh0LXdoaXRlXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7aGFuZGxlSXRlbUNsaWNrKFwiVXNlclwiKTsgcm91dGVyLnB1c2goXCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvdXNlclwiKX19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC91c2VyXCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBweC0zIHB5LTIgcm91bmRlZC1tZCAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiVXNlclwiID8gXCIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8RmFSZWdVc2VyIGNsYXNzTmFtZT1cInRleHQteGxcIiAvPiBVc2VyXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIkNvbnRlbnRcIiA/IFwiYm9yZGVyLWwtNCBib3JkZXItWyM1MEMyRkZdIFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm10LTAuNSAgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0NvbnRlbnRPcGVuKCFpc0NvbnRlbnRPcGVuKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBjdXJzb3ItcG9pbnRlciBmb250LXBvcHBpbnMgdGV4dC1zbSBoLVs0OHB4XSBwbC04IHByLTEwIHJvdW5kZWQtbGcgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiQ29udGVudFwiIHx8IGlzQ29udGVudE9wZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLVsjNTBDMkZGXSB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOmJnLVsjNTBDMkZGXSBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8R2lOb3RlYm9vayBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gQ29udGVudFxyXG4gICAgICAgICAgICAgICAgICAgIDxSaUFycm93RHJvcERvd25MaW5lXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmbG9hdC1lbmQgdHJhbnNpdGlvbi10cmFuc2Zvcm0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXNDb250ZW50T3BlbiA/IFwicm90YXRlLTE4MFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIHtpc0NvbnRlbnRPcGVuICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibXQtMiBzcGFjZS15LTQgbWwtMTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL21vYmlsZS1ob21lcGFnZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJNb2JpbGUgSG9tZSBQYWdlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiTW9iaWxlIEhvbWUgUGFnZVwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUl0ZW1DbGljayhcIk1vYmlsZSBIb21lIFBhZ2VcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE1vYmlsZSBIb21lIFBhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJc0xhbmRpbmdQYWdlT3BlbighaXNMYW5kaW5nUGFnZU9wZW4pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyIHRleHQtc20gZm9udC1wb3BwaW5zIHJvdW5kZWQtbGcgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiTGFuZGluZyBQYWdlXCIgfHwgaXNMYW5kaW5nUGFnZU9wZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0gYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIExhbmRpbmcgUGFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxSaUFycm93RHJvcERvd25MaW5lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBmbG9hdC1lbmQgdHJhbnNpdGlvbi10cmFuc2Zvcm0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNMYW5kaW5nUGFnZU9wZW4gPyBcInJvdGF0ZS0xODBcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNMYW5kaW5nUGFnZU9wZW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJtdC0yIG1sLTQgc3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9jb250ZW50LWJhbm5lclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJCYW5uZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6dGV4dC1bIzUwQzJGRl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJCYW5uZXJcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJCYW5uZXJcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEJhbm5lclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvZXhwbG9yZS13b3JsZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJFeHBsb3JlIHRoZSBXb3JsZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIkV4cGxvcmUgdGhlIFdvcmxkXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiRXhwbG9yZSB0aGUgV29ybGRcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEV4cGxvcmUgdGhlIFdvcmxkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9mZWF0dXJlZC1ob3N0ZWxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiRmVhdHVyZWQgSG9zdGVsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiRmVhdHVyZWQgSG9zdGVsXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiRmVhdHVyZWQgSG9zdGVsXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBGZWF0dXJlZCBIb3N0ZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL3RyYXZlbC1hY3Rpdml0eVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJUcmF2ZWwgYnkgQWN0aXZpdHlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6dGV4dC1bIzUwQzJGRl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJUcmF2ZWwgYnkgQWN0aXZpdHlcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJUcmF2ZWwgYnkgQWN0aXZpdHlcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyYXZlbCBieSBBY3Rpdml0eVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL3NwbGl0LWJpbGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIlNwbGl0IGJpbGwgZmFpcmZhcmUsIE1peCBtYXRlLCBNaXggcmlkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJTcGxpdCBiaWxsIGZhaXJmYXJlLCBNaXggbWF0ZSwgTWl4IHJpZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUl0ZW1DbGljayhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJTcGxpdCBiaWxsIGZhaXJmYXJlLCBNaXggbWF0ZSwgTWl4IHJpZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU3BsaXQgYmlsbCBmYWlyZmFyZSwgTWl4IG1hdGUsIE1peCByaWRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9ldmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJFdmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIkV2ZW50XCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiRXZlbnRcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEV2ZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9jb250ZW50LWJsb2dcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiTGFuZGluZy1CbG9nXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiTGFuZGluZy1CbG9nXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiTGFuZGluZy1CbG9nXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBMYW5kaW5nLUJsb2dcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJc090aGVyQ29udGVudE9wZW4oIWlzT3RoZXJDb250ZW50T3BlbilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXJjdXJzb3ItcG9pbnRlciB0ZXh0LXNtIGZvbnQtcG9wcGlucyByb3VuZGVkLWxnICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIk90aGVyIENvbnRlbnRcIiB8fCBpc090aGVyQ29udGVudE9wZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0gYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE90aGVyIENvbnRlbnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8UmlBcnJvd0Ryb3BEb3duTGluZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC0yeGwgZmxvYXQtZW5kIHRyYW5zaXRpb24tdHJhbnNmb3JtICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzT3RoZXJDb250ZW50T3BlbiA/IFwicm90YXRlLTE4MFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc090aGVyQ29udGVudE9wZW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJtdC0yIG1sLTQgc3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9hYm91dC11c1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJBYm91dCBVc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIkFib3V0IFVzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiQWJvdXQgVXNcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFib3V0IFVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9hd2FyZHNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiQXdhcmRzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiQXdhcmRzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiQXdhcmRzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBd2FyZHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL2hlbHBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiSGVscFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIkhlbHBcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJIZWxwXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBIZWxwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9ib29raW5nLWd1YXJhbnRlZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJCb29raW5nIEd1YXJhbnRlZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIkJvb2tpbmcgR3VhcmFudGVlXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiQm9va2luZyBHdWFyYW50ZWVcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEJvb2tpbmcgR3VhcmFudGVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvYm9va2luZy1yZWZ1bmQtcG9saWN5XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtcG9wcGlucyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd25JdGVtID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJCb29raW5nIFJlZnVuZCBQb2xpY3lcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6dGV4dC1bIzUwQzJGRl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiQm9va2luZyBSZWZ1bmQgUG9saWN5XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiQm9va2luZyBSZWZ1bmQgUG9saWN5XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEJvb2tpbmcgUmVmdW5kIFBvbGljeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvcHJpdmFjeS1wb2xpY3lcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiUHJpdmFjeSBQb2xpY3lcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6dGV4dC1bIzUwQzJGRl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJQcml2YWN5IFBvbGljeVwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUl0ZW1DbGljayhcIlByaXZhY3kgUG9saWN5XCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBQcml2YWN5IFBvbGljeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvdGVybXMmY29uZGl0aW9uc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJUZXJtcyAmIENvbmRpdGlvbnNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6dGV4dC1bIzUwQzJGRl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJUZXJtcyAmIENvbmRpdGlvbnNcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJUZXJtcyAmIENvbmRpdGlvbnNcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRlcm1zICYgQ29uZGl0aW9uc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL2ZhcXNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiRmFxc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIkZhcXNcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJGYXFzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBGYXFzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvb2ZmZXItYmFubmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtcG9wcGlucyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd25JdGVtID09PSBcIk9mZmVyIEJhbm5lclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LVsjNTBDMkZGXSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOmhvdmVyOnRleHQtWyM1MEMyRkZdXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEcm9wZG93bkl0ZW1DbGljayhcIk9mZmVyIEJhbm5lclwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUl0ZW1DbGljayhcIk9mZmVyIEJhbm5lclwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgT2ZmZXIgQmFubmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9ibG9nXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtcG9wcGlucyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd25JdGVtID09PSBcIkJsb2dcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6dGV4dC1bIzUwQzJGRl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJCbG9nXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiQmxvZ1wiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgQmxvZ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvY29udGFjdFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJDb250YWN0IFVzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiQ29udGFjdCBVc1wiKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiQ29udGFjdCBVc1wiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ29udGFjdCBVc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICBcclxuXHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIkJvb2tpbmdcIiA/IFwiYm9yZGVyLWwtNCBib3JkZXItWyM1MEMyRkZdIFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGN1cnNvci1wb2ludGVyIHRleHQtc20gZm9udC1wb3BwaW5zIGgtWzQ4cHhdIGZsZXggdGV4dC1ibGFjayByb3VuZGVkLWxnIHB4LTUgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgbXQtMC41IGhvdmVyOmZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xyXG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiQm9va2luZ1wiID8gXCJiZy1bIzUwQzJGRl0gdGV4dC13aGl0ZVwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge2hhbmRsZUl0ZW1DbGljayhcIkJvb2tpbmdcIik7cm91dGVyLnB1c2goXCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvYm9va2luZ1wiKX19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9ib29raW5nXCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtZ3JheS03MDAgIGRhcms6dGV4dC1bI2ZmZmZmZl0gcHgtMyBweS0yIHJvdW5kZWQtbWQgICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIkJvb2tpbmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEx1Q2FsZW5kYXJEYXlzIGNsYXNzTmFtZT1cInRleHQteGxcIiAvPiBCb29raW5nXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIkhvc3RlbFwiID8gXCJib3JkZXItbC00IGJvcmRlci1bIzUwQzJGRl0gXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8bGlcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmb250LXBvcHBpbnMgaC1bNDhweF0gZmxleCB0ZXh0LWJsYWNrIHJvdW5kZWQtbGcgcHgtNSBob3ZlcjpiZy1bIzUwQzJGRl0gaG92ZXI6dGV4dC13aGl0ZSBtdC0wLjUgaG92ZXI6Zm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJIb3N0ZWxcIiA/IFwiYmctWyM1MEMyRkZdIHRleHQtd2hpdGVcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtoYW5kbGVJdGVtQ2xpY2soXCJIb3N0ZWxcIik7IHJvdXRlci5wdXNoKFwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL2hvc3RlbFwiKX19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9ob3N0ZWxcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOnRleHQtd2hpdGUgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtWyNmZmZmZmZdIHB4LTMgcHktMiByb3VuZGVkLW1kICAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJIb3N0ZWxcIiA/IFwiIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEdpS2V5Q2FyZCBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gSG9zdGVsXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTYgYmctd2hpdGUgZGFyazpiZy1bIzBEMEQwRF0gaG92ZXI6Ym9yZGVyLWwtNCBtdC0wLjUgIGhvdmVyOmJvcmRlci1bIzUwQzJGRl0gJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJDb21tdW5pY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICA/IFwiYm9yZGVyLWwtNCBib3JkZXItWyM1MEMyRkZdIFwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8bGlcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmb250LXBvcHBpbnMgaC1bNDhweF0gZmxleCB0ZXh0LWJsYWNrIHJvdW5kZWQtbGcgcHgtNSBob3ZlcjpiZy1bIzUwQzJGRl0gaG92ZXI6dGV4dC13aGl0ZSBtdC0wLjUgaG92ZXI6Zm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJDb21tdW5pY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1bIzUwQzJGRl0gdGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtoYW5kbGVJdGVtQ2xpY2soXCJDb21tdW5pY2F0aW9uXCIpOyByb3V0ZXIucHVzaChcIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9jb21tdW5pY2F0aW9uXCIpfX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL2NvbW11bmljYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOnRleHQtd2hpdGUgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtWyNmZmZmZmZdIHB4LTMgcHktMiByb3VuZGVkLW1kICAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJDb21tdW5pY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcIiB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUYkJyYW5kV2VjaGF0IGNsYXNzTmFtZT1cInRleHQteGxcIiAvPiBDb21tdW5pY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIk5vdGlmaWNhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcImJvcmRlci1sLTQgYm9yZGVyLVsjNTBDMkZGXSBcIlxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGN1cnNvci1wb2ludGVyIHRleHQtc20gZm9udC1wb3BwaW5zIGgtWzQ4cHhdIGZsZXggdGV4dC1ibGFjayByb3VuZGVkLWxnIHB4LTUgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgbXQtMC41IGhvdmVyOmZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xyXG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiTm90aWZpY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1bIzUwQzJGRl0gdGV4dC13aGl0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtoYW5kbGVJdGVtQ2xpY2soXCJOb3RpZmljYXRpb25cIik7IHJvdXRlci5wdXNoKFwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL25vdGlmaWNhdGlvbnNcIil9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvbm90aWZpY2F0aW9uc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gcHgtMyBweS0yIHJvdW5kZWQtbWQgICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIk5vdGlmaWNhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8THVCZWxsUGx1cyBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gTm90aWZpY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTYgYmctd2hpdGUgZGFyazpiZy1bIzBEMEQwRF0gaG92ZXI6Ym9yZGVyLWwtNCBtdC0wLjUgIGhvdmVyOmJvcmRlci1bIzUwQzJGRl0gJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJGZWVkYmFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcImJvcmRlci1sLTQgYm9yZGVyLVsjNTBDMkZGXSBcIlxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGN1cnNvci1wb2ludGVyIHRleHQtc20gZm9udC1wb3BwaW5zIGgtWzQ4cHhdIGZsZXggdGV4dC1ibGFjayByb3VuZGVkLWxnIHB4LTUgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgbXQtMC41IGhvdmVyOmZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xyXG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiRmVlZGJhY2tcIiA/IFwiYmctWyM1MEMyRkZdIHRleHQtd2hpdGVcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtoYW5kbGVJdGVtQ2xpY2soXCJGZWVkYmFja1wiKTsgcm91dGVyLnB1c2goXCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvZmVlZGJhY2tcIil9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvZmVlZGJhY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOnRleHQtd2hpdGUgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtWyNmZmZmZmZdIHB4LTMgcHktMiByb3VuZGVkLW1kICAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJGZWVkYmFja1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8TWRPdXRsaW5lUmF0ZVJldmlldyBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gRmVlZGJhY2tcclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIlN1cHBvcnRcIiA/IFwiYm9yZGVyLWwtNCBib3JkZXItWyM1MEMyRkZdIFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGN1cnNvci1wb2ludGVyIHRleHQtc20gZm9udC1wb3BwaW5zIGgtWzQ4cHhdIGZsZXggdGV4dC1ibGFjayByb3VuZGVkLWxnIHB4LTUgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgbXQtMC41IGhvdmVyOmZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xyXG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiU3VwcG9ydFwiID8gXCJiZy1bIzUwQzJGRl0gdGV4dC13aGl0ZVwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge2hhbmRsZUl0ZW1DbGljayhcIlN1cHBvcnRcIik7IHJvdXRlci5wdXNoKFwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL3N1cHBvcnRcIil9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvc3VwcG9ydFwiXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgaG92ZXI6dGV4dC13aGl0ZSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gcHgtMyBweS0yIHJvdW5kZWQtbWQgICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIlN1cHBvcnRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEZhSGVhZHNldCBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gU3VwcG9ydFxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIFxyXG5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IGJnLXdoaXRlIGRhcms6YmctWyMwRDBEMERdIGhvdmVyOmJvcmRlci1sLTQgbXQtMC41ICBob3Zlcjpib3JkZXItWyM1MEMyRkZdICR7XHJcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiUHJvZmlsZVwiID8gXCJib3JkZXItbC00IGJvcmRlci1bIzUwQzJGRl0gXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8bGlcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmb250LXBvcHBpbnMgaC1bNDhweF0gZmxleCB0ZXh0LWJsYWNrIHJvdW5kZWQtbGcgcHgtNSBob3ZlcjpiZy1bIzUwQzJGRl0gaG92ZXI6dGV4dC13aGl0ZSBtdC0wLjUgaG92ZXI6Zm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1ICR7XHJcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJQcm9maWxlXCIgPyBcImJnLVsjNTBDMkZGXSB0ZXh0LXdoaXRlXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7aGFuZGxlSXRlbUNsaWNrKFwiUHJvZmlsZVwiKTsgcm91dGVyLnB1c2goXCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvcHJvZmlsZVwiKX19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9wcm9maWxlXCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBob3Zlcjp0ZXh0LXdoaXRlIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBweC0zIHB5LTIgcm91bmRlZC1tZCAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiUHJvZmlsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCIgdGV4dC13aGl0ZSBmb250LXNlbWlib2xkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8RmFSZWdDaXJjbGVVc2VyIGNsYXNzTmFtZT1cInRleHQteGxcIiAvPiBQcm9maWxlXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgXHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIk1hbmFnZW1lbnRcIlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCJib3JkZXItbC00IGJvcmRlci1bIzUwQzJGRl0gXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiXCJcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtdC0wLjUgIHRyYW5zaXRpb24gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IHRyYW5zZm9ybVwiPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNYW5hZ2VtZW50T3BlbighaXNNYW5hZ2VtZW50T3Blbil9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmb250LXBvcHBpbnMgaC1bNDhweF0gcm91bmRlZC1sZyBwci0xIHBsLVszMnB4XSAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJNYW5hZ2VtZW50XCIgfHwgaXNNYW5hZ2VtZW50T3BlblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYmctWyM1MEMyRkZdIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Zm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgfSB0cmFuc2l0aW9uIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1YH1cclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMdVVzZXJSb3VuZENvZyBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gTWFuYWdlbWVudFxyXG4gICAgICAgICAgICAgICAgICAgIDxSaUFycm93RHJvcERvd25MaW5lXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LTJ4bCBtdC0wLjUgZmxvYXQtZW5kIHRyYW5zaXRpb24tdHJhbnNmb3JtICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzTWFuYWdlbWVudE9wZW4gPyBcInJvdGF0ZS0xODBcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICB7aXNNYW5hZ2VtZW50T3BlbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm10LTIgbWwtMTQgc3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9tYW5hZ2VtZW50LWFkbWluXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtcG9wcGlucyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd25JdGVtID09PSBcIkFkbWluXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl0gaG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiQWRtaW5cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJBZG1pblwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgQWRtaW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9tYW5hZ2VtZW50LWF1ZGl0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtcG9wcGlucyAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlRHJvcGRvd25JdGVtID09PSBcIkF1ZGl0IExvZ3NcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6dGV4dC1bI2ZmZmZmZl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJBdWRpdCBMb2dzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiQXVkaXQgTG9nc1wiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgQXVkaXQgTG9nc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGxpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc3VwZXJhZG1pbi9kYXNoYm9hcmQvbWFuYWdlbWVudC1zdGF0dXNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiU3RhdHVzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtWyM1MEMyRkZdIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOnRleHQtWyNmZmZmZmZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiU3RhdHVzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlSXRlbUNsaWNrKFwiU3RhdHVzXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBTdGF0dXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIDxkaXZcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTYgYmctd2hpdGUgaG92ZXI6Ym9yZGVyLWwtNCBtdC0wLjUgIGhvdmVyOmJvcmRlci1bIzUwQzJGRl0gJHtcclxuICAgICAgICAgICAgICAgICAgYWN0aXZlSXRlbSA9PT0gXCJQYXltZW50XCIgPyBcImJvcmRlci1sLTQgYm9yZGVyLVsjNTBDMkZGXSBcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxsaVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BjdXJzb3ItcG9pbnRlciB0ZXh0LXNtIGgtWzQ4cHhdIGZsZXggdGV4dC1ibGFjayByb3VuZGVkLWxnIHB4LTUgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgbXQtMC41IGhvdmVyOmZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSAke1xyXG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiUGF5bWVudFwiID8gXCJiZy1bIzUwQzJGRl0gdGV4dC13aGl0ZVwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlSXRlbUNsaWNrKFwiUGF5bWVudFwiKX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL3N1cHBvcnRcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGhvdmVyOnRleHQtd2hpdGUgdGV4dC1ncmF5LTcwMCBweC0zIHB5LTIgcm91bmRlZC1tZFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8RmFIYW5kSG9sZGluZ1VzZCBjbGFzc05hbWU9XCJ0ZXh0LXhsXCIgLz4gUGF5bWVudFxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PiAqL31cclxuXHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBiZy13aGl0ZSBkYXJrOmJnLVsjMEQwRDBEXSBob3Zlcjpib3JkZXItbC00IG10LTAuNSAgaG92ZXI6Ym9yZGVyLVsjNTBDMkZGXSAke1xyXG4gICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIlBheW1lbnRcIiA/IFwiYm9yZGVyLWwtNCBib3JkZXItWyM1MEMyRkZdIFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm10LTAuNSBtbC0xLjUgIHRyYW5zaXRpb24gZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IHRyYW5zZm9ybVwiPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNQYXltZW50T3BlbighaXNQYXltZW50T3Blbil9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgIGdhcC0xLjUgY3Vyc29yLXBvaW50ZXIgdGV4dC1zbSBmb250LXBvcHBpbnMgaC1bNDhweF0gcm91bmRlZC1sZyBwci04ICBwbC1bMjZweF0gJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiUGF5bWVudFwiIHx8IGlzUGF5bWVudE9wZW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLVsjNTBDMkZGXSB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1bIzUwQzJGRl0gaG92ZXI6dGV4dC13aGl0ZSBob3Zlcjpmb250LXNlbWlib2xkIGRhcms6dGV4dC1bI2ZmZmZmZl1cIlxyXG4gICAgICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSBgfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPEZhSGFuZEhvbGRpbmdVc2QgY2xhc3NOYW1lPVwidGV4dC14bFwiIC8+IFBheW1lbnRcclxuICAgICAgICAgICAgICAgICAgICA8UmlBcnJvd0Ryb3BEb3duTGluZVxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC0yeGwgZmxvYXQtZW5kIHRyYW5zaXRpb24tdHJhbnNmb3JtICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzUGF5bWVudE9wZW4gPyBcInJvdGF0ZS0xODBcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICB7aXNQYXltZW50T3BlbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm10LTIgbWwtMTQgc3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9wYXltZW50LWJvb2tpbmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiQm9va2luZ3NcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwICBob3Zlcjp0ZXh0LVsjNTBDMkZGXSBkYXJrOnRleHQtWyNmZmZmZmZdIGRhcms6aG92ZXI6dGV4dC1bIzUwQzJGRl1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duSXRlbUNsaWNrKFwiQm9va2luZ3NcIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJCb29raW5nc1wiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgQm9va2luZ3NcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9saT5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zdXBlcmFkbWluL2Rhc2hib2FyZC9wYXltZW50LXN1YnNjcmlwdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXBvcHBpbnMgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duSXRlbSA9PT0gXCJTdWJzY3JpcHRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6dGV4dC1bI2ZmZmZmZl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJTdWJzY3JpcHRpb25cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVJdGVtQ2xpY2soXCJTdWJzY3JpcHRpb25cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFN1YnNjcmlwdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGxpID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3N1cGVyYWRtaW4vZGFzaGJvYXJkL3BheW1lbnQtZXZlbnRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1wb3BwaW5zICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93bkl0ZW0gPT09IFwiRXZlbnRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1bIzUwQzJGRl0gZm9udC1zZW1pYm9sZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtWyM1MEMyRkZdIGRhcms6dGV4dC1bI2ZmZmZmZl0gZGFyazpob3Zlcjp0ZXh0LVsjNTBDMkZGXVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRHJvcGRvd25JdGVtQ2xpY2soXCJFdmVudFwiKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUl0ZW1DbGljayhcIkV2ZW50XCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBFdmVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtdC1hdXRvIHctZnVsbCAke2lzQ29sbGFwc2VkID8gXCJweC0wXCIgOiBcInB4LTBcIn1gfT5cclxuICAgICAgICAgICAgPGhyIGNsYXNzTmFtZT1cImJvcmRlci0xLjUgcC0wXCIgLz5cclxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LXNtIGZvbnQtcG9wcGlucyAgZmxleCBmbGV4LWNvbCBoLVsxNTBweF0gbXQtMTAgbWQ6bXQtNSBcIj5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IGJnLXdoaXRlIGRhcms6YmctWyMwRDBEMERdIGhvdmVyOmJvcmRlci1sLTQgbXQtMC41ICBob3Zlcjpib3JkZXItWyM1MEMyRkZdICR7XHJcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiVGhlbWVcIiA/IFwiYm9yZGVyLWwtNCBib3JkZXItWyM1MEMyRkZdIFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPGxpXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTcgY3Vyc29yLXBvaW50ZXIgZmlyc3Q6bXQtMCB0ZXh0LXNtIGZvbnQtcG9wcGlucyBoLVs0OHB4XSAgZ2FwLXgtMiB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1bI2ZmZmZmZl0gIHJvdW5kZWQtbGcgaG92ZXI6YmctWyM1MEMyRkZdIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Zm9udC1zZW1pYm9sZCAgJHtcclxuICAgICAgICAgICAgICAgICAgICBhY3RpdmVJdGVtID09PSBcIlRoZW1lXCIgPyBcImJnLVsjNTBDMkZGXSB0ZXh0LXdoaXRlXCIgOiBcIlwiXHJcbiAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFRoZW1lU3dpdGNoZXIgIC8+XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IGJnLXdoaXRlIGRhcms6YmctWyMwRDBEMERdIGhvdmVyOmJvcmRlci1sLTQgbXQtMC41ICBob3Zlcjpib3JkZXItWyM1MEMyRkZdICR7XHJcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiU2V0dGluZ3NcIlxyXG4gICAgICAgICAgICAgICAgICAgID8gXCJib3JkZXItbC00IGJvcmRlci1bIzUwQzJGRl0gXCJcclxuICAgICAgICAgICAgICAgICAgICA6IFwiXCJcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxsaVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC03IGN1cnNvci1wb2ludGVyIGZpcnN0Om10LTAgIHRleHQtc20gZm9udC1wb3BwaW5zIHJvdW5kZWQtbGcgaC1bNDhweF0gIGdhcC14LTIgIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LVsjZmZmZmZmXSBob3ZlcjpiZy1bIzUwQzJGRl0gaG92ZXI6Zm9udC1zZW1pYm9sZCBob3Zlcjp0ZXh0LXdoaXRlICAke1xyXG4gICAgICAgICAgICAgICAgICAgIGlzQ29sbGFwc2VkID8gXCJqdXN0aWZ5LWNlbnRlclwiIDogXCJqdXN0aWZ5LXN0YXJ0XCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxGaVNldHRpbmdzIGNsYXNzTmFtZT17YCAke2lzQ29sbGFwc2VkID8gXCJcIiA6IFwidGV4dC14bFwifWB9IC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICB7aXNDb2xsYXBzZWQgPyBcIlwiIDogXCJTZXR0aW5ncyBcIn1cclxuICAgICAgICAgICAgICAgIDwvbGk+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTYgYmctd2hpdGUgIGRhcms6YmctWyMwRDBEMERdIGhvdmVyOmJvcmRlci1sLTQgbXQtMC41ICBob3Zlcjpib3JkZXItWyM1MEMyRkZdICR7XHJcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZUl0ZW0gPT09IFwiTG9nb3V0XCIgPyBcImJvcmRlci1sLTQgYm9yZGVyLVsjNTBDMkZGXSBcIiA6IFwiXCJcclxuICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxsaVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BjdXJzb3ItcG9pbnRlciB0ZXh0LXNtIGZvbnQtcG9wcGlucyBoLVs0OHB4XSAgcHgtNyBmbGV4IGl0ZW1zLWNlbnRlciByb3VuZGVkLWxnIGdhcC14LTIgdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtWyNmZmZmZmZdIGhvdmVyOmJnLVsjNTBDMkZGXSBob3Zlcjpmb250LXNlbWlib2xkIGhvdmVyOnRleHQtd2hpdGUgIGZpcnN0Om10LTAke1xyXG4gICAgICAgICAgICAgICAgICAgIGlzQ29sbGFwc2VkID8gXCJqdXN0aWZ5LWNlbnRlclwiIDogXCJqdXN0aWZ5LXN0YXJ0XCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUxvZ291dH1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPEJpTG9nT3V0Q2lyY2xlXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtpc0NvbGxhcHNlZCA/IFwiXCIgOiBcInRleHQteGxcIn1gfVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICB7aXNDb2xsYXBzZWQgPyBcIlwiIDogXCJMb2dvdXQgXCJ9XHJcbiAgICAgICAgICAgICAgICA8L2xpPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQm94PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFN1cGVyU2lkZWJhcjtcclxuIl0sIm5hbWVzIjpbInVzZVBhdGhuYW1lIiwiTGluayIsIkJveCIsIkltYWdlIiwiZ2V0SXRlbUxvY2FsU3RvcmFnZSIsInJlbW92ZUl0ZW1Mb2NhbFN0b3JhZ2UiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VTdGF0ZSIsInJlbW92ZUZpcmViYXNlVG9rZW4iLCJ1c2VIZWFkZXJPd25lciIsInVzZU5hdmJhciIsIkZpU2V0dGluZ3MiLCJQaVNxdWFyZXNGb3VyQm9sZCIsIlJ4Q3Jvc3MyIiwiVGhlbWVTd2l0Y2hlciIsIkJpTG9nT3V0Q2lyY2xlIiwiRmFSZWdDaXJjbGVVc2VyIiwiRmFSZWdVc2VyIiwiR2lLZXlDYXJkIiwiR2lOb3RlYm9vayIsIlJpQXJyb3dEcm9wRG93bkxpbmUiLCJMdUJlbGxQbHVzIiwiTHVDYWxlbmRhckRheXMiLCJMdVVzZXJSb3VuZENvZyIsIlRiQnJhbmRXZWNoYXQiLCJNZE91dGxpbmVSYXRlUmV2aWV3IiwiRmFIYW5kSG9sZGluZ1VzZCIsIkZhSGVhZHNldCIsInVzZVJvdXRlciIsIlN1cGVyU2lkZWJhciIsImlzQ29sbGFwc2VkIiwiaXNTaWRlYmFyT3BlbiIsInNldElzU2lkZWJhck9wZW4iLCJyb3V0ZXIiLCJhY3RpdmVJdGVtIiwic2V0QWN0aXZlSXRlbSIsImZsYWdzIiwic2V0RmxhZ3MiLCJpc0NvbnRlbnRPcGVuIiwic2V0SXNDb250ZW50T3BlbiIsImlzTWFuYWdlbWVudE9wZW4iLCJzZXRJc01hbmFnZW1lbnRPcGVuIiwiaXNQYXltZW50T3BlbiIsInNldElzUGF5bWVudE9wZW4iLCJpc0xhbmRpbmdQYWdlT3BlbiIsInNldElzTGFuZGluZ1BhZ2VPcGVuIiwiaXNPdGhlckNvbnRlbnRPcGVuIiwic2V0SXNPdGhlckNvbnRlbnRPcGVuIiwicGF0aG5hbWUiLCJ1cGRhdGVVc2VyU3RhdHVzIiwidXBkYXRlVXNlclJvbGUiLCJ1cGRhdGVIb3BJZCIsImFjdGl2ZURyb3Bkb3duSXRlbSIsInNldEFjdGl2ZURyb3Bkb3duSXRlbSIsInNpZGViYXJSZWYiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVEcm9wZG93bkl0ZW1DbGljayIsImRyb3Bkb3duSXRlbSIsImhhbmRsZUl0ZW1DbGljayIsIm5hbWUiLCJoYW5kbGVPcGVuTXVsdGlwbGVQcm9wZXJ0eSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJoYW5kbGVMb2dvdXQiLCJwYXlsb2FkIiwidG9rZW4iLCJ1c2VySWQiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJwdXNoIiwib3Blbk11bHRpcGxlUHJvcGVydHkiLCJzZXRPcGVuTXVsdGlwbGVQcm9wZXJ0eSIsInByb2ZpbGVEYXRhIiwicHJvcGVydHlEYXRhIiwiZmV0Y2hGbGFncyIsInJlc3BvbnNlIiwiZmV0Y2giLCJkYXRhIiwianNvbiIsImZpbHRlcmVkRmxhZ3MiLCJmaWx0ZXIiLCJjb3VudHJ5IiwiY29tbW9uTmFtZSIsImNvbW1vbiIsImFkZHJlc3MiLCJpbmNsdWRlcyIsIm1hcCIsImlkIiwiY2NhMyIsImltZyIsInN2ZyIsInNldEFjdGl2ZUl0ZW1Gcm9tUGF0aCIsInBhdGgiLCJkYXNoYm9hcmRQYXRoIiwic3BsaXQiLCJkaXYiLCJjbGFzc05hbWUiLCJyZWYiLCJyb2xlIiwic3giLCJ3aWR0aCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcmMiLCJwcm9maWxlSW1hZ2UiLCJvYmplY3RVUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfUzNfVVJMX0ZFIiwiYWx0IiwiaGVpZ2h0IiwibG9hZGluZyIsInRpdGxlIiwidWwiLCJsaSIsImhyZWYiLCJociJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/superadmin/SuperSidebar.jsx\n");

/***/ }),

/***/ "./components/superadmin/ThemeSwitcher.jsx":
/*!*************************************************!*\
  !*** ./components/superadmin/ThemeSwitcher.jsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeSwitcher)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_IoSunnyOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=IoSunnyOutline!=!react-icons/io5 */ \"__barrel_optimize__?names=IoSunnyOutline!=!./node_modules/react-icons/io5/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BsMoonStars_react_icons_bs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BsMoonStars!=!react-icons/bs */ \"__barrel_optimize__?names=BsMoonStars!=!./node_modules/react-icons/bs/index.mjs\");\n// \"use client\";\n// import { Moon, Sun } from \"lucide-react\";\n// import { useTheme } from \"next-themes\";\n// import { useEffect, useState } from \"react\";\n// import { BsMoonStars } from \"react-icons/bs\";\n// import { IoSunnyOutline } from \"react-icons/io5\";\n// const ThemeSwitcher = ({ isCollapsed }) => {\n//   const { theme, setTheme } = useTheme();\n//   const [mounted, setMounted] = useState(false);\n//   useEffect(() => {\n//     setMounted(true);\n//   }, []);\n//   if (!mounted) return null;\n//   return (\n//     <button\n//       className={`flex items-center justify-center text-sm ${\n//         isCollapsed ? \"\" : \"gap-x-2\"\n//       } `}\n//       onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n//     >\n//       {theme === \"light\" ? <IoSunnyOutline  size={18} /> : <BsMoonStars  size={18} />}\n//       {isCollapsed ? \"\" : \" Theme\"}\n//     </button>\n//   );\n// };\n// export default ThemeSwitcher;\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ThemeSwitcher() {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    // eslint-disable-next-line no-unused-vars\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeSwitcher.useEffect\": ()=>{\n            // Get theme from localStorage or default to light\n            const storedTheme = localStorage.getItem(\"theme\") || \"light\";\n            setTheme(storedTheme);\n            document.documentElement.classList.toggle(\"dark\", storedTheme === \"dark\");\n        }\n    }[\"ThemeSwitcher.useEffect\"], []);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        document.documentElement.classList.toggle(\"dark\", newTheme === \"dark\");\n        localStorage.setItem(\"theme\", newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${activeItem === \"Theme\" ? \"border-l-4 border-[#50C2FF] \" : \"\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: toggleTheme,\n            className: `flex items-center justify-center text-sm font-poppins gap-x-2  cursor-pointer  ${activeItem === \"Theme\" ? \"bg-[#50C2FF] text-white\" : \"\"}`,\n            children: [\n                theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoSunnyOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_2__.IoSunnyOutline, {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 28\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BsMoonStars_react_icons_bs__WEBPACK_IMPORTED_MODULE_3__.BsMoonStars, {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 58\n                }, this),\n                \"Theme\"\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\superadmin\\\\ThemeSwitcher.jsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/superadmin/ThemeSwitcher.jsx\n");

/***/ }),

/***/ "__barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BellRing,Check,ChevronDown,Globe,Menu,Search!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=BiLogOutCircle!=!./node_modules/react-icons/bi/index.mjs":
/*!******************************************************************************************!*\
  !*** __barrel_optimize__?names=BiLogOutCircle!=!./node_modules/react-icons/bi/index.mjs ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/bi/index.mjs */ "./node_modules/react-icons/bi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=Box!=!./node_modules/@mui/material/node/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=Box!=!./node_modules/@mui/material/node/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=BsMoonStars!=!./node_modules/react-icons/bs/index.mjs":
/*!***************************************************************************************!*\
  !*** __barrel_optimize__?names=BsMoonStars!=!./node_modules/react-icons/bs/index.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bs_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/bs/index.mjs */ "./node_modules/react-icons/bs/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bs_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_bs_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!./node_modules/react-icons/fa/index.mjs":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=FaHandHoldingUsd,FaHeadset!=!./node_modules/react-icons/fa/index.mjs ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaKey,FaUser!=!./node_modules/react-icons/fa6/index.mjs":
/*!*****************************************************************************************!*\
  !*** __barrel_optimize__?names=FaKey,FaUser!=!./node_modules/react-icons/fa6/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!./node_modules/react-icons/fa6/index.mjs":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=FaRegCircleUser,FaRegUser!=!./node_modules/react-icons/fa6/index.mjs ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiSettings!=!./node_modules/react-icons/fi/index.mjs":
/*!**************************************************************************************!*\
  !*** __barrel_optimize__?names=FiSettings!=!./node_modules/react-icons/fi/index.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.mjs */ "./node_modules/react-icons/fi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=GiKeyCard,GiNotebook!=!./node_modules/react-icons/gi/index.mjs":
/*!************************************************************************************************!*\
  !*** __barrel_optimize__?names=GiKeyCard,GiNotebook!=!./node_modules/react-icons/gi/index.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/gi/index.mjs */ "./node_modules/react-icons/gi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_gi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=IoSunnyOutline!=!./node_modules/react-icons/io5/index.mjs":
/*!*******************************************************************************************!*\
  !*** __barrel_optimize__?names=IoSunnyOutline!=!./node_modules/react-icons/io5/index.mjs ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/io5/index.mjs */ "./node_modules/react-icons/io5/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_io5_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!./node_modules/react-icons/lu/index.mjs":
/*!********************************************************************************************************************!*\
  !*** __barrel_optimize__?names=LuBellPlus,LuCalendarDays,LuUserRoundCog!=!./node_modules/react-icons/lu/index.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_lu_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/lu/index.mjs */ "./node_modules/react-icons/lu/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_lu_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_lu_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=MdManageAccounts!=!./node_modules/react-icons/md/index.mjs":
/*!********************************************************************************************!*\
  !*** __barrel_optimize__?names=MdManageAccounts!=!./node_modules/react-icons/md/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=MdOutlineRateReview!=!./node_modules/react-icons/md/index.mjs":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=MdOutlineRateReview!=!./node_modules/react-icons/md/index.mjs ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/md/index.mjs */ "./node_modules/react-icons/md/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_md_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=PiSquaresFourBold!=!./node_modules/react-icons/pi/index.mjs":
/*!*********************************************************************************************!*\
  !*** __barrel_optimize__?names=PiSquaresFourBold!=!./node_modules/react-icons/pi/index.mjs ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_pi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/pi/index.mjs */ "./node_modules/react-icons/pi/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_pi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_pi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=RiArrowDropDownLine!=!./node_modules/react-icons/ri/index.mjs":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=RiArrowDropDownLine!=!./node_modules/react-icons/ri/index.mjs ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_ri_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/ri/index.mjs */ "./node_modules/react-icons/ri/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_ri_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_ri_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=RxCross2!=!./node_modules/react-icons/rx/index.mjs":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=RxCross2!=!./node_modules/react-icons/rx/index.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_rx_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/rx/index.mjs */ "./node_modules/react-icons/rx/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_rx_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_rx_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=TbBrandWechat!=!./node_modules/react-icons/tb/index.mjs":
/*!*****************************************************************************************!*\
  !*** __barrel_optimize__?names=TbBrandWechat!=!./node_modules/react-icons/tb/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_tb_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/tb/index.mjs */ "./node_modules/react-icons/tb/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_tb_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_tb_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;