{"name": "mixdrom", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "eslint .", "postbuild": "next-sitemap", "prepare": "cd .. && husky frontend/.husky"}, "dependencies": {"@emotion/styled": "^11.13.0", "@fontsource/nunito-sans": "^5.1.1", "@fontsource/poppins": "^5.1.1", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@googlemaps/markerclusterer": "^2.5.3", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@iconify/react": "^5.0.1", "@mui/icons-material": "^5.15.20", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.15.20", "@next/bundle-analyzer": "^14.2.13", "@radix-ui/react-tooltip": "^1.2.0", "@react-google-maps/api": "^2.19.3", "@react-google-maps/marker-clusterer": "^2.20.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.7", "@rsuite/icons": "^1.3.0", "@sweetalert2/ngx-sweetalert2": "^12.4.0", "@tailwindcss/line-clamp": "^0.4.4", "@tippyjs/react": "^4.2.6", "apexcharts": "^4.4.0", "aws-sdk": "^2.1663.0", "axios": "^1.7.2", "chart.js": "^4.4.3", "country-flag-icons": "^1.5.13", "country-list": "^2.3.0", "country-state-city": "^3.2.1", "country-to-currency": "^1.1.5", "date-fns": "^3.6.0", "firebase": "^10.14.1", "flatpickr": "^4.6.13", "flowbite-react": "^0.10.2", "framer-motion": "^12.5.0", "lucide-react": "^0.390.0", "moment": "^2.30.1", "mui-tel-input": "^5.1.2", "next": "^15.1.3", "next-sitemap": "^4.2.3", "next-themes": "^0.3.0", "nextjs-toploader": "^1.6.12", "nprogress": "^0.2.0", "or": "^0.2.0", "otp-input-react": "^0.3.0", "razorpay": "^2.9.4", "react": "^18", "react-apexcharts": "^1.7.0", "react-apple-login": "^1.1.6", "react-big-calendar": "^1.14.1", "react-chartjs-2": "^5.2.0", "react-country-region-selector": "^3.6.1", "react-datepicker": "^7.6.0", "react-dom": "^18", "react-facebook": "^9.0.12", "react-fast-marquee": "^1.6.5", "react-flatpickr": "^4.0.10", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-intersection-observer": "^9.16.0", "react-paginate": "^8.2.0", "react-phone-number-input": "^3.4.5", "react-rating-stars-component": "^2.2.0", "react-redux": "^9.1.2", "react-select": "^5.8.0", "react-signature-canvas": "^1.0.7", "react-type-animation": "^3.2.0", "recharts": "^2.12.7", "rsuite": "^5.76.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "styled-components": "^6.1.13", "sweetalert2": "^11.12.3", "swiper": "^11.1.9", "tailwind-scrollbar": "^3.1.0", "tailwind-scrollbar-hide": "^2.0.0", "vis-timeline": "^7.7.3", "world-countries": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8.4.47", "react-router-dom": "^7.1.3", "tailwindcss": "^3.4.13"}}