"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_home_TravelActivity_js";
exports.ids = ["_pages-dir-node_components_home_TravelActivity_js"];
exports.modules = {

/***/ "(pages-dir-node)/./components/home/<USER>":
/*!*******************************************!*\
  !*** ./components/home/<USER>
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_react__WEBPACK_IMPORTED_MODULE_3__, swiper_modules__WEBPACK_IMPORTED_MODULE_4__]);\n([swiper_react__WEBPACK_IMPORTED_MODULE_3__, swiper_modules__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unescaped-entities */ \n\n\n\n\n\n// import { motion } from \"framer-motion\";\n\nconst TravelActivity = ()=>{\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const data = [\n        {\n            id: 1,\n            title: \"Explore Ruins\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity1.webp`,\n            Link: \"/travelactivity?category=Explore Ruins\"\n        },\n        {\n            id: 2,\n            title: \"Beach Snorkel\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity2.webp`,\n            Link: \"/travelactivity?category=Beach Snorkel\"\n        },\n        {\n            id: 3,\n            title: \"City Cycling\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity3.webp`,\n            Link: \"/travelactivity?category=City Cycling\"\n        },\n        {\n            id: 4,\n            title: \"Mountain Trek\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity4.webp`,\n            Link: \"/travelactivity?category=Mountain Trek\"\n        },\n        {\n            id: 5,\n            title: \"Food Tour\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity5.webp`,\n            Link: \"/travelactivity?category=Food Tour\"\n        },\n        {\n            id: 6,\n            title: \"River Cruise\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity6.webp`,\n            Link: \"/travelactivity?category=River Cruise\"\n        },\n        {\n            id: 7,\n            title: \"Spa Retreat\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity7.webp`,\n            Link: \"/travelactivity?category=Spa Retreat\"\n        },\n        {\n            id: 8,\n            title: \"Road Trip\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity8.webp`,\n            Link: \"/travelactivity?category=Road Trips\"\n        },\n        {\n            id: 9,\n            title: \"City Rush\",\n            image: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Travel-activity9.webp`,\n            Link: \"/travelactivity?category=City Rush\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TravelActivity.useEffect\": ()=>{\n            if (data && data.length > 0) {\n                setLoading(false);\n            }\n        }\n    }[\"TravelActivity.useEffect\"], [\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"w-full bg-white md:py-16 sm:py-10 py-8 lg:px-6 relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-10 right-8 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-20 -left-8 w-48 h-48 bg-yellow-300 rounded-full blur-2xl opacity-30\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-10 right-4 w-40 h-40 bg-pink-300 rounded-full blur-2xl opacity-40\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"font-normal xs:text-4xl text-3xl md:text-6xl text-primary-blue font-mashiny sm:mb-8 mb-3\",\n                                    children: [\n                                        \"Travel by \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-black\",\n                                            children: \" Activities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center sm:hidden block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/travelactivity\",\n                                        className: \"text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750\",\n                                        prefetch: false,\n                                        children: \"See All\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `gap-2 ${data?.length > 9 ? 'xl:flex' : 'xl:hidden'} ${data?.length > 8 ? 'lg:flex' : 'lg:hidden'} ${data?.length > 6 ? 'md:flex' : 'md:hidden'} ${data?.length > 4.6 ? 'sm:flex' : 'sm:hidden'} ${data?.length > 3.4 ? 'hidden' : 'hidden'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"slider-button-prev cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__.ArrowLeft, {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                lineNumber: 112,\n                                                columnNumber: 66\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"slider-button-next cursor-pointer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__.ArrowRight, {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                lineNumber: 113,\n                                                columnNumber: 66\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hidden sm:block mt-2 text-base font-medium text-[#737373] font-manrope mb-3 w-[80%]\",\n                            children: [\n                                \"Discover top-rated hostels and dormitories\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/travelactivity\",\n                                    className: \"text-primary-blue\",\n                                    children: \" travel activities\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                    lineNumber: 123,\n                                    columnNumber: 55\n                                }, undefined),\n                                \" around the world, curated for every kind of traveler. Whether you're chasing adventure, looking for a social backpackers’ vibe, or craving a peaceful budget retreat, our handpicked hostels offer the perfect mix of comfort, affordability, and experience. Explore our top 8 featured hostels, each offering unique amenities, vibrant atmospheres, and competitive room ratesfor solo travelers, couples, and groups alike. Start your hostel booking journey with Mixdorm today and find the stay that matches your travel style.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                modules: [\n                                    swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Autoplay,\n                                    swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Navigation\n                                ],\n                                // autoplay={{ delay: 1500 }}\n                                slidesPerView: 18,\n                                loop: true,\n                                navigation: {\n                                    prevEl: '.slider-button-prev',\n                                    nextEl: '.slider-button-next'\n                                },\n                                speed: 1000,\n                                spaceBetween: 14,\n                                className: \"mySwiper myCustomSwiper travelactivity-slider discover-event-slider-home overflow-hidden\",\n                                breakpoints: {\n                                    0: {\n                                        slidesPerView: 3.2\n                                    },\n                                    480: {\n                                        slidesPerView: 3.4\n                                    },\n                                    640: {\n                                        slidesPerView: 4.6\n                                    },\n                                    768: {\n                                        slidesPerView: 6\n                                    },\n                                    1024: {\n                                        slidesPerView: 8\n                                    },\n                                    1280: {\n                                        slidesPerView: 9\n                                    }\n                                },\n                                children: loading ? // Render 8 skeleton slides\n                                Array.from({\n                                    length: 8\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 rounded-full bg-gray-200 animate-pulse mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-4 bg-gray-200 animate-pulse rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, `skeleton-${index}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, undefined)) : // Render actual data\n                                data.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: item?.Link,\n                                            className: \"font-manrope text-center tracking-normal group transition-all py-3 block\",\n                                            prefetch: false,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-20 h-20 min-w-20 min-h-20 xs:w-24 xs:h-24 xs:min-w-24 xs:min-h-24 mx-auto mb-4 rounded-full overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        src: item.image,\n                                                        alt: `Travel by Activity ${item.title}`,\n                                                        fill: true,\n                                                        className: `rounded-full object-cover group-hover:scale-110 transition-all ${loading ? \"bg-slate-200 animate-pulse\" : \"bg-slate-200\"}`,\n                                                        sizes: \"(max-width: 640px) 80px, 96px\",\n                                                        loading: \"lazy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"xs:text-sm text-xs font-bold mb-0 group-hover:text-primary-blue transition-all\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, item.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-10 hidden sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/travelactivity\",\n                                className: \"text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750\",\n                                prefetch: false,\n                                children: \"See All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\TravelActivity.js\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TravelActivity);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/home/<USER>");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;