/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_home_Expand_js"],{

/***/ "(pages-dir-browser)/./components/home/<USER>":
/*!***********************************!*\
  !*** ./components/home/<USER>
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight!=!react-icons/fa6 */ \"(pages-dir-browser)/__barrel_optimize__?names=FaArrowRight!=!./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"(pages-dir-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! swiper/modules */ \"(pages-dir-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/navigation */ \"(pages-dir-browser)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-browser)/./services/webflowServices.jsx\");\n/* eslint-disable react/no-unescaped-entities */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// import { useInView } from \"react-intersection-observer\";\n\n\n\n\n\n\n\n// import { motion } from \"framer-motion\";\nconst ExpandPage = ()=>{\n    _s();\n    const [currentImage, setCurrentImage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // eslint-disable-next-line no-unused-vars\n    const [manualClick, setManualClick] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // const [ref1, inView1] = useInView({ threshold: 0.2 });\n    // const [ref2, inView2] = useInView({ threshold: 0.1 });\n    // const [ref3, inView3] = useInView({ threshold: 0.98 });\n    const [popertyCount, setPopertyCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [visibleSlides, setVisibleSlides] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    // eslint-disable-next-line no-unused-vars\n    const [direction, setDirection] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"right\"); // Track slide direction\n    const countryImages = {\n        India: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/india-new.webp\"),\n        Thailand: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Thailand-new.webp\"),\n        Indonesia: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Indonesia-new.webp\"),\n        Colombia: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/colombiaaa.webp\"),\n        Spain: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/spainnn.webp\"),\n        Mexico: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/mexicooo.webp\"),\n        Italy: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/italyyy-new.webp\"),\n        Portugal: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/portugalll.webp\"),\n        Brazil: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/brazilll.webp\"),\n        USA: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/usaaa.webp\"),\n        Japan: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/japannn.webp\"),\n        Vietnam: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Vietnam-new.webp\"),\n        France: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/franceee.webp\"),\n        Australia: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/australiaaa.webp\"),\n        Peru: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/peruuu.webp\")\n    };\n    const CountryList = [\n        \"India\",\n        \"Thailand\",\n        \"Indonesia\",\n        \"Colombia\",\n        \"Spain\",\n        \"Mexico\",\n        \"Italy\",\n        \"Portugal\",\n        \"Brazil\",\n        \"USA\",\n        \"Japan\",\n        \"Vietnam\",\n        \"France\",\n        \"Australia\",\n        \"Peru\"\n    ];\n    const images = [\n        \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Bookhostel.webp\"),\n        \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/phone-details-1.2.webp\"),\n        \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/phone-details-2.2.webp\")\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExpandPage.useEffect\": ()=>{\n            const fetchPropertyCount = {\n                \"ExpandPage.useEffect.fetchPropertyCount\": async ()=>{\n                    try {\n                        var _response_data_data, _response_data;\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_7__.getHomePagePropertyCountApi)({\n                            countries: CountryList\n                        });\n                        setPopertyCount((response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_data = _response_data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.propertyCounts) || []);\n                    } catch (error) {\n                        console.error(\"Error fetching stay data:\", error);\n                    } finally{\n                    /* empty */ }\n                }\n            }[\"ExpandPage.useEffect.fetchPropertyCount\"];\n            if (!isFirstRender.current) {\n                fetchPropertyCount();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"ExpandPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ExpandPage.useEffect\": ()=>{\n            const checkMobile = {\n                \"ExpandPage.useEffect.checkMobile\": ()=>{\n                    setIsMobile( true && window.innerWidth <= 768);\n                }\n            }[\"ExpandPage.useEffect.checkMobile\"];\n            // Initial check\n            checkMobile();\n            // Add resize listener\n            if (true) {\n                window.addEventListener(\"resize\", checkMobile);\n                return ({\n                    \"ExpandPage.useEffect\": ()=>window.removeEventListener(\"resize\", checkMobile)\n                })[\"ExpandPage.useEffect\"];\n            }\n        }\n    }[\"ExpandPage.useEffect\"], []);\n    // useEffect(() => {\n    //   if (!manualClick) {\n    //     if (inView3) setCurrentImage(2);\n    //     else if (inView2) setCurrentImage(1);\n    //     else if (inView1) setCurrentImage(0);\n    //   }\n    // }, [inView1, inView2, inView3, manualClick]);\n    const handleClick = (index)=>{\n        setCurrentImage(index);\n        setManualClick(true);\n    // setTimeout(() => setManualClick(false), 3500); // allow scroll update after 1.5s\n    };\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array(images.length).fill(false));\n    const [qrLoaded, setQrLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleImageLoad = (index)=>{\n        setImageLoaded((prev)=>{\n            const updated = [\n                ...prev\n            ];\n            updated[index] = true;\n            return updated;\n        });\n    };\n    // useEffect(() => {\n    //   const interval = setInterval(() => {\n    //     setCurrentImage((prev) => (prev + 1) % images.length); // Loop through images\n    //   }, 5000); // Change image every 3 seconds\n    //   return () => clearInterval(interval); // Cleanup interval\n    // }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative container z-0 sm:block hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"w-full bg-transparent md:pb-16 pb-2 backdrop-blur-sm hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full  absolute -top-44 lg:-top-60 \",\n                    children: isMobile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-semibold text-white font-manrope md:text-3xl sm:text-2xl text-xl mb-3\",\n                                children: [\n                                    \"Explore the\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mashiny font-normal text-primary-blue text-4xl md:text-5xl\",\n                                        children: [\n                                            \" \",\n                                            \"World\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                slidesPerView: \"auto\",\n                                spaceBetween: 15,\n                                autoplay: true,\n                                loop: true,\n                                modules: [\n                                    swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Autoplay,\n                                    swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Navigation\n                                ],\n                                // navigation={true}\n                                className: \"mySwiper myCustomSwiper\",\n                                autoHeight: true,\n                                onSlideChange: (swiper)=>{\n                                    // Get currently visible slides\n                                    const newVisibleSlides = swiper.slides.slice(swiper.activeIndex, swiper.activeIndex + 4) // Select 4 slides in view\n                                    .map((slide)=>slide.getAttribute(\"data-index\")); // Get index of each slide\n                                    setVisibleSlides(newVisibleSlides);\n                                },\n                                breakpoints: {\n                                    0: {\n                                        slidesPerView: 1.5,\n                                        spaceBetween: 20\n                                    },\n                                    640: {\n                                        slidesPerView: 2\n                                    },\n                                    768: {\n                                        slidesPerView: 3\n                                    },\n                                    1024: {\n                                        slidesPerView: 4\n                                    }\n                                },\n                                children: [\n                                    \"India\",\n                                    \"Thailand\",\n                                    \"Indonesia\",\n                                    \"Colombia\",\n                                    \"Spain\",\n                                    \"Mexico\",\n                                    \"Italy\",\n                                    \"Portugal\",\n                                    \"Brazil\",\n                                    \"USA\",\n                                    \"Japan\",\n                                    \"Vietnam\",\n                                    \"France\",\n                                    \"Australia\",\n                                    \"Peru\"\n                                ].map((country, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                        \"data-index\": index,\n                                        className: \"\".concat(visibleSlides.indexOf(index.toString()) === 1 || visibleSlides.indexOf(index.toString()) === 3 ? \"mt-6 pb-10\" : \"mt-0\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative overflow-hidden duration-300 ease-in-out group min-h-[228px] max-h-[268px] shadow-lg bg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                        href: \"/tophostel?country=\".concat(country),\n                                                        prefetch: false,\n                                                        className: \"comman-tooltip\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            src: countryImages[country],\n                                                            width: 208,\n                                                            height: 362,\n                                                            alt: country,\n                                                            className: \"object-cover w-full h-full duration-300 ease-in-out opacity-100 group-hover:scale-105\",\n                                                            loading: \"lazy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute text-white top-[80%] font-manrope left-5 opacity-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm sm:text-base font-bold\",\n                                                                children: [\n                                                                    \"Hostel in \",\n                                                                    country\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            (popertyCount === null || popertyCount === void 0 ? void 0 : popertyCount[country]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-sm font-normal\",\n                                                                children: [\n                                                                    popertyCount[country],\n                                                                    \" Hostels\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 205,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between flex-col md:flex-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-semibold text-white  md:text-3xl sm:text-2xl text-xl mb-5  relative z-10\",\n                                        children: [\n                                            \"Explore the\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl text-primary-blue\",\n                                                children: [\n                                                    \"World\",\n                                                    \" \"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/exploreworld\",\n                                        prefetch: false,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-primary-blue hover:bg-teal-400 h-9 w-24 rounded-3xl text-sm font-semibold mt-5\",\n                                            children: \"See All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                    slidesPerView: \"auto\",\n                                    spaceBetween: 15,\n                                    autoplay: true,\n                                    loop: true,\n                                    modules: [\n                                        swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Autoplay,\n                                        swiper_modules__WEBPACK_IMPORTED_MODULE_5__.Navigation\n                                    ],\n                                    // navigation={true}\n                                    className: \"mySwiper myCustomSwiper\",\n                                    autoHeight: true,\n                                    onSlideChange: (swiper)=>{\n                                        // Get currently visible slides\n                                        const newVisibleSlides = swiper.slides.slice(swiper.activeIndex, swiper.activeIndex + 4) // Select 4 slides in view\n                                        .map((slide)=>slide.getAttribute(\"data-index\")); // Get index of each slide\n                                        setVisibleSlides(newVisibleSlides);\n                                    },\n                                    breakpoints: {\n                                        0: {\n                                            slidesPerView: 1.5,\n                                            spaceBetween: 20\n                                        },\n                                        640: {\n                                            slidesPerView: 2\n                                        },\n                                        768: {\n                                            slidesPerView: 3\n                                        },\n                                        1024: {\n                                            slidesPerView: 4\n                                        }\n                                    },\n                                    children: [\n                                        \"India\",\n                                        \"Thailand\",\n                                        \"Indonesia\",\n                                        \"Colombia\",\n                                        \"Spain\",\n                                        \"Mexico\",\n                                        \"Italy\",\n                                        \"Portugal\",\n                                        \"Brazil\",\n                                        \"USA\",\n                                        \"Japan\",\n                                        \"Vietnam\",\n                                        \"France\",\n                                        \"Australia\",\n                                        \"Peru\"\n                                    ].map((country, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                            \"data-index\": index,\n                                            className: \"\".concat(visibleSlides.indexOf(index.toString()) === 1 || visibleSlides.indexOf(index.toString()) === 3 ? \"mt-5 py-10\" : \"mt-0\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative overflow-hidden duration-300 ease-in-out group min-h-40 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/tophostel?country=\".concat(country),\n                                                            prefetch: false,\n                                                            className: \"comman-tooltip\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                src: countryImages[country],\n                                                                width: 208,\n                                                                height: 362,\n                                                                alt: country,\n                                                                className: \"object-cover w-full h-full duration-300 ease-in-out  group-hover:scale-105 bg-white opacity-100\",\n                                                                loading: \"lazy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute text-white top-[83%] font-manrope left-5 opacity-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm 2xl:text-xl font-bold\",\n                                                                    children: [\n                                                                        \"Hostel in \",\n                                                                        country\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                (popertyCount === null || popertyCount === void 0 ? void 0 : popertyCount[country]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-xs  2xl:text-sm font-normal\",\n                                                                    children: [\n                                                                        popertyCount[country],\n                                                                        \" Hostels\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white flex flex-col z-0 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"relative z-10 w-full lg:w-[40%] text-left lg:text-right font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl mb-4 md:mb-0 mt-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl\",\n                                children: \"Expand\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \",\n                            \"your\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl \",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex  justify-between flex-col-reverse md:flex-row md:justify-around lg:justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-[20%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1 left-[33%] w-40 h-40 bg-pink-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1 left-[10%] w-40 h-40 bg-yellow-300 rounded-full blur-2xl opacity-40\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 -left-2 w-36 h-36 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-[48%] left-[15%] w-36 h-36 bg-yellow-300 rounded-full blur-xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-[7%] right-[29%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-[23%] right-[4%] w-40 h-40 bg-pink-400 rounded-full blur-xl opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex flex-wrap justify-end items-start lg:justify-end  lg:items-end md:hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // initial={{ x: -100, opacity: 0 }}\n                                        // whileInView={{ x: 0, opacity: 1 }}\n                                        // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.3 }}\n                                        // viewport={{ once: false }}\n                                        onClick: ()=>setCurrentImage(0),\n                                        className: \"relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] \".concat(currentImage === 0 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"sr-only\",\n                                                children: \"Main Page Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"sr-only\",\n                                                children: \"Section Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                children: \"Book your hostels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer line-clamp-4\",\n                                                children: \"Top Hostel Booking Made Easy with Mixdorm Explore and book hostels, dormitories, and budget hotels around the world — from solo backpacker spots to vibrant youth hostels in top cities. Whether you're looking for the cheapest accommodation, a cozy hostel stay, or social backpackers hostels, Mixdorm helps you find the perfect match. Start your hostel booking in India or across the globe with real traveler reviews, smart filters, and seamless online booking. From hidden gems to popular picks, discover hostel listings tailored for travelers like you.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                    className: \"text-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex  justify-start items-stretch gap-6 mt-8 w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.9 }}\n                                                // viewport={{ once: false }}\n                                                onClick: ()=>setCurrentImage(1),\n                                                className: \"relative p-5 border rounded-xl w-full max-w-[280px]  \".concat(currentImage === 1 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                        children: \"Noticeboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer\",\n                                                        children: \"AI-powered social noticeboard keeps you updated on trending stays, local events, and must-visit spots—helping you connect, explore, and experience more on your journey.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.6 }}\n                                                // viewport={{ once: false }}\n                                                onClick: ()=>setCurrentImage(2),\n                                                className: \"relative p-4 border rounded-xl max-w-[217px] \".concat(currentImage === 2 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer whitespace-nowrap\",\n                                                        children: \"Events Spotlight\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer line-clamp-5\",\n                                                        children: \"Explore Top-Rated Hostels Around the World Discover the best hostels and dormitory stays offering fun events, social vibes, and unforgettable travel experiences. From budget-friendly hostels to lively backpackers' stays, find your perfect match for adventure, comfort, and connection.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full hidden md:flex flex-col justify-end items-start lg:items-end md:w-[90%] lg:w-[45%] mb-16 px-4 lg:px-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        // initial={{ x: -100, opacity: 0 }}\n                                        // whileInView={{ x: 0, opacity: 1 }}\n                                        // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.3 }}\n                                        // viewport={{ once: false }}\n                                        // ref={ref1}\n                                        onClick: ()=>handleClick(0),\n                                        className: \"relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] \".concat(currentImage === 0 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"sr-only\",\n                                                children: \"Main Page Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 513,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"sr-only\",\n                                                children: \"Section Title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 514,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                children: \"Book your hostels\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 515,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 font-manrope cursor-pointer line-clamp-4\",\n                                                children: [\n                                                    \"Top Hostel Booking Made Easy with \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                        href: \"/owner/about-mixdorm\",\n                                                        className: \"text-primary-blue\",\n                                                        children: \"Mixdorm \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 51\n                                                    }, undefined),\n                                                    \"Explore and book hostels, dormitories, and budget hotels around the world — from solo backpacker spots to vibrant youth hostels in top cities. Whether you're looking for the cheapest accommodation, a cozy hostel stay, or social backpackers hostels, Mixdorm helps you find the perfect match. Start your hostel booking in India or across the globe with real traveler reviews, smart filters, and seamless online booking. From hidden gems to popular picks, discover hostel listings tailored for travelers like you.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 518,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                    className: \"text-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row justify-start items-stretch gap-6 mt-8 w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.9 }}\n                                                // viewport={{ once: false }}\n                                                // ref={ref2}\n                                                onClick: ()=>handleClick(1),\n                                                className: \"relative p-5 border rounded-xl w-full max-w-[330px] \".concat(currentImage === 1 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                        children: \"Noticeboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 font-manrope cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                href: \"/meetbuddies\",\n                                                                className: \"text-primary-blue\",\n                                                                children: \"AI-powered social noticeboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            \" keeps you updated on trending stays, local events, and must-visit spots—helping you connect, explore, and experience more on your journey.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 550,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                // initial={{ x: -100, opacity: 0 }}\n                                                // whileInView={{ x: 0, opacity: 1 }}\n                                                // transition={{ duration: 0.8, ease: \"easeOut\", delay: 0.6 }}\n                                                // viewport={{ once: false }}\n                                                // ref={ref3}\n                                                onClick: ()=>handleClick(2),\n                                                className: \"relative p-4 border rounded-xl w-full sm:max-w-[217px] \".concat(currentImage === 2 ? \"border-2 border-dashed border-primary-blue shadow-xl\" : \"border border-gray-300 shadow-none\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer\",\n                                                        children: \"Events Spotlight\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 font-manrope cursor-pointer line-clamp-5\",\n                                                        children: \"Explore Top-Rated Hostels Around the World Discover the best hostels and dormitory stays offering fun events, social vibes, and unforgettable travel experiences. From budget-friendly hostels to lively backpackers' stays, find your perfect match for adventure, comfort, and connection.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_react_icons_fa6__WEBPACK_IMPORTED_MODULE_8__.FaArrowRight, {\n                                                            className: \"text-black\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center w-full sm:w-[90%] md:w-[70%] lg:w-[30%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    // initial={{ y: 80, opacity: 0 }}\n                                    // whileInView={{ y: 0, opacity: 1 }}\n                                    // transition={{\n                                    //   type: \"spring\",\n                                    //   stiffness: 120,\n                                    //   damping: 12,\n                                    //   duration: 0.8,\n                                    //   delay: 0.2,\n                                    // }}\n                                    // viewport={{ once: false }}\n                                    className: \"relative w-full max-w-[327px] h-[670px] rounded-3xl p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                src: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/Phone-image.webp\"),\n                                                width: 327,\n                                                height: 670,\n                                                alt: \"Mobile UI Mockup\",\n                                                className: \"rounded-3xl w-full h-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute rounded-3xl top-2 left-3 w-[100%] md:w-[90%] max-w-[270px] overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-full rounded-3xl h-[660px] flex transition-transform duration-500 ease-in-out \".concat(direction === \"right\" ? \"translate-x-0\" : \"-translate-x-full\"),\n                                                    children: images.map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-hidden\",\n                                                            children: [\n                                                                !imageLoaded[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full max-h-[560px] h-[560px] bg-slate-200 animate-pulse rounded-3xl z-10 flex items-center justify-center absolute top-0 left-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                        className: \"text-white font-bold font-manrope text-center\",\n                                                                        children: \"MixDorm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                        lineNumber: 665,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    src: img,\n                                                                    width: 320,\n                                                                    height: 660,\n                                                                    alt: \"Mobile UI Mockup\",\n                                                                    onLoad: ()=>handleImageLoad(index),\n                                                                    className: \"absolute rounded-3xl transition-transform duration-500 \".concat(index === currentImage ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\")\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                        lineNumber: 643,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex justify-start w-[30%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col justify-evenly \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            // initial={{ x: 100, opacity: 0 }}\n                                            // whileInView={{ x: 0, opacity: 1 }}\n                                            // transition={{ duration: 0.8, ease: \"easeOut\" }}\n                                            // viewport={{ once: false }}\n                                            className: \"pb-56\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-[15px] font-manrope text-justify\",\n                                                    children: [\n                                                        \"Find \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/tophostel?country=India\",\n                                                            className: \"text-primary-blue\",\n                                                            children: \"Top-Rated Hostels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 24\n                                                        }, undefined),\n                                                        \" Worldwide for Every Kind of Traveler Explore the best hostel stays across the globe — perfect for solo travelers, backpackers, digital nomads, and budget-conscious explorers. From vibrant social hostels in major cities to peaceful dorms in nature getaways, our curated \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/search\",\n                                                            className: \"text-primary-blue\",\n                                                            children: \" hostel listings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 69\n                                                        }, undefined),\n                                                        \" offer unique stays on every continent.\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/search\",\n                                                            className: \"text-primary-blue\",\n                                                            children: \" Search hostels\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        \" by destination, book instantly, and connect with travelers worldwide. Start Your Hostel Adventure Now\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-primary-blue text-black py-2.5 px-5 rounded-full shadow-lg transition text-sm font-manrope font-semibold mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                        href: \"/exploreworld\",\n                                                        children: \"Explore World \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-10 text-center flex items-end justify-end w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-200  shadow-xl shadow-black/40 p-4 rounded-lg inline-block w-[155px] h-[147px] relative bottom-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-black text-base font-manrope font-extrabold\",\n                                                        children: \"Scan Here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute h-[102px] w-[102px] left-7\",\n                                                        children: [\n                                                            !qrLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full bg-slate-200 animate-pulse rounded-lg absolute top-0 left-0 z-10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                src: \"\".concat(\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\", \"/front-images/QR.webp\"),\n                                                                width: 100,\n                                                                height: 100,\n                                                                onLoad: ()=>setQrLoaded(true),\n                                                                alt: \"QR Code\",\n                                                                className: \"rounded-lg transition-opacity duration-300 \".concat(qrLoaded ? \"opacity-100\" : \"opacity-0\"),\n                                                                style: {\n                                                                    height: \"100%\",\n                                                                    width: \"100%\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                            lineNumber: 756,\n                                            columnNumber: 16\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                                lineNumber: 693,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\Expand.js\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ExpandPage, \"NIXV8wtC17Euqm22fbdmA+f9ZYo=\");\n_c = ExpandPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExpandPage);\nvar _c;\n$RefreshReg$(_c, \"ExpandPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/home/<USER>"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./node_modules/swiper/modules/navigation.css":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./node_modules/swiper/modules/navigation.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \":root {\\n  --swiper-navigation-size: 44px;\\n  /*\\n  --swiper-navigation-top-offset: 50%;\\n  --swiper-navigation-sides-offset: 10px;\\n  --swiper-navigation-color: var(--swiper-theme-color);\\n  */\\n}\\n.swiper-button-prev,\\n.swiper-button-next {\\n  position: absolute;\\n  top: var(--swiper-navigation-top-offset, 50%);\\n  width: calc(var(--swiper-navigation-size) / 44 * 27);\\n  height: var(--swiper-navigation-size);\\n  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));\\n  z-index: 10;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--swiper-navigation-color, var(--swiper-theme-color));\\n}\\n.swiper-button-prev.swiper-button-disabled,\\n.swiper-button-next.swiper-button-disabled {\\n  opacity: 0.35;\\n  cursor: auto;\\n  pointer-events: none;\\n}\\n.swiper-button-prev.swiper-button-hidden,\\n.swiper-button-next.swiper-button-hidden {\\n  opacity: 0;\\n  cursor: auto;\\n  pointer-events: none;\\n}\\n.swiper-navigation-disabled .swiper-button-prev,\\n.swiper-navigation-disabled .swiper-button-next {\\n  display: none !important;\\n}\\n.swiper-button-prev svg,\\n.swiper-button-next svg {\\n  width: 100%;\\n  height: 100%;\\n  -o-object-fit: contain;\\n     object-fit: contain;\\n  transform-origin: center;\\n}\\n.swiper-rtl .swiper-button-prev svg,\\n.swiper-rtl .swiper-button-next svg {\\n  transform: rotate(180deg);\\n}\\n.swiper-button-prev,\\n.swiper-rtl .swiper-button-next {\\n  left: var(--swiper-navigation-sides-offset, 10px);\\n  right: auto;\\n}\\n.swiper-button-next,\\n.swiper-rtl .swiper-button-prev {\\n  right: var(--swiper-navigation-sides-offset, 10px);\\n  left: auto;\\n}\\n.swiper-button-lock {\\n  display: none;\\n}\\n/* Navigation font start */\\n.swiper-button-prev:after,\\n.swiper-button-next:after {\\n  font-family: swiper-icons;\\n  font-size: var(--swiper-navigation-size);\\n  text-transform: none !important;\\n  letter-spacing: 0;\\n  font-variant: initial;\\n  line-height: 1;\\n}\\n.swiper-button-prev:after,\\n.swiper-rtl .swiper-button-next:after {\\n  content: 'prev';\\n}\\n.swiper-button-next,\\n.swiper-rtl .swiper-button-prev {\\n  right: var(--swiper-navigation-sides-offset, 10px);\\n  left: auto;\\n}\\n.swiper-button-next:after,\\n.swiper-rtl .swiper-button-prev:after {\\n  content: 'next';\\n}\\n/* Navigation font end */\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://node_modules/swiper/modules/navigation.css\"],\"names\":[],\"mappings\":\"AAAA;EACE,8BAA8B;EAC9B;;;;GAIC;AACH;AACA;;EAEE,kBAAkB;EAClB,6CAA6C;EAC7C,oDAAoD;EACpD,qCAAqC;EACrC,2DAA2D;EAC3D,WAAW;EACX,eAAe;EACf,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,gEAAgE;AAClE;AACA;;EAEE,aAAa;EACb,YAAY;EACZ,oBAAoB;AACtB;AACA;;EAEE,UAAU;EACV,YAAY;EACZ,oBAAoB;AACtB;AACA;;EAEE,wBAAwB;AAC1B;AACA;;EAEE,WAAW;EACX,YAAY;EACZ,sBAAmB;KAAnB,mBAAmB;EACnB,wBAAwB;AAC1B;AACA;;EAEE,yBAAyB;AAC3B;AACA;;EAEE,iDAAiD;EACjD,WAAW;AACb;AACA;;EAEE,kDAAkD;EAClD,UAAU;AACZ;AACA;EACE,aAAa;AACf;AACA,0BAA0B;AAC1B;;EAEE,yBAAyB;EACzB,wCAAwC;EACxC,+BAA+B;EAC/B,iBAAiB;EACjB,qBAAqB;EACrB,cAAc;AAChB;AACA;;EAEE,eAAe;AACjB;AACA;;EAEE,kDAAkD;EAClD,UAAU;AACZ;AACA;;EAEE,eAAe;AACjB;AACA,wBAAwB\",\"sourcesContent\":[\":root {\\n  --swiper-navigation-size: 44px;\\n  /*\\n  --swiper-navigation-top-offset: 50%;\\n  --swiper-navigation-sides-offset: 10px;\\n  --swiper-navigation-color: var(--swiper-theme-color);\\n  */\\n}\\n.swiper-button-prev,\\n.swiper-button-next {\\n  position: absolute;\\n  top: var(--swiper-navigation-top-offset, 50%);\\n  width: calc(var(--swiper-navigation-size) / 44 * 27);\\n  height: var(--swiper-navigation-size);\\n  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));\\n  z-index: 10;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--swiper-navigation-color, var(--swiper-theme-color));\\n}\\n.swiper-button-prev.swiper-button-disabled,\\n.swiper-button-next.swiper-button-disabled {\\n  opacity: 0.35;\\n  cursor: auto;\\n  pointer-events: none;\\n}\\n.swiper-button-prev.swiper-button-hidden,\\n.swiper-button-next.swiper-button-hidden {\\n  opacity: 0;\\n  cursor: auto;\\n  pointer-events: none;\\n}\\n.swiper-navigation-disabled .swiper-button-prev,\\n.swiper-navigation-disabled .swiper-button-next {\\n  display: none !important;\\n}\\n.swiper-button-prev svg,\\n.swiper-button-next svg {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: contain;\\n  transform-origin: center;\\n}\\n.swiper-rtl .swiper-button-prev svg,\\n.swiper-rtl .swiper-button-next svg {\\n  transform: rotate(180deg);\\n}\\n.swiper-button-prev,\\n.swiper-rtl .swiper-button-next {\\n  left: var(--swiper-navigation-sides-offset, 10px);\\n  right: auto;\\n}\\n.swiper-button-next,\\n.swiper-rtl .swiper-button-prev {\\n  right: var(--swiper-navigation-sides-offset, 10px);\\n  left: auto;\\n}\\n.swiper-button-lock {\\n  display: none;\\n}\\n/* Navigation font start */\\n.swiper-button-prev:after,\\n.swiper-button-next:after {\\n  font-family: swiper-icons;\\n  font-size: var(--swiper-navigation-size);\\n  text-transform: none !important;\\n  letter-spacing: 0;\\n  font-variant: initial;\\n  line-height: 1;\\n}\\n.swiper-button-prev:after,\\n.swiper-rtl .swiper-button-next:after {\\n  content: 'prev';\\n}\\n.swiper-button-next,\\n.swiper-rtl .swiper-button-prev {\\n  right: var(--swiper-navigation-sides-offset, 10px);\\n  left: auto;\\n}\\n.swiper-button-next:after,\\n.swiper-rtl .swiper-button-prev:after {\\n  content: 'next';\\n}\\n/* Navigation font end */\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./node_modules/swiper/modules/navigation.css\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(pages-dir-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || '';\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"(pages-dir-browser)/./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _usemergedref = __webpack_require__(/*! ./use-merged-ref */ \"(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _erroronce = __webpack_require__(/*! ../shared/lib/utils/error-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options) {\n    if (false) {}\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== 'undefined' ? options.locale : 'locale' in router ? router.locale : undefined;\n        const prefetchedKey = href + '%' + as + '%' + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    router.prefetch(href, as, options).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if ('beforePopState' in router) {\n            router[replace ? 'replace' : 'push'](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? 'replace' : 'push'](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    navigate();\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onNavigate, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'locale') {\n                if (props[key] && valType !== 'string') {\n                    throw createPropError({\n                        key,\n                        expected: '`string`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'legacyBehavior') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'prefetch') {\n                if (props[key] != null && valType !== 'boolean' && props[key] !== 'auto') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean | \"auto\"`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    const { href, as } = _react.default.useMemo({\n        \"Link.LinkComponent.useMemo\": ()=>{\n            if (!router) {\n                const resolvedHref = formatStringOrUrl(hrefProp);\n                return {\n                    href: resolvedHref,\n                    as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n                };\n            }\n            const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, hrefProp, true);\n            return {\n                href: resolvedHref,\n                as: asProp ? (0, _resolvehref.resolveHref)(router, asProp) : resolvedAs || resolvedHref\n            };\n        }\n    }[\"Link.LinkComponent.useMemo\"], [\n        router,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: '200px'\n    });\n    const setIntersectionWithResetRef = _react.default.useCallback({\n        \"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\": (el)=>{\n            // Before the link getting observed, check if visible state need to be reset\n            if (previousAs.current !== as || previousHref.current !== href) {\n                resetVisible();\n                previousAs.current = as;\n                previousHref.current = href;\n            }\n            setIntersectionRef(el);\n        }\n    }[\"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\"], [\n        as,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    const setRef = (0, _usemergedref.useMergedRef)(setIntersectionWithResetRef, childRef);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect({\n        \"Link.LinkComponent.useEffect\": ()=>{\n            // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n            if (true) {\n                return;\n            }\n            if (!router) {\n                return;\n            }\n            // If we don't need to prefetch the URL, don't do prefetch.\n            if (!isVisible || !prefetchEnabled) {\n                return;\n            }\n            // Prefetch the URL.\n            prefetch(router, href, as, {\n                locale\n            });\n        }\n    }[\"Link.LinkComponent.useEffect\"], [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        router == null ? void 0 : router.locale,\n        router\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            });\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            });\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        const curLocale = typeof locale !== 'undefined' ? locale : router == null ? void 0 : router.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (router == null ? void 0 : router.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, router == null ? void 0 : router.locales, router == null ? void 0 : router.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, router == null ? void 0 : router.defaultLocale));\n    }\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\")), \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\");\n_c1 = Link;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)({\n    // We do not support link status in the Pages Router, so we always return false\n    pending: false\n});\nconst useLinkStatus = ()=>{\n    // This behaviour is like React's useFormStatus. When the component is not under\n    // a <form> tag, it will get the default value, instead of throwing an error.\n    return (0, _react.useContext)(LinkStatusContext);\n};\nconst _default = Link;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(pages-dir-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === 'function';\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || ''\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHNcXGVycm9yLW9uY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVycm9yT25jZSA9IChfOiBzdHJpbmcpID0+IHt9XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBjb25zdCBlcnJvcnMgPSBuZXcgU2V0PHN0cmluZz4oKVxuICBlcnJvck9uY2UgPSAobXNnOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWVycm9ycy5oYXMobXNnKSkge1xuICAgICAgY29uc29sZS5lcnJvcihtc2cpXG4gICAgfVxuICAgIGVycm9ycy5hZGQobXNnKVxuICB9XG59XG5cbmV4cG9ydCB7IGVycm9yT25jZSB9XG4iXSwibmFtZXMiOlsiZXJyb3JPbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImVycm9ycyIsIlNldCIsIm1zZyIsImhhcyIsImNvbnNvbGUiLCJlcnJvciIsImFkZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"(pages-dir-browser)/./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2xpbmsuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkhBQThDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBPT0pBXFxNaXhkb3JtXFxNaXhkb3JtLVdlYi0yLjBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG5leHRcXGxpbmsuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/swiper/modules/navigation.css":
/*!****************************************************!*\
  !*** ./node_modules/swiper/modules/navigation.css ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!../../next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./navigation.css */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./node_modules/swiper/modules/navigation.css\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector('#__next_css__DO_NOT_USE__');\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === 'default') {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === 'default') {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!../../next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./navigation.css */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./node_modules/swiper/modules/navigation.css\",\n      function () {\n        content = __webpack_require__(/*! !!../../next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!../../next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./navigation.css */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[8].oneOf[11].use[2]!./node_modules/swiper/modules/navigation.css\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/swiper/modules/navigation.css\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=FaArrowRight!=!./node_modules/react-icons/fa6/index.mjs":
/*!*****************************************************************************************!*\
  !*** __barrel_optimize__?names=FaArrowRight!=!./node_modules/react-icons/fa6/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa6/index.mjs */ "(pages-dir-browser)/./node_modules/react-icons/fa6/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa6_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

}]);