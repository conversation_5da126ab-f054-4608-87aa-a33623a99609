import dynamic from "next/dynamic";
import Head from "next/head";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";

// Immediate load components (above the fold)
import Banner from "./Banner";
import RecentSearch from "./RecentSearch";

// Dynamic imports for better performance
const Navbar = dynamic(() => import("../navbar"), {
  loading: () => <NavbarSkeleton />,
});

const ExploreWorld = dynamic(() => import("./exploreWorld"), {
  loading: () => <ExploreWorldSkeleton />,
});

const FlagSlider = dynamic(() => import("./FlagSlider"), {
  loading: () => <div className="h-20 bg-gray-100 animate-pulse" />,
});

const Expand = dynamic(() => import("./Expand"), {
  loading: () => <div className="h-40 bg-gray-100 animate-pulse" />,
});

const FeaturedHostel = dynamic(() => import("./FeaturedHostel"), {
  loading: () => <FeaturedHostelSkeleton />,
});

const TravelActivity = dynamic(() => import("./TravelActivity"), {
  loading: () => <div className="h-60 bg-gray-100 animate-pulse" />,
});

const EventSpotlight = dynamic(() => import("./eventSpotlight"), {
  loading: () => <div className="h-60 bg-gray-100 animate-pulse" />,
});

const OurlatestBlog = dynamic(() => import("./OurlatestBlog"), {
  loading: () => <div className="h-60 bg-gray-100 animate-pulse" />,
});

const ChannelPartner = dynamic(() => import("./ChannelPartner"), {
  loading: () => <div className="h-40 bg-gray-100 animate-pulse" />,
});

const FAQs = dynamic(() => import("./Faqs"), {
  loading: () => <div className="h-60 bg-gray-100 animate-pulse" />,
});


const NavbarSkeleton = () => <div className="h-16 bg-transparent" />;
const ExploreWorldSkeleton = () => <section className='w-full bg-transparent md:pb-0 pb-0 relative sm:mt-6 mt-1 z-10'>
  <div className='lg:px-6 sm:mb-2'>
    <div className='w-full container'>
      <div className='w-full sm:pt-4 arrow_top'>
        {/* Mobile (0-399px) - 5 slides */}
        <div className="block sm:hidden">
          <Swiper
            modules={[Autoplay]}
            loop
            slidesPerView={5}
            spaceBetween={10}
            className='mySwiper myCustomSwiper'
          >
            {[...Array(5)].map((_, index) => (
              <SwiperSlide key={index} className='py-3'>
                <div className="flex flex-col items-center">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full w-10 h-10 mx-auto animate-pulse"></div>
                  <div className='bg-gray-200 dark:bg-gray-700 h-4 w-16 mt-2 rounded-md animate-pulse mx-auto'></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Small (400-639px) - 6 slides */}
        <div className="hidden sm:block md:hidden">
          <Swiper
            modules={[Autoplay]}
            loop
            slidesPerView={6}
            spaceBetween={10}
            className='mySwiper myCustomSwiper'
          >
            {[...Array(6)].map((_, index) => (
              <SwiperSlide key={index} className='py-3'>
                <div className="flex flex-col items-center">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full w-[50px] h-[50px] mx-auto animate-pulse"></div>
                  <div className='bg-gray-200 dark:bg-gray-700 h-4 w-16 mt-2 rounded-md animate-pulse mx-auto'></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Medium (640-767px) - 6 slides */}
        <div className="hidden md:block lg:hidden">
          <Swiper
            modules={[Autoplay]}
            loop
            slidesPerView={6}
            spaceBetween={10}
            className='mySwiper myCustomSwiper'
          >
            {[...Array(6)].map((_, index) => (
              <SwiperSlide key={index} className='py-3'>
                <div className="flex flex-col items-center">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full w-[50px] h-[50px] mx-auto animate-pulse"></div>
                  <div className='bg-gray-200 dark:bg-gray-700 h-4 w-16 mt-2 rounded-md animate-pulse mx-auto'></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Large (768-1023px) - 8 slides */}
        <div className="hidden lg:block xl:hidden">
          <Swiper
            modules={[Autoplay]}
            loop
            slidesPerView={8}
            spaceBetween={10}
            className='mySwiper myCustomSwiper'
          >
            {[...Array(8)].map((_, index) => (
              <SwiperSlide key={index} className='py-3'>
                <div className="flex flex-col items-center">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full w-[50px] h-[50px] mx-auto animate-pulse"></div>
                  <div className='bg-gray-200 dark:bg-gray-700 h-4 w-16 mt-2 rounded-md animate-pulse mx-auto'></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* Extra Large (1024px+) - 15 slides */}
        <div className="hidden xl:block">
          <Swiper
            modules={[Autoplay]}
            loop
            slidesPerView={15}
            spaceBetween={10}
            className='mySwiper myCustomSwiper'
          >
            {[...Array(15)].map((_, index) => (
              <SwiperSlide key={index} className='py-3'>
                <div className="flex flex-col items-center">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full w-[50px] h-[50px] mx-auto animate-pulse"></div>
                  <div className='bg-gray-200 dark:bg-gray-700 h-4 w-16 mt-2 rounded-md animate-pulse mx-auto'></div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </div>
  </div>
</section>;
const FlagSliderSkeleton = () => <div className="relative container z-10 overflow-hidden">
  <div className="container w-full xs:pt-2 xs:px-2 px-0 overflow-hidden">
    {/* Skeleton Swiper */}
    <Swiper
      slidesPerView={5}
      breakpoints={{
        0: { slidesPerView: 3 },
        480: { slidesPerView: 3 },
        640: { slidesPerView: 3 },
        768: { slidesPerView: 5 },
        1024: { slidesPerView: 5 },
        1280: { slidesPerView: 5 }
      }}
      centeredSlides={true}
      spaceBetween={0}
      loop={true}
      modules={[Autoplay]}
      className="mySwiper w-full mx-auto pt-10 2xl:pb-20 sm:pb-10 pb-5 relative z-20 md:px-0 sm:px-[11px] xs:px-[8px] px-[6px] overflow-hidden"
    >
      {[...Array(5)].map((_, index) => (
        <SwiperSlide
          key={`skeleton-${index}`}
          className="
            transition-all duration-300 transform-gpu relative
            swiper-slide-active:scale-110 
            swiper-slide-next:scale-105 
            swiper-slide-prev:scale-105
            swiper-slide-active:z-10 
            swiper-slide-next:z-9 
            swiper-slide-prev:z-9
            swiper-slide-next:ml-[-20px] 
            swiper-slide-prev:mr-[-20px]
          "
        >
          <div className="w-full h-[400px] rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse relative flex items-center justify-center">
            <div className="text-transparent font-bold font-manrope text-center mt-8">MixDorm</div>
            <div className="absolute bottom-0 w-full bg-gradient-to-t from-black/40 to-transparent p-4 rounded-b-lg">
              <div className="w-1/2 h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2 animate-pulse"></div>
              <div className="w-1/3 h-3 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            </div>
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
  </div>
</div>
const ExpandSkeleton = () => <div className="flex justify-between flex-col-reverse md:flex-row md:justify-around lg:justify-between">


  {/* Left Content Skeleton */}
  <div className="w-full flex flex-wrap justify-end items-start lg:justify-end lg:items-end md:hidden">
    {/* Main Card */}
    <div className="relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] border border-gray-300 animate-pulse">
      <div className="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
      <div className="space-y-2">
        <div className="h-3 w-full bg-gray-200 rounded"></div>
        <div className="h-3 w-5/6 bg-gray-200 rounded"></div>
        <div className="h-3 w-4/5 bg-gray-200 rounded"></div>
        <div className="h-3 w-3/4 bg-gray-200 rounded"></div>
      </div>
      <div className="absolute -top-4 left-8 h-9 w-9 bg-gray-300 rounded-full"></div>
    </div>

    {/* Bottom Cards */}
    <div className="flex justify-start items-stretch gap-6 mt-8 w-full">
      {/* Noticeboard Card */}
      <div className="relative p-5 border border-gray-300 rounded-xl w-full max-w-[280px] animate-pulse">
        <div className="h-6 w-2/3 bg-gray-200 rounded mb-3"></div>
        <div className="space-y-2">
          <div className="h-3 w-full bg-gray-200 rounded"></div>
          <div className="h-3 w-5/6 bg-gray-200 rounded"></div>
          <div className="h-3 w-4/5 bg-gray-200 rounded"></div>
        </div>
        <div className="absolute -top-4 left-8 h-9 w-9 bg-gray-300 rounded-full"></div>
      </div>

      {/* Events Spotlight Card */}
      <div className="relative p-4 border border-gray-300 rounded-xl max-w-[217px] animate-pulse">
        <div className="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
        <div className="space-y-2">
          <div className="h-3 w-full bg-gray-200 rounded"></div>
          <div className="h-3 w-5/6 bg-gray-200 rounded"></div>
          <div className="h-3 w-4/5 bg-gray-200 rounded"></div>
        </div>
        <div className="absolute -top-4 left-8 h-9 w-9 bg-gray-300 rounded-full"></div>
      </div>
    </div>
  </div>

  {/* Desktop Left Content Skeleton */}
  <div className="w-full hidden md:flex flex-col justify-end items-start lg:items-end md:w-[90%] lg:w-[45%] mb-16 px-4 lg:px-0">
    {/* Main Card */}
    <div className="relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] border border-gray-300 animate-pulse">
      <div className="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
      <div className="space-y-2">
        <div className="h-4 w-full bg-gray-200 rounded"></div>
        <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
        <div className="h-4 w-4/5 bg-gray-200 rounded"></div>
        <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
      </div>
      <div className="absolute -top-4 left-8 h-9 w-9 bg-gray-300 rounded-full"></div>
    </div>

    {/* Bottom Cards */}
    <div className="flex flex-col sm:flex-row justify-start items-stretch gap-6 mt-8 w-full">
      {/* Noticeboard Card */}
      <div className="relative p-5 border border-gray-300 rounded-xl w-full max-w-[330px] animate-pulse">
        <div className="h-6 w-2/3 bg-gray-200 rounded mb-3"></div>
        <div className="space-y-2">
          <div className="h-4 w-full bg-gray-200 rounded"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
          <div className="h-4 w-4/5 bg-gray-200 rounded"></div>
        </div>
        <div className="absolute -top-4 left-8 h-9 w-9 bg-gray-300 rounded-full"></div>
      </div>

      {/* Events Spotlight Card */}
      <div className="relative p-4 border border-gray-300 rounded-xl w-full sm:max-w-[217px] animate-pulse">
        <div className="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
        <div className="space-y-2">
          <div className="h-4 w-full bg-gray-200 rounded"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
          <div className="h-4 w-4/5 bg-gray-200 rounded"></div>
        </div>
        <div className="absolute -top-4 left-8 h-9 w-9 bg-gray-300 rounded-full"></div>
      </div>
    </div>
  </div>

  {/* Phone Mockup Skeleton */}
  <div className="flex justify-center w-full sm:w-[90%] md:w-[70%] lg:w-[30%]">
    <div className="relative w-full max-w-[327px] h-[670px] rounded-3xl p-4 bg-gray-200 animate-pulse">
      <div className="absolute rounded-3xl top-2 left-3 w-[90%] max-w-[270px] h-[660px] bg-gray-300"></div>
    </div>
  </div>

  {/* Right Content Skeleton */}
  <div className="hidden lg:flex justify-start w-[30%]">
    <div className="flex flex-col justify-evenly">
      <div className="pb-56">
        <div className="space-y-3">
          <div className="h-4 w-full bg-gray-200 rounded"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
          <div className="h-4 w-4/5 bg-gray-200 rounded"></div>
          <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
        </div>
        <div className="h-10 w-40 bg-gray-300 rounded-full mt-4"></div>
      </div>

      {/* QR Code Skeleton */}
      <div className="mb-10 text-center flex items-end justify-end w-full">
        <div className="bg-gray-200 shadow-xl shadow-black/40 p-4 rounded-lg inline-block w-[155px] h-[147px] relative bottom-12">
        
          <div className="absolute h-[102px] w-[102px] left-7 bg-gray-300 rounded-lg"></div>
        </div>
      </div>
    </div>
  </div>
</div>;
const FeaturedHostelSkeleton = () => <div className="bg-gray-200 dark:bg-gray-800 w-full mt-4 animate-pulse">
  <section className='w-full md:pb-16 pb-10 lg:px-6 py-10'>
    <div className='container xs:py-10 py-4'>
      {/* Header Section Skeleton */}
      <div className='flex justify-between items-center my-8'>
        <div className="flex items-center gap-2">
          <div className="h-12 w-32 bg-gray-300 dark:bg-gray-700 rounded"></div>
          <div className="h-12 w-24 bg-gray-300 dark:bg-gray-700 rounded"></div>
        </div>
        
        {/* Mobile See All Button Skeleton */}
        <div className="sm:hidden block">
          <div className="h-10 w-24 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
        </div>

        {/* Slider Arrows Skeleton */}
        <div className="hidden md:flex gap-2">
          <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
          <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
        </div>
      </div>

      {/* Description Skeleton */}
      <div className='hidden sm:block mt-2'>
        <div className="h-4 w-full bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
        <div className="h-4 w-4/5 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
        <div className="h-4 w-3/4 bg-gray-300 dark:bg-gray-700 rounded"></div>
      </div>

      {/* Hostel Cards Skeleton */}
      <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white dark:bg-gray-700 rounded-lg overflow-hidden shadow-md animate-pulse">
            <div className="h-48 bg-gray-300 dark:bg-gray-600"></div>
            <div className="p-4">
              <div className="h-5 w-3/4 bg-gray-200 dark:bg-gray-600 rounded mb-3"></div>
              <div className="h-4 w-1/2 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
              <div className="flex justify-between mt-4">
                <div className="h-4 w-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-4 w-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* See All Button Skeleton */}
      <div className="text-center mt-10 hidden sm:block">
        <div className="inline-block h-12 w-36 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
      </div>
    </div>
  </section>
</div>;
const TravelActivitySkeleton = () => <section className="w-full bg-white md:py-16 sm:py-10 py-8 lg:px-6 relative">
  <div className="container relative">
    {/* Header section skeleton */}
    <div className="flex justify-between items-center mb-8">
      <div className="flex flex-col gap-2">
        <div className="h-12 w-48 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-12 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
      
      {/* Mobile button skeleton */}
      <div className="sm:hidden block">
        <div className="h-10 w-24 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
      
      {/* Navigation arrows skeleton */}
      <div className="hidden md:flex gap-2">
        <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
        <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
      </div>
    </div>
    
    {/* Description skeleton */}
    <div className="hidden sm:block mt-2 mb-8">
      <div className="h-4 w-full bg-gray-200 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-5/6 bg-gray-200 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-4/5 bg-gray-200 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-3/4 bg-gray-200 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
    </div>
    
    {/* Swiper skeleton */}
    <div className="overflow-hidden">
      <div className="flex gap-4">
        {[...Array(8)].map((_, index) => (
          <div key={index} className="flex flex-col items-center py-3 min-w-[80px]">
            <div className="w-20 h-20 xs:w-24 xs:h-24 rounded-full bg-gray-200 animate-pulse mb-4"></div>
            <div className="w-16 h-4 bg-gray-200 animate-pulse rounded"></div>
          </div>
        ))}
      </div>
    </div>
    
    {/* See All button skeleton */}
    <div className="text-center mt-10 hidden sm:block">
      <div className="inline-block h-12 w-36 bg-gray-200 rounded-full animate-pulse"></div>
    </div>
  </div>
</section>;
const EventSpotlightSkeleton = () => <div className="py-8 md:py-16 px-0 md:px-6 relative bg-gray-200 dark:bg-gray-800 animate-pulse">
  <section className="w-full xs:py-10 py-0">
    <div className="container relative">
      {/* Header Section */}
      <div className="flex justify-between items-center my-8">
        <div>
          <div className="h-12 w-48 bg-gray-300 dark:bg-gray-700 rounded"></div>
          <div className="h-12 w-32 bg-gray-300 dark:bg-gray-700 rounded mt-2"></div>
        </div>
        
        {/* Mobile Button */}
        <div className="sm:hidden block">
          <div className="h-10 w-24 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
        </div>
        
        {/* Navigation Arrows */}
        <div className="hidden md:flex gap-2">
          <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
          <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
        </div>
      </div>

      {/* Description */}
      <div className="hidden sm:block mt-2 mb-8">
        <div className="h-4 w-full bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
        <div className="h-4 w-5/6 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
        <div className="h-4 w-4/5 bg-gray-300 dark:bg-gray-700 rounded mb-2"></div>
        <div className="h-4 w-3/4 bg-gray-300 dark:bg-gray-700 rounded"></div>
      </div>

      {/* Event Cards Skeleton */}
      <div className="overflow-hidden py-4">
        <div className="flex gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="min-w-[280px] w-[280px]">
              <div className="relative w-full">
                {/* Date Badge */}
                <div className="absolute right-[14px] top-[14px] p-2 bg-gray-300 dark:bg-gray-600 rounded-md w-12 h-12 z-10"></div>
                
                {/* Event Info */}
                <div className="bg-gray-400 dark:bg-gray-600 bottom-0 absolute left-0 w-full py-4 px-4 z-10">
                  <div className="w-full">
                    <div className="flex gap-3 items-center mb-2">
                      <div className="flex -space-x-1">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="w-[26px] h-[26px] bg-gray-300 dark:bg-gray-500 rounded-full border border-white"></div>
                        ))}
                      </div>
                      <div className="h-4 w-20 bg-gray-300 dark:bg-gray-500 rounded"></div>
                    </div>
                    <div className="h-4 w-16 bg-gray-300 dark:bg-gray-500 rounded mb-3"></div>
                    <div className="flex justify-between">
                      <div className="h-6 w-3/5 bg-gray-300 dark:bg-gray-500 rounded"></div>
                      <div className="h-6 w-1/5 bg-gray-300 dark:bg-gray-500 rounded"></div>
                    </div>
                  </div>
                </div>
                
                {/* Image Placeholder */}
                <div className="min-h-[280px] md:min-h-[340px] bg-gray-300 dark:bg-gray-700 border-[6px] border-gray-100 dark:border-gray-600"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* See All Button */}
      <div className="text-center mt-5 sm:flex hidden justify-center">
        <div className="h-12 w-36 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
      </div>
    </div>
  </section>
</div>;
const OurlatestBlogSkeleton = () => <section className="w-full relative md:pb-16 pb-10 lg:px-6 py-8 bg-gray-100 dark:bg-gray-800">
  {/* Background gradient placeholders */}
  <div className="absolute -top-20 -left-8 w-40 h-40 bg-gray-300 dark:bg-gray-700 rounded-full blur-2xl opacity-30 animate-pulse"></div>
  <div className="absolute bottom-20 right-4 w-40 h-40 bg-gray-300 dark:bg-gray-700 rounded-full blur-2xl opacity-30 animate-pulse"></div>
  
  <div className="container relative">
    {/* Header section */}
    <div className="flex items-center justify-between w-full my-0 mb-8">
      <div>
        <div className="h-12 w-24 bg-gray-300 dark:bg-gray-700 rounded"></div>
        <div className="h-12 w-32 bg-gray-300 dark:bg-gray-700 rounded mt-2"></div>
      </div>
      
      {/* Mobile button */}
      <div className="sm:hidden block">
        <div className="h-10 w-24 bg-gray-300 dark:bg-gray-700 rounded-full animate-pulse"></div>
      </div>
      
      {/* Navigation arrows */}
      <div className="hidden sm:flex gap-2">
        <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full animate-pulse"></div>
        <div className="h-8 w-8 bg-gray-300 dark:bg-gray-700 rounded-full animate-pulse"></div>
      </div>
    </div>

    {/* Description */}
    <div className="hidden sm:block mt-2 mb-8">
      <div className="h-4 w-full bg-gray-300 dark:bg-gray-700 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-5/6 bg-gray-300 dark:bg-gray-700 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-4/5 bg-gray-300 dark:bg-gray-700 rounded mb-2 animate-pulse"></div>
      <div className="h-4 w-3/4 bg-gray-300 dark:bg-gray-700 rounded animate-pulse"></div>
    </div>

    {/* Blog cards skeleton */}
    <div className="flex gap-6 overflow-hidden py-4">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="min-w-[280px] w-[280px] flex-shrink-0">
          <div className="border-4 border-gray-200 dark:border-gray-600 overflow-hidden bg-white dark:bg-gray-700 shadow-md animate-pulse">
            {/* Image placeholder */}
            <div className="w-full h-[212px] bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
              <div className="text-white font-bold">MixDorm</div>
            </div>
            
            {/* Category and like button */}
            <div className="absolute top-3 left-3 bg-gray-400 dark:bg-gray-500 rounded-full w-16 h-6"></div>
            <div className="absolute top-3 right-3 bg-gray-400 dark:bg-gray-500 rounded-full w-6 h-6"></div>
            
            {/* Content */}
            <div className="py-4 px-2">
              <div className="flex gap-3 mb-2">
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                  <div className="w-20 h-4 bg-gray-300 dark:bg-gray-600 rounded-md"></div>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                  <div className="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded-md"></div>
                </div>
              </div>
              
              <div className="h-4 w-[90%] bg-gray-300 dark:bg-gray-600 rounded-md mb-2"></div>
              <div className="h-4 w-[70%] bg-gray-300 dark:bg-gray-600 rounded-md mb-4"></div>
              
              <div className="flex justify-between items-center mt-6">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                  <div className="w-20 h-4 bg-gray-300 dark:bg-gray-600 rounded-md"></div>
                </div>
                <div className="w-24 h-8 bg-gray-300 dark:bg-gray-600 rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>

    {/* See All button */}
    <div className="text-center mt-10 hidden sm:block">
      <div className="inline-block h-12 w-36 bg-gray-300 dark:bg-gray-700 rounded-full animate-pulse"></div>
    </div>
  </div>
</section>;
const ChannelPartnerSkeleton = () => <div className="py-16 md:py-16 px-0 md:px-6 relative bg-gray-200 dark:bg-gray-800 animate-pulse">
  <div className="container flex flex-col lg:flex-row">
    {/* Left Content Skeleton */}
    <div className="space-y-6 w-[100%] lg:w-[45%] flex flex-col justify-center mb-4 lg:mb-0">
      {/* Title */}
      <div className="h-16 w-64 bg-gray-300 dark:bg-gray-700 rounded"></div>
      <div className="h-16 w-48 bg-gray-300 dark:bg-gray-700 rounded"></div>
      
      {/* Description */}
      <div className="hidden lg:block space-y-2">
        <div className="h-4 w-full bg-gray-300 dark:bg-gray-700 rounded"></div>
        <div className="h-4 w-5/6 bg-gray-300 dark:bg-gray-700 rounded"></div>
        <div className="h-4 w-4/5 bg-gray-300 dark:bg-gray-700 rounded"></div>
        <div className="h-4 w-3/4 bg-gray-300 dark:bg-gray-700 rounded"></div>
      </div>
      
      {/* Button */}
      <div className="hidden lg:block h-12 w-36 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
    </div>

    {/* Right Grid Skeleton */}
    <div className="grid grid-cols-3 xs:grid-cols-4 xs:gap-4 gap-2">
      {[...Array(12)].map((_, index) => (
        <div 
          key={index}
          className="bg-gray-300 dark:bg-gray-700 
                   xxs:w-[100px] xs:w-[110px] sm:w-[140px] lg:w-[130px] xl:w-[170px]
                   xxs:h-[90px] xs:h-[100px] sm:h-[100px] md:h-[120px] 
                   shadow-md p-4 border border-gray-400 dark:border-gray-600"
        >
          <div className="relative w-full h-full">
            <div className="absolute inset-0 bg-gray-400 dark:bg-gray-600 rounded-md animate-pulse"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
</div>;






const HomeScreen = () => {
  const images = [
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/bg-home-page.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new2.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new3.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new4.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new5.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new6.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new7.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new8.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new9.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new10.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new11.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new12.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new13.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new14.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new15.webp`,
  ];
  // const [currentImage, setCurrentImage] = useState(0);
  // const [fade, setFade] = useState(false);

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setFade(true); // Start blink effect

  //     setTimeout(() => {
  //       setCurrentImage((prev) => (prev + 1) % images.length);
  //       setFade(false); // Remove blink effect
  //     }, 50); // Blink effect duration
  //   }, 4000); // Image changes every 4 seconds

  //   return () => clearInterval(interval);
  // }, []);

  // const [[currentImage, direction], setCurrentImage] = useState([0, 1]);

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setCurrentImage(([prevImage, _]) => [
  //       (prevImage + 1) % images.length,
  //       1,
  //     ]);
  //   }, 4000);

  //   return () => clearInterval(interval);
  // }, []);

  /* code commented */

  // const [allImagesLoaded, setAllImagesLoaded] = useState(false);

  // useEffect(() => {
  //   let loadedCount = 0;

  //   const preloadCount = images.length - 1;

  //   images.slice(1).forEach((src) => {
  //     const img = new window.Image();
  //     img.src = src;
  //     img.onload = img.onerror = () => {
  //       loadedCount++;
  //       if (loadedCount === preloadCount) {
  //         setAllImagesLoaded(true);
  //       }
  //     };
  //   });
  // }, []);

  // useEffect(() => {
  //   if (!allImagesLoaded) return;

  //   const interval = setInterval(() => {
  //     setCurrentImage(([prevImage, _]) => [
  //       (prevImage + 1) % images.length,
  //       1,
  //     ]);
  //   }, 3000);

  //   return () => clearInterval(interval);
  // }, [allImagesLoaded]);

  return (
    <>
      <Head>
        <link rel="canonical" href="https://mixdorm.com/" />
        <link rel="preconnect" href={process.env.NEXT_PUBLIC_S3_URL_FE} />
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_S3_URL_FE} />
        <link rel="preload" as="image" href={images[0]} />
      </Head>

      <div className="overflow-hidden">
        {" "}
        <div className="relative bg-cover">

          {/* {!allImagesLoaded && ( */}
          <Image
            src={images[0]}
            alt="Initial Slide"
            fill
            priority
            fetchPriority="high"
            quality={75}          
            sizes="100vw"
            className="object-cover absolute top-0 left-0 w-full h-screen z-0"
         
            // style={{ opacity: allImagesLoaded ? 0 : 1 }}
          />
          {/* )} */}

          {/* ✅ Animated slideshow after all images load */}
          {/* {allImagesLoaded && (
            <AnimatePresence custom={direction} initial={false}>
              <motion.div
                key={`${currentImage}-${direction}`}
                custom={direction}
                variants={slideVariants}
                initial="enter"
                animate="center"
                exit="exit"
                style={{ zIndex: 1 }}
                className="absolute top-0 left-0 w-full h-full"
              >
                <Image
                  src={images[currentImage]}
                  alt={`Slide ${currentImage}`}
                  fill
                  priority={currentImage === 0}
                  fetchPriority={currentImage === 0 ? "high" : "auto"}
                  sizes="100vw"
                  className="object-cover"
                />
              </motion.div>
            </AnimatePresence>
          )} */}
          <div
            className="absolute inset-0 bg-black/50 w-full h-screen"
            style={{ zIndex: 2 }}
          />

          <Navbar />
          <Banner />
          <RecentSearch />
          <ExploreWorld />
          <FlagSlider />
        </div>
        <Expand />
        <FeaturedHostel />
        <TravelActivity />
        {/* <Buddies /> */}
        {/* <Event /> */}
        {/* <Hostel /> */}
        <EventSpotlight />
        <OurlatestBlog />
        <ChannelPartner />
        {/* <LensGallery /> */}
        {/* <DownloadApp /> */}
        <FAQs />
      </div>
    </>
  );
};

export default HomeScreen;
