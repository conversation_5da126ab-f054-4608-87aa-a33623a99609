"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "(pages-dir-node)/./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-N7VRSSHG');`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Mixdorm | Best Affordable Hostel Booking Worldwide\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        itemProp: \"image\",\n                        content: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/ogimg.png?v=2`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image:width\",\n                        content: \"300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image:height\",\n                        content: \"300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:url\",\n                        content: \"https://www.mixdorm.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/x-icon\",\n                        href: `/fav11.png`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"Mixdorm - Your Next Epic Stay\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: \"Explore the world with Mixdorm! Big savings on hostels, dorms & shared stays. Hostel booking made easy—stay cheap, meet people, travel smarter!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/ogimg.png?v=2`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image:secure_url\",\n                        content: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/ogimg.png?v=2`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image:width\",\n                        content: \"1200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image:height\",\n                        content: \"630\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image:type\",\n                        content: \"image/png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:url\",\n                        content: \"https://www.mixdorm.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"Mixdorm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:locale\",\n                        content: \"en_US\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"author\",\n                        content: \"Mixdorm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"title\",\n                        content: \"Mixdorm - Your Next Epic Stay\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"Hostels,Dorms,Hotels,Accommodation,Travel,Budget Hotels\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"Mixdorm - Your Next Epic Stay\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: \"Explore the world with Mixdorm! Big savings on hostels, dorms & shared stays. Hostel booking made easy—stay cheap, meet people, travel smarter!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/ogimg.png?v=2`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:site\",\n                        content: \"@mixdorm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            src: \"https://www.googletagmanager.com/ns.html?id=GTM-N7VRSSHG\",\n                            height: \"0\",\n                            width: \"0\",\n                            style: {\n                                display: \"none\",\n                                visibility: \"hidden\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\pages\\\\_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(pages-dir-node)/./pages/_document.js")));
module.exports = __webpack_exports__;

})();