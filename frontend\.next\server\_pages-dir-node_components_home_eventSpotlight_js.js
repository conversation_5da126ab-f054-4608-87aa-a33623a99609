"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_home_eventSpotlight_js";
exports.ids = ["_pages-dir-node_components_home_eventSpotlight_js"];
exports.modules = {

/***/ "(pages-dir-node)/./components/home/<USER>":
/*!*******************************************!*\
  !*** ./components/home/<USER>
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"swiper/react\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/modules */ \"swiper/modules\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/services/webflowServices */ \"(pages-dir-node)/./services/webflowServices.jsx\");\n/* harmony import */ var _navbarContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./navbarContext */ \"(pages-dir-node)/./components/home/<USER>");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/navigation */ \"(pages-dir-node)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var world_countries__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! world-countries */ \"world-countries\");\n/* harmony import */ var world_countries__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(world_countries__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swiper_react__WEBPACK_IMPORTED_MODULE_3__, swiper_modules__WEBPACK_IMPORTED_MODULE_4__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_6__]);\n([swiper_react__WEBPACK_IMPORTED_MODULE_3__, swiper_modules__WEBPACK_IMPORTED_MODULE_4__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unescaped-entities */ \n\n\n\n\n\n\n\n\n\n// import { motion } from \"framer-motion\";\n\nconst EventSpotlight = ()=>{\n    // eslint-disable-next-line no-unused-vars\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activityData, setActivityData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // eslint-disable-next-line no-unused-vars\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // eslint-disable-next-line no-unused-vars\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currencyData, setCurrencyData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const limit = 10;\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { token } = (0,_navbarContext__WEBPACK_IMPORTED_MODULE_7__.useNavbar)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [imgLoaded, setImgLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imgError, setImgError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Init arrays if not set (especially after data fetch)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventSpotlight.useEffect\": ()=>{\n            if (activityData.length > 0) {\n                setImgLoaded(Array(activityData.length).fill(false));\n                setImgError(Array(activityData.length).fill(false));\n            }\n        }\n    }[\"EventSpotlight.useEffect\"], [\n        activityData\n    ]);\n    // const [isMobile, setIsMobile] = useState(false);\n    // useEffect(() => {\n    //   const checkMobile = () => {\n    //     setIsMobile(typeof window !== 'undefined' && window.innerWidth <= 768);\n    //   };\n    //   // Initial check\n    //   checkMobile();\n    //   // Add resize listener\n    //   if (typeof window !== 'undefined') {\n    //     window.addEventListener('resize', checkMobile);\n    //     return () => window.removeEventListener('resize', checkMobile);\n    //   }\n    // }, []);\n    const profileImages = [\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/usericon1.jpeg`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/usericon2.jpeg`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/usericon3.jpeg`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/usericon4.jpeg`\n    ];\n    const flagImages = [\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/flag14.png`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/flag15.png`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/flag13.png`,\n        `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/flag11.png`\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventSpotlight.useEffect\": ()=>{\n            const fetchActivityData = {\n                \"EventSpotlight.useEffect.fetchActivityData\": async ()=>{\n                    try {\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_6__.eventApi)(currentPage, limit);\n                        setActivityData(response?.data?.data?.events || []);\n                        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);\n                        setIsLoading(false);\n                    } catch (error) {\n                        console.error(\"Error fetching event data:\", error);\n                    }\n                }\n            }[\"EventSpotlight.useEffect.fetchActivityData\"];\n            if (!isFirstRender.current) {\n                fetchActivityData();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"EventSpotlight.useEffect\"], [\n        currentPage,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventSpotlight.useEffect\": ()=>{\n            const fetchCurrencyData = {\n                \"EventSpotlight.useEffect.fetchCurrencyData\": ()=>{\n                    try {\n                        const currencyMap = {};\n                        world_countries__WEBPACK_IMPORTED_MODULE_9___default().forEach({\n                            \"EventSpotlight.useEffect.fetchCurrencyData\": (country)=>{\n                                if (country.currencies) {\n                                    const currencyCode = Object.keys(country.currencies)[0];\n                                    const currencyInfo = country.currencies[currencyCode];\n                                    if (currencyInfo && currencyInfo.symbol) {\n                                        currencyMap[currencyCode] = currencyInfo.symbol;\n                                    }\n                                }\n                            }\n                        }[\"EventSpotlight.useEffect.fetchCurrencyData\"]);\n                        setCurrencyData(currencyMap);\n                        setIsLoading(false);\n                    } catch (error) {\n                        console.error(\"Error processing currency data:\", error);\n                    }\n                }\n            }[\"EventSpotlight.useEffect.fetchCurrencyData\"];\n            fetchCurrencyData();\n        }\n    }[\"EventSpotlight.useEffect\"], []);\n    const getCurrencySymbol = (currencyCode)=>{\n        return currencyData[currencyCode] || currencyCode;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-8 md:py-16 px-0 md:px-6 relative bg-repeat-round w-full\",\n        style: {\n            backgroundImage: `url(${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/left-cross-bg.webp)`,\n            backgroundSize: \"cover\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"w-full xs:py-10 py-0\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center my-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \" text-white font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl\",\n                                        children: [\n                                            \"Events\",\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Spotlight\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center sm:hidden block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/discover-event\",\n                                    \"aria-label\": \"See all discoverable events\",\n                                    className: \"text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750\",\n                                    prefetch: false,\n                                    children: \"See All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `gap-2 ${activityData?.length > 4 ? \"xl:flex\" : \"xl:hidden\"} ${activityData?.length > 3.5 ? \"lg:flex\" : \"lg:hidden\"} ${activityData?.length > 2.5 ? \"md:flex\" : \"md:hidden\"} ${activityData?.length > 2 ? \"sm:flex\" : \"sm:hidden\"} ${activityData?.length > 1.5 ? \"hidden\" : \"hidden\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"slider-button-prev cursor-pointer custom-nav\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ArrowLeft, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                            lineNumber: 169,\n                                            columnNumber: 77\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"slider-button-next cursor-pointer custom-nav\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ArrowRight, {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 77\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"hidden sm:block mt-2 text-base font-medium text-white/60 font-manrope mb-2 w-[80%]\",\n                        children: [\n                            \"Discover top-rated hostels across the globe that go beyond just a bed — offering \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/discover-event\",\n                                className: \"text-primary-blue\",\n                                children: \" social events, \"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                lineNumber: 181,\n                                columnNumber: 94\n                            }, undefined),\n                            \" cultural experiences, and unforgettable moments. Whether you're after a lively party hostel, a peaceful retreat, or an adventure-packed base, these featured hostels combine affordability, comfort, and community vibes. Explore our top 8 hostels known for hosting local events, pub crawls, cooking nights, and more — all with unique amenities and budget-friendly rates. Join the fun, meet fellow solo backpackers, and make every night count — only with Mixdorm\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                            modules: [\n                                swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Autoplay,\n                                swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Navigation\n                            ],\n                            // autoplay={{ delay: 1500, pauseOnMouseEnter: true }}\n                            slidesPerView: 18,\n                            navigation: {\n                                prevEl: \".slider-button-prev\",\n                                nextEl: \".slider-button-next\"\n                            },\n                            loop: true,\n                            speed: 1000,\n                            spaceBetween: 12,\n                            className: \"mySwiper overflow-hidden py-4\",\n                            breakpoints: {\n                                0: {\n                                    slidesPerView: 1.5,\n                                    spaceBetween: 10\n                                },\n                                480: {\n                                    slidesPerView: 1.4,\n                                    spaceBetween: 10\n                                },\n                                640: {\n                                    slidesPerView: 2,\n                                    spaceBetween: 10\n                                },\n                                768: {\n                                    slidesPerView: 2.5,\n                                    spaceBetween: 20\n                                },\n                                1024: {\n                                    slidesPerView: 3.5\n                                },\n                                1280: {\n                                    slidesPerView: 4\n                                }\n                            },\n                            children: isLoading ? Array.from({\n                                length: 4\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full animate-pulse bg-gray-300 z-20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-[14px] top-[14px] p-2 bg-gray-300 text-transparent rounded-md w-12 h-12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/40 bottom-0 absolute left-0 w-full py-4 px-4 z-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3 items-center mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex -space-x-1\",\n                                                                    children: [\n                                                                        1,\n                                                                        2,\n                                                                        3\n                                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-[26px] h-[26px] bg-gray-300 rounded-full border border-white\"\n                                                                        }, i, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 31\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm bg-gray-300 text-transparent rounded-md w-20 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"uppercase text-white font-roboto font-normal text-sm my-3\",\n                                                            children: \"SHOW\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-300 w-[60%] h-6 rounded-md\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-300 w-[20%] h-6 rounded-md\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"xl:min-h-[410px] lg:min-h-[380px] md:min-h-[340px] xs:min-h-[310px] min-h-[280px] bg-gray-300 border-[6px] border-slate-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, `skeleton-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                    lineNumber: 237,\n                                    columnNumber: 17\n                                }, undefined)) : activityData.map((slide, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                    className: \"\",\n                                    onClick: ()=>setSelectedImage(index),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full ease-in-out transition-all duration-300 hover:scale-105\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-black font-thin text-center absolute right-[14px] top-[14px] p-2 bg-[#40E0D0] uppercase text-[13px] sm:w-[60px] sm:h-[60px] w-12 z-10\",\n                                                children: [\n                                                    new Date(slide?.startDate).toLocaleString(\"en-US\", {\n                                                        month: \"short\"\n                                                    }).toUpperCase(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block sm:text-[26px] text-lg font-bold sm:leading-5 leading-3\",\n                                                        children: new Date(slide?.startDate).getDate()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-black/40 bottom-0 absolute left-0 w-full py-4 sm:px-6 px-4 z-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3 items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                    href: \"/membership\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex -space-x-1\",\n                                                                        children: profileImages.map((profileSrc, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"relative w-[26px] h-[26px] 2xl:w-[32px] 2xl:h-[32px]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                            src: profileSrc,\n                                                                                            alt: \"User\",\n                                                                                            fill: true,\n                                                                                            loading: \"lazy\",\n                                                                                            className: `rounded-full border border-white object-cover ${isLoading ? \"bg-slate-200 animate-pulse\" : \"bg-slate-200\"}`,\n                                                                                            sizes: \"(min-width: 1536px) 32px, 26px\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                            lineNumber: 288,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                        lineNumber: 287,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"absolute -bottom-1 left-[16px] 2xl:left-[18px] z-10 w-[12px] h-[12px] 2xl:w-[14px] 2xl:h-[14px]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                            src: flagImages[i],\n                                                                                            alt: \"Flag\",\n                                                                                            fill: true,\n                                                                                            loading: \"lazy\",\n                                                                                            className: \"rounded-full object-cover\",\n                                                                                            sizes: \"(min-width: 1536px) 14px, 12px\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                            lineNumber: 298,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                        lineNumber: 297,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    i === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"absolute top-0 left-0 w-full h-full rounded-full overflow-hidden z-0 cursor-pointer border\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"absolute inset-0 backdrop-blur-sm z-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                                lineNumber: 310,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"relative flex items-center justify-center w-full h-full z-10 text-sm text-white font-extrabold\",\n                                                                                                children: \"+4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                                lineNumber: 313,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, i, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 33\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white text-sm font-roboto\",\n                                                                    children: \"20+ Going\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"uppercase text-white font-roboto font-normal text-sm my-3\",\n                                                            children: \"SHOW\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full flex mt-0 justify-between items-start xl:items-start relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"comman-tooltip before:!top-[-30px] before:!left-[50%]\",\n                                                                        \"data-title\": slide?.title,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"md:text-2xl xs:text-lg text-md font-normal font-roboto text-white line-clamp-1\",\n                                                                            children: slide?.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-normal font-roboto text-white text-end \",\n                                                                    children: [\n                                                                        getCurrencySymbol(slide?.currency || \"USD\"),\n                                                                        slide?.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            (!imgLoaded[index] || imgError[index]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-slate-200 xl:min-h-[410px] lg:min-h-[380px] md:min-h-[340px] xs:min-h-[310px] min-h-[280px] flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-white font-bold font-manrope\",\n                                                    children: \"MixDorm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 346,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                src: slide?.attachment?.[0]?.url || `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/event_1.jpg`,\n                                                alt: `Slide ${slide?._id}`,\n                                                width: 402,\n                                                height: 409,\n                                                quality: 90,\n                                                loading: \"lazy\",\n                                                sizes: \"(max-width: 480px) 90vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 402px\",\n                                                className: \"xl:min-h-[410px] lg:min-h-[380px] md:min-h-[340px] xs:min-h-[310px] min-h-[280px] object-cover border-[6px] border-slate-105 bg-slate-200\",\n                                                onLoad: ()=>setImgLoaded((prev)=>{\n                                                        const updated = [\n                                                            ...prev\n                                                        ];\n                                                        updated[index] = true;\n                                                        return updated;\n                                                    }),\n                                                onError: ()=>{\n                                                    setImgError((prev)=>{\n                                                        const updated = [\n                                                            ...prev\n                                                        ];\n                                                        updated[index] = true;\n                                                        return updated;\n                                                    });\n                                                    setImgLoaded((prev)=>{\n                                                        const updated = [\n                                                            ...prev\n                                                        ];\n                                                        updated[index] = true;\n                                                        return updated;\n                                                    });\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                                lineNumber: 350,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, slide?._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-5 sm:flex hidden justify-center items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                            href: \"/discover-event\",\n                            \"aria-label\": \"See all discoverable events\",\n                            className: \"text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750\",\n                            prefetch: false,\n                            children: \"See All\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\home\\\\eventSpotlight.js\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EventSpotlight);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/home/<USER>");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowLeft,ArrowRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "(pages-dir-node)/./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;