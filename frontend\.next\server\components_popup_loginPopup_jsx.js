"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_popup_loginPopup_jsx";
exports.ids = ["components_popup_loginPopup_jsx"];
exports.modules = {

/***/ "./components/popup/loginPopup.jsx":
/*!*****************************************!*\
  !*** ./components/popup/loginPopup.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/webflowServices */ \"./services/webflowServices.jsx\");\n/* harmony import */ var _barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@mui/material */ \"__barrel_optimize__?names=Button!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=FaApple,FaFacebook!=!react-icons/fa */ \"__barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-oauth/google */ \"@react-oauth/google\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-apple-login */ \"react-apple-login\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_apple_login__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-facebook */ \"react-facebook\");\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_facebook__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _signinPopup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./signinPopup */ \"./components/popup/signinPopup.jsx\");\n/* harmony import */ var _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../socialLogin/googleSocialLogin */ \"./components/socialLogin/googleSocialLogin.jsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/api */ \"./utils/api.js\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/firebaseConfig */ \"./utils/firebaseConfig.jsx\");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/services/ownerflowServices */ \"./services/ownerflowServices.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\n/* harmony import */ var _mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/icons-material/VisibilityOutlined */ \"./node_modules/@mui/icons-material/VisibilityOutlined.js\");\n/* harmony import */ var _mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/VisibilityOffOutlined */ \"./node_modules/@mui/icons-material/VisibilityOffOutlined.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_5__, _signinPopup__WEBPACK_IMPORTED_MODULE_8__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, axios__WEBPACK_IMPORTED_MODULE_5__, _signinPopup__WEBPACK_IMPORTED_MODULE_8__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst LoginPopup = ({ isOpen, onClose })=>{\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPopup.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginPopup.useEffect\"], []);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fullname: \"\",\n        phoneNumber: \"\",\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSignInOpen, setIsSignInOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(isOpen);\n    const [isSignUpOpen, setIsSignUpOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_15__.useNavbar)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSignup = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password || !formData.fullname || !formData.phoneNumber) {\n            setError(\"All fields are required.\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.registerApi)({\n                name: formData?.fullname,\n                email: formData?.email,\n                password: formData?.password,\n                contact: formData?.phoneNumber\n            });\n            if (response?.data?.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(response?.data?.message || \"Registration successful!\");\n                setFormData({\n                    fullname: \"\",\n                    phoneNumber: \"\",\n                    email: \"\",\n                    password: \"\"\n                });\n                router.push(\"/verifyotp\");\n                onClose(); // Close LoginPopup\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(response?.data?.message || \"Something went wrong, please try again.\");\n            }\n        } catch (error) {\n            setError(error.response?.data?.message || \"An error occurred, please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isDisabled = loading || !formData.email || !formData.password || !formData.fullname || !formData.phoneNumber;\n    const handleFacebookSuccess = async (response)=>{\n        try {\n            const { accessToken } = response.authResponse;\n            // Manually fetch user profile from Facebook using the access token\n            const profileRes = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(`https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`);\n            const { name, email } = profileRes.data;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_10__.BASE_URL}/auth/social/login`, {\n                token: accessToken,\n                role: \"user\",\n                email,\n                name\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"name\", name);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"email\", res?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"contact\", res?.data?.data?.user?.contact);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                onClose();\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleFacebookFailure = (error)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Facebook login failed: \" + error.message);\n    };\n    const handleAppleSuccess = async (response)=>{\n        try {\n            const { authorization, user } = response;\n            const { id_token } = authorization;\n            const name = user?.name ? `${user.name.firstName} ${user.name.lastName}` : null;\n            const email = user?.email || null;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_10__.BASE_URL}/auth/social/login`, {\n                token: id_token,\n                role: \"user\",\n                provider: \"apple\",\n                name: name,\n                email: email\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"name\", name || `${res.data.data.user.name.first} ${res.data.data.user.name.last}`);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"email\", res?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"contact\", res?.data?.data?.user?.contact);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_13__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_11__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_14__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n                router.push(\"/\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleAppleFailure = (response)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Apple login failed: \" + response.error);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPopup.useEffect\": ()=>{\n            setIsSignInOpen(isOpen);\n        }\n    }[\"LoginPopup.useEffect\"], [\n        isOpen\n    ]);\n    const openSignInForm = ()=>{\n        setIsSignInOpen(true);\n        setIsSignUpOpen(false);\n    };\n    const openSignUpForm = ()=>{\n        setIsSignInOpen(false);\n        setIsSignUpOpen(true);\n    };\n    const closePopup = ()=>{\n        setIsSignInOpen(false);\n        setIsSignUpOpen(false);\n        onClose(); // Ensure the parent is informed about closing\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            isSignInOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signinPopup__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: isSignInOpen,\n                onClose: closePopup,\n                openSignUpForm: openSignUpForm\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined) : isSignUpOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl w-[95%] max-w-2xl p-5 font-manrope\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: closePopup,\n                                className: \"text-black hover:text-gray-600 transition duration-150\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 244,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col justify-center items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#40E0D0] flex text-2xl font-extrabold mb-2\",\n                                children: [\n                                    \"Mix\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-black text-2xl font-extrabold \",\n                                        children: \"Dorm\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                lineNumber: 253,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 252,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSignup,\n                            className: \"mx-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 mb-4 text-center\",\n                                    children: \"\\uD83D\\uDC4B Sign Up your Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"fullname\",\n                                        name: \"fullname\",\n                                        value: formData.fullname,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Full Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"phoneNumber\",\n                                        name: \"phoneNumber\",\n                                        value: formData.phoneNumber,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Phone Number\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        name: \"email\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            id: \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            className: \"w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                            placeholder: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute right-4 top-[15px] text-gray-300 transform\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: `w-full font-semibold text-white py-4 rounded-full transition duration-200 ${isDisabled ? \"bg-gray-400 cursor-not-allowed\" : \"bg-[#40E0D0] hover:bg-[#38c7b7]\"}`,\n                                    disabled: isDisabled,\n                                    children: loading ? \"Signing up...\" : \"Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, undefined),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-center text-red-600\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 257,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mx-8 space-x-4 my-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 border-t border-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Or\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 border-t border-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 358,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-x-4 justify-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_7__.FacebookProvider, {\n                                    appId: \"3141101952691007\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_7__.LoginButton, {\n                                        scope: \"public_profile,email\",\n                                        onSuccess: handleFacebookSuccess,\n                                        onError: handleFacebookFailure,\n                                        className: \"min-w-0 p-0 text-3xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_18__.FaFacebook, {\n                                            className: \"text-[#0866ff]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_oauth_google__WEBPACK_IMPORTED_MODULE_4__.GoogleOAuthProvider, {\n                                    clientId: \"179422288655-1cm80o7s30fk0p1esfh3an4s15duj2u9.apps.googleusercontent.com\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        role: \"user\",\n                                        closeLoginPopup: ()=>closePopup()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_apple_login__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                    clientId: \"com.mixdorm.app\",\n                                    redirectURI: \"https://mixdorm.com\",\n                                    responseType: \"code id_token\",\n                                    scope: \"name email\",\n                                    render: (renderProps)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                            className: \"min-w-0 p-0 text-3xl text-black mb-1\",\n                                            onClick: renderProps.onClick,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_18__.FaApple, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 21\n                                        }, void 0),\n                                    onSuccess: handleAppleSuccess,\n                                    onError: handleAppleFailure\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 363,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-basec text-center text-sm text-gray-400\",\n                            children: [\n                                \"Already have an account?\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"text-primary-blue font-medium ml-1 cursor-pointer \",\n                                    onClick: openSignInForm,\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                            lineNumber: 403,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                    lineNumber: 243,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\loginPopup.jsx\",\n                lineNumber: 242,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoginPopup);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/popup/loginPopup.jsx\n");

/***/ }),

/***/ "./components/popup/signinPopup.jsx":
/*!******************************************!*\
  !*** ./components/popup/signinPopup.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=FaApple,FaFacebook!=!react-icons/fa */ \"__barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!@mui/material */ \"__barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/webflowServices */ \"./services/webflowServices.jsx\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/firebaseConfig */ \"./utils/firebaseConfig.jsx\");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/ownerflowServices */ \"./services/ownerflowServices.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-facebook */ \"react-facebook\");\n/* harmony import */ var react_facebook__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_facebook__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../socialLogin/googleSocialLogin */ \"./components/socialLogin/googleSocialLogin.jsx\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-apple-login */ \"react-apple-login\");\n/* harmony import */ var react_apple_login__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react_apple_login__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @react-oauth/google */ \"@react-oauth/google\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @mui/icons-material/VisibilityOutlined */ \"./node_modules/@mui/icons-material/VisibilityOutlined.js\");\n/* harmony import */ var _mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @mui/icons-material/VisibilityOffOutlined */ \"./node_modules/@mui/icons-material/VisibilityOffOutlined.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/utils/api */ \"./utils/api.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__, axios__WEBPACK_IMPORTED_MODULE_14__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__, _socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__, axios__WEBPACK_IMPORTED_MODULE_14__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable react/no-unescaped-entities */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst SignInPopup = ({ isOpen, onClose, openSignUpForm, closeLoginPopup })=>{\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value.trim()\n        });\n    };\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignInPopup.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"SignInPopup.useEffect\"], []);\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_8__.useNavbar)();\n    const handleSignIn = async (e)=>{\n        e.preventDefault();\n        if (!formData.email || !formData.password) {\n            setError(\"All fields are required.\");\n            return;\n        }\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.logInApi)({\n                email: formData.email,\n                password: formData.password,\n                role: \"user\"\n            });\n            console.log(\"response\", response);\n            if (response?.status === 403) {\n                try {\n                    const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.resendOtpApi)({\n                        email: formData?.email\n                    });\n                    console.log(\"response22\", response);\n                    if (response?.data?.status) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(`We’ve sent a verification code to ${formData?.email}. Please enter this code to continue.`);\n                        router.push(\"/verifyotp\");\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(response.data.message || \"Failed to resend verification code.\");\n                    }\n                    onClose();\n                } catch (error) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data?.message || \"An error occurred while resending OTP.\");\n                    console.error(\"Resend OTP error:\", error);\n                }\n            } else if (response?.data?.status) {\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", response?.data?.data?.user?.name.first);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", response?.data?.data?.user?._id);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", response?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"email\", response?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"contact\", response?.data?.data?.user?.contact);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(response?.data?.data?.token);\n                updateUserStatus(response?.data?.data?.token);\n                updateUserRole(response?.data?.data?.user?.role);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(response?.data?.message);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: response?.data?.data?.user?._id\n                    });\n                }\n                onClose(); // Close SignInPopup\n                closeLoginPopup(); // Close LoginPopup\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(response.data.message || \"An error occurred.\");\n            }\n        } catch (error) {\n            setError(error.response?.data?.message || \"An error occurred, please try again.\");\n            console.error(\"Login error:\", error.response);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const isDisabled = loading || !formData.email || !formData.password;\n    const handleFacebookSuccess = async (response)=>{\n        try {\n            const { accessToken } = response.authResponse;\n            // Manually fetch user profile from Facebook using the access token\n            const profileRes = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].get(`https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`);\n            const { name, email } = profileRes.data;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_15__.BASE_URL}/auth/social/login`, {\n                token: accessToken,\n                role: \"user\",\n                email,\n                name\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", name);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                onClose();\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleFacebookFailure = (error)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Facebook login failed: \" + error.message);\n    };\n    const handleAppleSuccess = async (response)=>{\n        try {\n            const { authorization, user } = response;\n            const { id_token } = authorization;\n            const name = user?.name ? `${user.name.firstName} ${user.name.lastName}` : null;\n            const email = user?.email || null;\n            const res = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_15__.BASE_URL}/auth/social/login`, {\n                token: id_token,\n                role: \"user\",\n                provider: \"apple\",\n                name: name,\n                email: email\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", name || `${res.data.data.user.name.first} ${res.data.data.user.name.last}`);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_6__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_7__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n                router.push(\"/\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(error.response?.data.message || error.message);\n        }\n    };\n    const handleAppleFailure = (response)=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"Apple login failed: \" + response.error);\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl w-[90%] max-w-md p-5 font-manrope\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-black hover:text-gray-600 transition duration-150\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col justify-center items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[#40E0D0] flex text-2xl font-extrabold sm:mb-2\",\n                            children: [\n                                \"Mix\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-black text-2xl font-extrabold \",\n                                    children: \"Dorm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 16\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSignIn,\n                        className: \"mx-1 sm:mx-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-[16px] sm:text-xl font-bold text-gray-800 sm:mb-6 mb-4 text-center\",\n                                children: \"\\uD83D\\uDC4B Login to your Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:mb-5 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    id: \"email\",\n                                    name: \"email\",\n                                    value: formData.email,\n                                    onChange: handleChange,\n                                    className: \"w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                    placeholder: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative sm:mb-5 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: showPassword ? \"text\" : \"password\",\n                                        id: \"password\",\n                                        name: \"password\",\n                                        value: formData.password,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500\",\n                                        placeholder: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"absolute right-4 top-[15px] text-gray-300 transform\",\n                                        onClick: ()=>setShowPassword(!showPassword),\n                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOutlined__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_VisibilityOffOutlined__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center sm:mb-6 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__.FormControlLabel, {\n                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__.Checkbox, {\n                                            className: \"text-[12px] sm:text-sm mt-[4px] block sm:mt-0\",\n                                            sx: {\n                                                color: \"rgba(0,0,0,0.4)\",\n                                                \"&.Mui-checked\": {\n                                                    color: \"#40E0D0\"\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \" text-[12px] sm:text-sm\",\n                                            children: \"Remember me\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/forgot-password\",\n                                        className: \"text-[#40E0D0] font-medium ml-1 cursor-pointer\",\n                                        prefetch: false,\n                                        onClick: onClose,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[12px] sm:text-sm\",\n                                            children: \" Forgot Passsword?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: `w-full sm:my-3 my-1 sm:text-base text-sm font-semibold text-white sm:py-4 py-3 rounded-4xl transition duration-200 ${isDisabled ? \"bg-gray-400 cursor-not-allowed\" : \"bg-[#40E0D0] hover:bg-teal-500\"}`,\n                                disabled: isDisabled,\n                                children: loading ? \"Signing in...\" : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 text-center text-red-600\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mx-8 space-x-4 sm:my-8 my-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 border-t border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600 sm:text-base text-sm\",\n                                children: \"Or\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 border-t border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center sm:gap-x-4 gap-x-2 justify-center sm:mb-6 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_9__.FacebookProvider, {\n                                appId: \"3141101952691007\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_facebook__WEBPACK_IMPORTED_MODULE_9__.LoginButton, {\n                                    scope: \"public_profile,email\",\n                                    onSuccess: handleFacebookSuccess,\n                                    onError: handleFacebookFailure,\n                                    className: \"min-w-0 p-0 sm:text-3xl text-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaFacebook, {\n                                        className: \"text-[#0866ff]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_oauth_google__WEBPACK_IMPORTED_MODULE_12__.GoogleOAuthProvider, {\n                                clientId: \"179422288655-1cm80o7s30fk0p1esfh3an4s15duj2u9.apps.googleusercontent.com\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_socialLogin_googleSocialLogin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    role: \"user\",\n                                    closeLoginPopup: onClose\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_apple_login__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                clientId: \"com.mixdorm.app\",\n                                redirectURI: \"https://mixdorm.com\",\n                                responseType: \"code id_token\",\n                                scope: \"name email\",\n                                render: (renderProps)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Checkbox_FormControlLabel_mui_material__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                                        className: \"min-w-0 p-0 text-3xl text-black mb-1\",\n                                        onClick: renderProps.onClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaApple_FaFacebook_react_icons_fa__WEBPACK_IMPORTED_MODULE_19__.FaApple, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, void 0),\n                                onSuccess: handleAppleSuccess,\n                                onError: handleAppleFailure\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-basec text-center text-sm text-gray-400\",\n                        children: [\n                            \"Don't have an account?\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                className: \"text-[#40E0D0] font-medium ml-1 cursor-pointer\",\n                                onClick: openSignUpForm,\n                                children: \"Sign Up\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\popup\\\\signinPopup.jsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SignInPopup);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/popup/signinPopup.jsx\n");

/***/ }),

/***/ "./components/socialLogin/googleSocialLogin.jsx":
/*!******************************************************!*\
  !*** ./components/socialLogin/googleSocialLogin.jsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-oauth/google */ \"@react-oauth/google\");\n/* harmony import */ var _react_oauth_google__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/api */ \"./utils/api.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_FcGoogle_react_icons_fc__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=FcGoogle!=!react-icons/fc */ \"__barrel_optimize__?names=FcGoogle!=!./node_modules/react-icons/fc/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@mui/material */ \"__barrel_optimize__?names=Button!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/firebaseConfig */ \"./utils/firebaseConfig.jsx\");\n/* harmony import */ var _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/ownerflowServices */ \"./services/ownerflowServices.jsx\");\n/* harmony import */ var _home_navbarContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"./components/home/<USER>");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_6__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__]);\n([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_6__, _utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__, _services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst GoogleSocialLogin = ({ role, closeLoginPopup })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { updateUserStatus, updateUserRole } = (0,_home_navbarContext__WEBPACK_IMPORTED_MODULE_10__.useNavbar)();\n    const fetchGoogleUserProfile = async (access_token)=>{\n        try {\n            const res = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('https://www.googleapis.com/oauth2/v3/userinfo', {\n                headers: {\n                    Authorization: `Bearer ${access_token}`\n                }\n            });\n            return res.data;\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to fetch user profile: \" + error.message);\n            throw error;\n        }\n    };\n    const handleGoogleSuccess = async (tokenResponse)=>{\n        const { access_token } = tokenResponse;\n        try {\n            const userProfile = await fetchGoogleUserProfile(access_token);\n            const { name, email } = userProfile;\n            const [firstName, lastName] = name.split(' ');\n            const res = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${_utils_api__WEBPACK_IMPORTED_MODULE_5__.BASE_URL}/auth/social/login`, {\n                token: access_token,\n                role: role,\n                name: {\n                    first: firstName,\n                    last: lastName || ''\n                },\n                email: email\n            });\n            if (res.status === 201 && res.data.status) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Login successful!\");\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"name\", `${res?.data?.data?.user?.name?.first} ${res?.data?.data?.user?.name?.last}`);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"role\", res?.data?.data?.user?.role);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"email\", res?.data?.data?.user?.email);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"contact\", res?.data?.data?.user?.contact);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setToken)(res?.data?.data?.token);\n                (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"id\", res?.data?.data?.user?._id);\n                updateUserStatus(res?.data?.data?.token);\n                updateUserRole(res?.data?.data?.user?.role);\n                const fcmToken = await (0,_utils_firebaseConfig__WEBPACK_IMPORTED_MODULE_8__.requestForFCMToken)();\n                if (fcmToken) {\n                    (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.setItemLocalStorage)(\"FCT\", fcmToken);\n                    await (0,_services_ownerflowServices__WEBPACK_IMPORTED_MODULE_9__.saveFirebaseToken)({\n                        token: fcmToken,\n                        userId: res?.data?.data?.user?._id\n                    });\n                }\n                if (role === \"hostel_owner\") {\n                    router.push(\"/owner/list\");\n                } else {\n                    closeLoginPopup();\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Social login failed: \" + res.data.message);\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(error.response?.data.message || error.message);\n        }\n    };\n    const login = (0,_react_oauth_google__WEBPACK_IMPORTED_MODULE_2__.useGoogleLogin)({\n        onSuccess: handleGoogleSuccess,\n        onError: {\n            \"GoogleSocialLogin.useGoogleLogin[login]\": (error)=>react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Google login failed: \" + error)\n        }[\"GoogleSocialLogin.useGoogleLogin[login]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_11__.Button, {\n        className: \"min-w-0 p-0 text-3xl\",\n        onClick: ()=>login(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FcGoogle_react_icons_fc__WEBPACK_IMPORTED_MODULE_12__.FcGoogle, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\socialLogin\\\\googleSocialLogin.jsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\socialLogin\\\\googleSocialLogin.jsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GoogleSocialLogin);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/socialLogin/googleSocialLogin.jsx\n");

/***/ }),

/***/ "./utils/firebaseConfig.jsx":
/*!**********************************!*\
  !*** ./utils/firebaseConfig.jsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requestForFCMToken: () => (/* binding */ requestForFCMToken)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"firebase/app\");\n/* harmony import */ var firebase_messaging__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/messaging */ \"firebase/messaging\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_messaging__WEBPACK_IMPORTED_MODULE_1__]);\n([firebase_app__WEBPACK_IMPORTED_MODULE_0__, firebase_messaging__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// firebaseClient.js\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDL2ms8HU3bt4gn_bpTN6Z5v9umH94iZ_4\",\n    authDomain: \"mixdorm-c5c35.firebaseapp.com\",\n    projectId: \"mixdorm-c5c35\",\n    storageBucket: \"mixdorm-c5c35.appspot.com\",\n    messagingSenderId: \"789621005244\",\n    appId: \"1:789621005244:web:cf662eaedbfd773330a9b9\",\n    measurementId: \"G-Z6LTN1ZD1X\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\nconst requestForFCMToken = async ()=>{\n    if (false) {} else {\n        console.log(\"Firebase messaging is not supported or this is not running in the browser.\");\n        return null;\n    }\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/firebaseConfig.jsx\n");

/***/ }),

/***/ "__barrel_optimize__?names=Button!=!./node_modules/@mui/material/node/index.js":
/*!*************************************************************************************!*\
  !*** __barrel_optimize__?names=Button!=!./node_modules/@mui/material/node/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!./node_modules/@mui/material/node/index.js":
/*!***************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Button,Checkbox,FormControlLabel!=!./node_modules/@mui/material/node/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs":
/*!**********************************************************************************************!*\
  !*** __barrel_optimize__?names=FaApple,FaFacebook!=!./node_modules/react-icons/fa/index.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FcGoogle!=!./node_modules/react-icons/fc/index.mjs":
/*!************************************************************************************!*\
  !*** __barrel_optimize__?names=FcGoogle!=!./node_modules/react-icons/fc/index.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fc_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fc/index.mjs */ "./node_modules/react-icons/fc/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fc_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fc_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;