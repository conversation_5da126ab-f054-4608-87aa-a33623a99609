"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_model_noticeboard_jsx";
exports.ids = ["components_model_noticeboard_jsx"];
exports.modules = {

/***/ "./components/model/noticeBoardDetail.jsx":
/*!************************************************!*\
  !*** ./components/model/noticeBoardDetail.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"__barrel_optimize__?names=ChevronRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Backdrop,Fade,Modal!=!@mui/material */ \"__barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _services_webflowServices__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/webflowServices */ \"./services/webflowServices.jsx\");\n/* harmony import */ var _utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/browserSetting */ \"./utils/browserSetting.jsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_FaCaretUp_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaCaretUp!=!react-icons/fa */ \"__barrel_optimize__?names=FaCaretUp!=!./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// import { BiSolidHeartCircle } from \"react-icons/bi\";\n// import { MdOutlineChat } from \"react-icons/md\";\n// import { FaCalendarDays } from \"react-icons/fa6\";\n// import { PiMapPinLineFill } from \"react-icons/pi\";\n// import { IoFilterSharp } from \"react-icons/io5\";\n\n// import zIndex from \"@mui/material/styles/zIndex\";\n// const notices = [\n//   {\n//     title: \"Notice 1\",\n//     text: \"Mad monkey updated new roof top cafe with edm vibes 5 min ago\",\n//   },\n//   {\n//     title: \"Notice 2\",\n//     text: \"Mad monkey updated new roof top cafe with edm vibes 5 min ago\",\n//   },\n//   {\n//     title: \"Notice 3\",\n//     text: \"Mad monkey updated new roof top cafe with edm vibes 5 min ago\",\n//   },\n// ];\nconst NoticeBoardDetail = ({ open, close })=>{\n    const style = {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"70%\",\n        transform: \"translate(-50%, -50%)\",\n        width: \"100%\",\n        bgcolor: \"background.paper\",\n        boxShadow: 24,\n        borderRadius: \"20px\"\n    };\n    // eslint-disable-next-line no-unused-vars\n    const [age, setAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // eslint-disable-next-line no-unused-vars\n    const [notices, setNotices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // eslint-disable-next-line no-unused-vars\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isFirstRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const handleOpenDetail = ()=>{\n        router.push(\"/noticeboard-detail\");\n        close(); // Close the main modal\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoticeBoardDetail.useEffect\": ()=>{\n            const fetchNotices = {\n                \"NoticeBoardDetail.useEffect.fetchNotices\": async ()=>{\n                    setLoading(true);\n                    try {\n                        const response = await (0,_services_webflowServices__WEBPACK_IMPORTED_MODULE_3__.getNoticeApi)();\n                        setNotices(response?.data?.data);\n                    } catch (error) {\n                        console.error(\"Error fetching payment data:\", error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"NoticeBoardDetail.useEffect.fetchNotices\"];\n            if (!isFirstRender.current && (0,_utils_browserSetting__WEBPACK_IMPORTED_MODULE_4__.getItemLocalStorage)(\"token\") && open) {\n                fetchNotices();\n            } else if (open) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Please Login !!!\");\n                close();\n            } else {\n                isFirstRender.current = false;\n            }\n        }\n    }[\"NoticeBoardDetail.useEffect\"], [\n        open\n    ]);\n    // eslint-disable-next-line no-unused-vars\n    const handleChange = (event)=>{\n        setAge(event.target.value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n            \"aria-labelledby\": \"transition-modal-title\",\n            \"aria-describedby\": \"transition-modal-description\",\n            open: open,\n            onClose: close,\n            closeAfterTransition: true,\n            slots: {\n                backdrop: _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__.Backdrop\n            },\n            slotProps: {\n                backdrop: {\n                    timeout: 500\n                }\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_7__.Fade, {\n                in: open,\n                sx: style,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl max-w-[450px] md:w-auto xs:w-[320px] w-[280px] lg:end-[10%] end-[5%] lg:mt-28 mt-24 float-right relative z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCaretUp_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaCaretUp, {\n                                    className: \"xs:top-[-25px] top-[-22px] absolute lg:start-[40%] md:start-[70%] xs:start-[60%] start-[52%]\",\n                                    size: 40,\n                                    color: \"#40E0D0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center pe-4 border-b bg-[#40E0D0] rounded-t-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"sm:text-[27px] text-xl leading-none rounded-t-2xl text-center font-bold text-black p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-[#fff]\",\n                                                    children: \"Notice\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"board\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleOpenDetail,\n                                            className: \"bg-[#fff] xs:text-sm text-xs font-semibold rounded-lg border border-white hover:border-black transition-all flex items-center justify-center py-1 ps-2 pe-1\",\n                                            children: [\n                                                \"View All \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_9__.ChevronRight, {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 28\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"xs:pb-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-around\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Mate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-semibold text-gray-400 text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Priyanka\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- You got your first like!\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-evenly\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Event\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Mad Monkey\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- Created New Event - Club Dance\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Creator\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Mad Monkey - Mix\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-[#56edde]\",\n                                                                            children: \"Creator \"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Sent offer request to Ayush Jain\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-around\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Mate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-semibold text-gray-400 text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Priyanka\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- You got your first like!\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col justify-evenly\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/Profile1.png`,\n                                                                width: 37,\n                                                                height: 37,\n                                                                alt: \"Arlene McCoy\",\n                                                                className: \"min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md\",\n                                                                    children: \"Mix Event\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-black text-xs xs:font-bold font-semibold\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                \"Mad Monkey\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \" \",\n                                                                        \"- Created New Event - Club Dance\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sm:text-sm text-xs text-gray-400 leading-none\",\n                                                children: \"5m\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeBoardDetail.jsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoticeBoardDetail);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/model/noticeBoardDetail.jsx\n");

/***/ }),

/***/ "./components/model/noticeboard.jsx":
/*!******************************************!*\
  !*** ./components/model/noticeboard.jsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Backdrop,Fade,Modal!=!@mui/material */ \"__barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js\");\n/* harmony import */ var _noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./noticeBoardDetail */ \"./components/model/noticeBoardDetail.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([_noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst Noticeboard = ({ close, open, openNoticeBoardDetails })=>{\n    const style = {\n        position: \"fixed\",\n        top: \"50%\",\n        left: \"50%\",\n        transform: \"translate(-50%, -50%)\",\n        maxWidth: 384,\n        bgcolor: \"background.paper\",\n        boxShadow: 24,\n        borderRadius: \"20px\",\n        p: 4\n    };\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [openBoardDetail, setOpenBoardDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenBoardDetail = ()=>{\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"Please Do Atleast One Booking First.\");\n        router.push(\"/\");\n        close(); // Close the main modal\n    };\n    const handleCloseBoardDetail = ()=>{\n        setOpenBoardDetail(false);\n        close(); // Close the main modal when nested modal closes\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Noticeboard.useEffect\": ()=>{\n            if (openNoticeBoardDetails) {\n                setOpenBoardDetail(true);\n            }\n        }\n    }[\"Noticeboard.useEffect\"], [\n        openNoticeBoardDetails\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                open: open,\n                onClose: close,\n                \"aria-labelledby\": \"child-modal-title\",\n                \"aria-describedby\": \"child-modal-description\",\n                closeAfterTransition: true,\n                slots: {\n                    backdrop: _barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__.Backdrop\n                },\n                slotProps: {\n                    backdrop: {\n                        timeout: 500\n                    }\n                },\n                className: \"bg-black/70\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Backdrop_Fade_Modal_mui_material__WEBPACK_IMPORTED_MODULE_6__.Fade, {\n                    in: open,\n                    sx: style,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl max-w-[384px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-[27px] leading-none text-center font-bold text-gray-800 border-b p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-blue\",\n                                        children: \"Notice\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"board\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center pt-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        src: `${\"https://mixdorm-live.s3.ap-south-1.amazonaws.com\"}/front-images/noticeboard_img.png`,\n                                        alt: \"Noticeboard Image\",\n                                        width: 245,\n                                        height: 151,\n                                        className: \"max-w-full h-auto mb-6 block mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-center text-[#888888] mb-6\",\n                                        children: \"Please Book Your Stay To Visit Noticeboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOpenBoardDetail,\n                                        className: \"bg-[#40E0D0] text-sm font-semibold w-full py-4 rounded-3xl hover:bg-sky-blue-750 hover:text-white transition-all\",\n                                        children: \"Go To Booking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_noticeBoardDetail__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                open: openBoardDetail,\n                close: handleCloseBoardDetail\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Mixdorm\\\\Mixdorm-Web-2.0\\\\frontend\\\\components\\\\model\\\\noticeboard.jsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Noticeboard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/model/noticeboard.jsx\n");

/***/ }),

/***/ "__barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js":
/*!**************************************************************************************************!*\
  !*** __barrel_optimize__?names=Backdrop,Fade,Modal!=!./node_modules/@mui/material/node/index.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@mui/material/node/index.js */ "./node_modules/@mui/material/node/index.js");
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_mui_material_node_index_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=ChevronRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js":
/*!*****************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronRight!=!./node_modules/lucide-react/dist/cjs/lucide-react.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/lucide-react/dist/cjs/lucide-react.js */ "./node_modules/lucide-react/dist/cjs/lucide-react.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_lucide_react_dist_cjs_lucide_react_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FaCaretUp!=!./node_modules/react-icons/fa/index.mjs":
/*!*************************************************************************************!*\
  !*** __barrel_optimize__?names=FaCaretUp!=!./node_modules/react-icons/fa/index.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fa/index.mjs */ "./node_modules/react-icons/fa/index.mjs");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => C_Users_POOJA_Mixdorm_Mixdorm_Web_2_0_frontend_node_modules_react_icons_fa_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ })

};
;